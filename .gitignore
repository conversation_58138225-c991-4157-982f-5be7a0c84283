# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

#dev
/public/embed.js
/public/embed.html
/public/embed.js.map
.parcel-cache

# production
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
.dev.vars

npm-debug.log*
yarn-debug.log*
yarn-error.log*

.idea/
/test-results/
/playwright-report/
/playwright/.cache/

.wrangler/
.npmrc
