import { AsyncMethodReturns, connectToChild } from "penpal";
import {
    RollbarSettings,
    rollbarMessage
} from "../functions/internal/helpers/rollbar";
import {
    CatalogDetailsResponse,
    CollectionDetailsResponse
} from "@switcherstudio/player-api-client";
import { Modals } from "../src/store/Modals/types";
import {
    ICreatorCustomerTicket,
    PasswordClaim
} from "../src/store/CreatorCustomers/types";

declare global {
    interface Window {
        switcherPlayerApp: any;
        Shopify: any;
    }
}

interface SwitcherPlayerCollection {
    mainDialog: HTMLDialogElement;
    mainIframe: HTMLIFrameElement;
    auxDialog: HTMLDialogElement;
    auxIframe: HTMLIFrameElement;
    mainConnection?: AsyncMethodReturns<ChildFrame>;
    auxConnection?: AsyncMethodReturns<ChildFrame>;
}

interface ChildFrame {
    setDoNotTrack: (doNotTrack: boolean) => void;
    setCatalogDetails: (params: CatalogDetailsResponse) => void;
    setCollectionDetails: (params: CollectionDetailsResponse) => void;
    loadPlayer: (
        catalogId: string,
        collectionId: string,
        upcomingPreselectedPageNumber: number | null,
        onDemandPreselectedPageNumber: number | null,
        preselectedBroadcastId: string,
        autoPlay: boolean
    ) => void;
    loadPurchaseModal: (
        type: Modals,
        playerId: string,
        playlistBroadcastId: string
    ) => void;
    setAuthorization: (ticket: ICreatorCustomerTicket) => void;
    setPasswordClaims: (
        projectId: string,
        passwordClaims: PasswordClaim[]
    ) => void;
}

const rollbarSettings = {
    environment: process.env.VITE_ENV || "local",
    version: process.env.VITE_VERSION || "1.0",
    clientKey: process.env.VITE_ROLLBAR_SERVERKEY
} as RollbarSettings;

class SwitcherPlayerApp {
    playerCollection: Record<string, SwitcherPlayerCollection[]>;
    private static instanceCounter: number = 0;
    /**
     * A promise that resolves when the initialization of the SwitcherPlayerApp is complete.
     * This is used to track the readiness state of the application.
     */
    private readyPromise: Promise<boolean>;

    /**
     * A function to resolve the `readyPromise` when the initialization is complete.
     */
    private readyResolver: (value: boolean) => void;

    constructor() {
        this.playerCollection = {};
        // Initialize the ready promise
        this.readyPromise = new Promise<boolean>((resolve) => {
            this.readyResolver = resolve;
        });
    }

    async init() {
        // Get a fresh collection of containers each time init is called
        // This ensures we see all containers, even those added after initial page load
        const initcontainers = document.getElementsByClassName(
            "dff402f7-5be0-4890-b831-95c5b63ddb42"
        );

        const initializationPromises: Promise<void>[] = [];

        for (let i = 0; i < initcontainers.length; i++) {
            const container = initcontainers[i] as HTMLElement;

            // init can be called multiple times, so we need to check if the container is already initialized
            if (
                container.dataset.isPlayerInitialized === "true" ||
                container.hasAttribute("data-player-initialized") ||
                container.querySelector("dialog") !== null
            ) {
                continue;
            }

            // Mark as initialized with multiple flags for redundancy
            container.dataset.isPlayerInitialized = "true";
            container.setAttribute("data-player-initialized", "true");
            container.dataset.playerInstanceId = `player-${SwitcherPlayerApp.instanceCounter++}`;

            // query params from the url or container div
            const params = this.getParams(container);
            const { hostName, path } = params;

            const iframeUrl = new URL(hostName);
            iframeUrl.pathname = path;

            // create catalog elements
            const mainDialog = document.createElement("dialog");
            mainDialog.style.display = "block";
            mainDialog.style.boxSizing = "border-box";
            mainDialog.style.border = "none";
            mainDialog.style.padding = "0";
            mainDialog.style.margin = "0";
            mainDialog.style.width = "100%";
            mainDialog.style.maxWidth = "100%";
            mainDialog.style.position = "relative";

            if (container.dataset.microsite) {
                mainDialog.style.height = "100%";
            }

            const mainIframe = document.createElement("iframe");
            // Permissions required for the iframe:
            // - autoplay: Allows media to play automatically without user interaction.
            // - fullscreen: Enables the iframe to enter fullscreen mode.
            // - picture-in-picture: Allows videos to be played in a floating window.
            // - clipboard-write: Enables the iframe to write to the clipboard (e.g., for copy-paste functionality).
            // - payment: Allows the iframe to initiate payment requests (e.g., for embedded payment gateways).
            mainIframe.allow =
                "autoplay; fullscreen; picture-in-picture; clipboard-write; payment";
            mainIframe.frameBorder = "0";
            mainIframe.scrolling = "auto";
            mainIframe.style.width = "100%";
            mainIframe.style.height = "100%";
            mainIframe.style.padding = "0";
            mainIframe.style.margin = "0 0 -5px 0";

            this.setIframeSrc(mainIframe, iframeUrl, {
                ...params,
                iframeType: "main"
            });

            // create player elements
            const auxDialog = document.createElement("dialog");
            auxDialog.style.display = "none";
            auxDialog.style.position = "fixed";
            auxDialog.style.padding = "0";
            auxDialog.style.margin = "0";
            auxDialog.style.width = "100%";
            auxDialog.style.height = "100%";
            auxDialog.style.maxWidth = "100%";
            auxDialog.style.maxHeight = "100%";
            auxDialog.style.top = "0px";
            auxDialog.style.left = "0px";
            auxDialog.style.zIndex = "9999";
            auxDialog.style.boxSizing = "border-box";
            auxDialog.style.border = "none";
            auxDialog.style.background = "none";

            const auxIframe = document.createElement("iframe");
            auxIframe.allow =
                "autoplay; fullscreen; picture-in-picture; clipboard-write; payment";
            auxIframe.frameBorder = "0";
            auxIframe.scrolling = "auto";
            auxIframe.style.position = "absolute";
            auxIframe.style.top = "0";
            auxIframe.style.left = "0";
            auxIframe.style.width = "100%";
            auxIframe.style.height = "100%";
            // load iframe url intially to handle auth flow
            this.setIframeSrc(auxIframe, iframeUrl, {
                ...params,
                iframeType: "aux-player",
                // clear embed variables
                p: null,
                c: null
            });

            // append elements
            mainDialog.appendChild(mainIframe);
            container.appendChild(mainDialog);
            auxDialog.appendChild(auxIframe);
            container.appendChild(auxDialog);

            // how to reference this instance of the embed
            const collectionKey = params.c ?? params.p;

            // add to player collection
            if (!this.playerCollection[collectionKey]) {
                this.playerCollection[collectionKey] = [];
            }

            const playerInstance = {
                mainDialog: mainDialog,
                mainIframe: mainIframe,
                auxDialog: auxDialog,
                auxIframe: auxIframe
            };

            this.playerCollection[collectionKey].push(playerInstance);

            // Get the index of this instance in the collection
            const instanceIndex =
                this.playerCollection[collectionKey].length - 1;

            // prep for child connection setups
            const maxRetries = 3;

            // Create promises for aux and main connections with retry logic
            const auxInitPromise = new Promise<void>((resolve, reject) => {
                let retryCount = 0;

                const attemptAuxInit = () => {
                    this.makeAuxEventListeners(
                        auxDialog,
                        auxIframe,
                        instanceIndex,
                        collectionKey
                    )
                        .then((auxConnection) => {
                            this.playerCollection[collectionKey][
                                instanceIndex
                            ].auxConnection = auxConnection;
                            resolve();
                        })
                        .catch((error) => {
                            console.error(
                                "Failed to make aux event listeners:",
                                error
                            );
                            if (retryCount < maxRetries) {
                                retryCount++;
                                console.log(
                                    `Attempting to re-initialize aux event listeners (attempt ${retryCount})...`
                                );
                                setTimeout(attemptAuxInit, 1000);
                            } else {
                                const errorMessage =
                                    "Max retries reached for aux event listeners.";
                                console.error(errorMessage);
                                rollbarMessage(rollbarSettings, errorMessage, {
                                    error
                                });
                                reject(error);
                            }
                        });
                };

                attemptAuxInit();
            });

            const mainInitPromise = new Promise<void>((resolve, reject) => {
                let retryCount = 0;

                const attemptMainInit = () => {
                    this.makeMainEventListeners(
                        collectionKey,
                        mainDialog,
                        mainIframe,
                        auxDialog,
                        instanceIndex
                    )
                        .then((mainConnection) => {
                            this.playerCollection[collectionKey][
                                instanceIndex
                            ].mainConnection = mainConnection;
                            resolve();
                        })
                        .catch((error) => {
                            console.error(
                                "Failed to make main event listeners:",
                                error
                            );
                            if (retryCount < maxRetries) {
                                retryCount++;
                                console.log(
                                    `Attempting to re-initialize main event listeners (attempt ${retryCount})...`
                                );
                                setTimeout(attemptMainInit, 1000);
                            } else {
                                const errorMessage =
                                    "Max retries reached for main event listeners.";
                                console.error(errorMessage);
                                rollbarMessage(rollbarSettings, errorMessage, {
                                    error
                                });
                                reject(error);
                            }
                        });
                };

                attemptMainInit();
            });

            // Add both connection promises to our array
            initializationPromises.push(auxInitPromise);
            initializationPromises.push(mainInitPromise);
        }

        // Wait for all initialization promises to settle (both success and failure)
        await Promise.allSettled(initializationPromises);

        const results = await Promise.allSettled(initializationPromises);

        // Check if any promise was rejected
        const hasRejections = results.some(
            (result) => result.status === "rejected"
        );
        if (hasRejections) {
            const errors = results
                .filter((result) => result.status === "rejected")
                .map((result) => (result as PromiseRejectedResult).reason);
            console.error("Initialization failed with errors:", errors);
            rollbarMessage(rollbarSettings, "Initialization failed", {
                errors
            });

            // Dispatch an event to indicate that the app has been initialized
            dispatchEvent(new Event("switcherPlayerAppLoadFailure"));

            return false;
        }

        // Dispatch an event to indicate that the app has been initialized
        dispatchEvent(new Event("switcherPlayerAppLoaded"));

        // Resolve the ready promise to indicate that initialization is complete
        this.readyResolver(true);

        return true;
    }

    getParams(container?: HTMLElement): Record<string, any> {
        let params = {
            hostName: container?.dataset?.hostname ?? null,
            path: container?.dataset?.path ?? null,
            loc: container?.dataset?.location ?? null,
            ajs_uid: container?.dataset?.userid ?? null,
            projectId: container?.dataset?.projectid ?? null,
            b: container?.dataset?.broadcastid ?? null,
            p: container?.dataset?.videoplayerid ?? null,
            c: container?.dataset?.catalogid ?? null,
            doNotTrack: !!window.Shopify?.customerPrivacy
                ? !window.Shopify.customerPrivacy.userCanBeTracked()
                : false,
            referrerUrl: window.location.href,
            microsite: container?.dataset?.microsite ?? false
        } as any;

        // Add search params from the URL if they exist
        // Format: [searchParamKey, paramsObjectKey] or just the key if they're the same
        const possibleQueryParams: (string | [string, string])[] = [
            ["switcher-token", "token"],
            "autoplay",
            "switcher-pcid",
            "switcher-pbid"
        ];
        const searchParams = new URLSearchParams(window.location.search);
        possibleQueryParams.forEach((param) => {
            let searchKey: string;
            let paramKey: string;

            if (Array.isArray(param)) {
                [searchKey, paramKey] = param;
            } else {
                searchKey = paramKey = param;
            }

            const value = searchParams.get(searchKey);
            if (value !== null) {
                params[paramKey] = value;
            }
        });

        return params;
    }

    appendSearchParams(params: Record<string, any>, url: URL): URL {
        const newUrl = new URL(url.toString());
        const urlParams = new URLSearchParams(newUrl.search);
        Object.entries(params)
            .filter(([, v]) => v !== null && v !== undefined) // filter null and undefined params
            .forEach(([k, v]) => {
                urlParams.append(k, v);
            });
        newUrl.search = urlParams.toString();
        return newUrl;
    }

    setIframeSrc(
        ifrm: HTMLIFrameElement,
        iframeUrl: URL,
        params: Record<string, any>
    ) {
        // create local copy of iframe url
        let localUrl = new URL(iframeUrl);

        // If token is present, we must load the authorize view in the embed with the appropriate data.
        if (!!params.token && params.iframeType === "main") {
            let successUrl = new URL(iframeUrl);
            successUrl = this.appendSearchParams(
                {
                    ...params,
                    token: null
                },
                successUrl
            );
            localUrl.pathname = "/authorize";
            let authParams = {
                token: params.token,
                final_url: successUrl.href
            };
            localUrl = this.appendSearchParams(authParams, localUrl);
        } else {
            if (params) {
                localUrl = this.appendSearchParams(params, localUrl);
            }
        }

        ifrm.setAttribute("src", localUrl.href);
    }

    resize(iframe: HTMLIFrameElement, height?: number) {
        if (height) {
            iframe.style.height = `${height}px`;
        } else {
            iframe.style.height = "100%";
        }
    }

    distributeAuthorization(ticket: ICreatorCustomerTicket) {
        // loop through player collections
        Object.keys(this.playerCollection).forEach((collectionKey) => {
            // Process each instance of this collection key
            this.playerCollection[collectionKey].forEach((playerInstance) => {
                // bail out if the connections are not set
                if (
                    !playerInstance.mainConnection ||
                    !playerInstance.auxConnection
                )
                    return;

                playerInstance.mainConnection.setAuthorization(ticket);
                playerInstance.auxConnection.setAuthorization(ticket);
            });
        });
    }

    distributePasswordClaims(
        projectId: string,
        passwordClaims: PasswordClaim[]
    ) {
        // loop through player collections
        Object.keys(this.playerCollection).forEach((collectionKey) => {
            // Process each instance of this collection key
            this.playerCollection[collectionKey].forEach((playerInstance) => {
                // bail out if the connections are not set
                if (
                    !playerInstance.mainConnection ||
                    !playerInstance.auxConnection
                )
                    return;

                playerInstance.mainConnection.setPasswordClaims(
                    projectId,
                    passwordClaims
                );
                playerInstance.auxConnection.setPasswordClaims(
                    projectId,
                    passwordClaims
                );
            });
        });
    }

    private showModal(dialog: HTMLDialogElement, isAux: boolean = false) {
        if (typeof dialog.showModal === "function") dialog.showModal();
        dialog.style.position = "fixed";
        dialog.style.top = "0px";
        dialog.style.left = "0px";
        dialog.style.zIndex = "9999";
        if (isAux) {
            dialog.style.display = "block";
        }
        dispatchEvent(new Event("switcherPlayerDialogOpened"));
    }
    private closeModal(dialog: HTMLDialogElement, isAux: boolean = false) {
        if (typeof dialog.close === "function") dialog.close();
        dialog.style.position = "relative";
        dialog.style.top = "initial";
        dialog.style.left = "initial";
        dialog.style.zIndex = "initial";
        if (isAux) {
            dialog.style.display = "none";
        }
        dispatchEvent(new Event("switcherPlayerDialogClosed"));
    }

    async makeMainEventListeners(
        collectionKey: string,
        mainDialog: HTMLDialogElement,
        mainIframe: HTMLIFrameElement,
        auxDialog: HTMLDialogElement,
        instanceIndex: number
    ) {
        // eslint-disable-next-line @typescript-eslint/no-this-alias
        const _this = this;
        const connection = connectToChild<ChildFrame>({
            iframe: mainIframe,
            methods: {
                redirect(redirectUrl: string) {
                    const sanitizedRedirect = decodeURIComponent(
                        decodeURI(redirectUrl)
                    );
                    const url = new URL(sanitizedRedirect);
                    window.location.href = url.href;
                },
                /**
                 * Catalog Id is a required parameter - if it's not available, use an empty string
                 */
                openPlayer(
                    catalogId: string,
                    collectionId: string,
                    upcomingPreselectedPageNumber: number | null,
                    onDemandPreselectedPageNumber: number | null,
                    preselectedBroadcastId: string,
                    autoPlay: boolean = false
                ) {
                    _this.playerCollection[collectionKey][
                        instanceIndex
                    ].auxConnection?.loadPlayer(
                        catalogId,
                        collectionId,
                        upcomingPreselectedPageNumber,
                        onDemandPreselectedPageNumber,
                        preselectedBroadcastId,
                        autoPlay
                    );

                    _this.showModal(auxDialog, true);
                },
                openPurchaseModal(
                    type: Modals,
                    collectionId: string,
                    playerId: string
                ) {
                    _this.playerCollection[collectionKey][
                        instanceIndex
                    ].auxConnection?.loadPurchaseModal(
                        type,
                        collectionId,
                        playerId
                    );
                    _this.showModal(auxDialog, true);
                },
                expand() {
                    mainDialog.style.position = "fixed";
                    mainDialog.style.height = "100%";
                    mainDialog.style.aspectRatio = "initial";
                    mainDialog.style.maxWidth = "100%";
                    _this.showModal(mainDialog);
                },
                collapse(aspectRatio, maxWidth) {
                    mainDialog.style.position = "relative";
                    mainDialog.style.height = "initial";
                    mainDialog.style.aspectRatio =
                        aspectRatio === "NineBySixteen" ? "9/16" : "16/9";
                    mainDialog.style.maxWidth = !!maxWidth
                        ? `${maxWidth}px`
                        : "100%";
                    _this.closeModal(mainDialog);
                },
                /** Before aspect ratio is set by child, the dialog is initialized but hidden */
                setAspectRatio(aspectRatio) {
                    if (!!aspectRatio) {
                        mainIframe.style.position = "absolute";
                        mainIframe.style.top = "0";
                        mainIframe.style.left = "0";
                        mainDialog.style.aspectRatio =
                            aspectRatio === "NineBySixteen" ? "9/16" : "16/9";
                    } else {
                        mainIframe.style.position = "initial";
                        mainIframe.style.top = "initial";
                        mainIframe.style.left = "initial";
                        mainDialog.style.aspectRatio = "initial";
                    }
                },
                resize(height?: number) {
                    _this.resize(mainIframe, height);
                },
                distributeAuthorization(ticket: ICreatorCustomerTicket) {
                    _this.distributeAuthorization(ticket);
                },
                distributePasswordClaims(
                    projectId: string,
                    passwordClaims: PasswordClaim[]
                ) {
                    _this.distributePasswordClaims(projectId, passwordClaims);
                },
                emitEvent(eventName: string, eventData: any) {
                    // wait for app initialization before emitting child events
                    _this.readyPromise.then(() => {
                        mainIframe.dispatchEvent(
                            new CustomEvent(eventName, { detail: eventData })
                        );
                    });
                },
                setDialogBackgroundColor(color: string) {
                    mainDialog.style.backgroundColor = color;
                },
                setMaxWidth(maxWidth: string) {
                    mainDialog.style.maxWidth = !!maxWidth
                        ? `${maxWidth}px`
                        : "100%";
                },
                setCatalogDetails(params: CatalogDetailsResponse) {
                    _this.playerCollection[collectionKey][
                        instanceIndex
                    ].auxConnection?.setCatalogDetails(params);
                },
                setCollectionDetails(params: CollectionDetailsResponse) {
                    _this.playerCollection[collectionKey][
                        instanceIndex
                    ].auxConnection?.setCollectionDetails(params);
                }
            }
        });

        connection.promise
            .then((mainConnection) => {
                // set base events
                document.addEventListener(
                    "trackingConsentAccepted",
                    function () {
                        mainConnection.setDoNotTrack(false);
                    }
                );
            })
            .catch((error) => {
                rollbarMessage(rollbarSettings, error, null);
            });

        return connection.promise;
    }

    async makeAuxEventListeners(
        auxDialog: HTMLDialogElement,
        auxIframe: HTMLIFrameElement,
        instanceIndex: number,
        collectionKey: string
    ) {
        // eslint-disable-next-line @typescript-eslint/no-this-alias
        const _this = this;
        const connection = connectToChild<ChildFrame>({
            iframe: auxIframe,
            methods: {
                redirect(redirectUrl: string) {
                    const sanitizedRedirect = decodeURIComponent(
                        decodeURI(redirectUrl)
                    );
                    const url = new URL(sanitizedRedirect);
                    window.location.href = url.href;
                },
                // DEVNOTE: This is only used for the browser navigation, and should NOT be leveraged outside of the usePlayerHistoryNavigation hook
                openPlayer(
                    catalogId: string,
                    collectionId: string,
                    upcomingPreselectedPageNumber: number | null,
                    onDemandPreselectedPageNumber: number | null,
                    preselectedBroadcastId: string,
                    autoPlay: boolean = false
                ) {
                    _this.playerCollection[collectionKey][
                        instanceIndex
                    ].auxConnection?.loadPlayer(
                        catalogId,
                        collectionId,
                        upcomingPreselectedPageNumber,
                        onDemandPreselectedPageNumber,
                        preselectedBroadcastId,
                        autoPlay
                    );

                    _this.showModal(auxDialog, true);
                },
                closePlayer() {
                    _this.closeModal(auxDialog, true);
                },
                closePurchaseModal() {
                    _this.closeModal(auxDialog, true);
                },
                distributeAuthorization(ticket: ICreatorCustomerTicket) {
                    _this.distributeAuthorization(ticket);
                },
                distributePasswordClaims(
                    projectId: string,
                    passwordClaims: PasswordClaim[]
                ) {
                    _this.distributePasswordClaims(projectId, passwordClaims);
                }
            }
        });

        connection.promise
            .then((auxConnection) => {
                // set base events
                document.addEventListener(
                    "trackingConsentAccepted",
                    function () {
                        auxConnection.setDoNotTrack(false);
                    }
                );
            })
            .catch((error) => {
                rollbarMessage(rollbarSettings, error, null);
            });

        return connection.promise;
    }
}

window.switcherPlayerApp = window.switcherPlayerApp ?? new SwitcherPlayerApp();
if (!!window.Shopify) {
    try {
        window.Shopify.loadFeatures(
            [
                {
                    name: "consent-tracking-api",
                    version: "0.1"
                }
            ],
            function (error: any) {
                if (error) {
                    throw error;
                }
            }
        );
    } catch (error) {
        rollbarMessage(rollbarSettings, error, null);
    } finally {
        window.switcherPlayerApp.init();
    }
} else {
    window.switcherPlayerApp.init();
}
