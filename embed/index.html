<html lang="en">
	<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width">
		<title>Test Page for iFrame</title>
    
		<script>
            !function () {
                var analytics = window.analytics = window.analytics || []; if (!analytics.initialize) if (analytics.invoked) window.console && console.error && console.error("Segment snippet included twice."); else {
                    analytics.invoked = !0; analytics.methods = ["trackSubmit", "trackClick", "trackLink", "trackForm", "pageview", "identify", "reset", "group", "track", "ready", "alias", "debug", "page", "once", "off", "on", "addSourceMiddleware"]; analytics.factory = function (t) { return function () { var e = Array.prototype.slice.call(arguments); e.unshift(t); analytics.push(e); return analytics } }; for (var t = 0; t < analytics.methods.length; t++) { var e = analytics.methods[t]; analytics[e] = analytics.factory(e) } analytics.load = function (t, e) { var n = document.createElement("script"); n.type = "text/javascript"; n.async = !0; n.src = "https://cdn.segment.com/analytics.js/v1/" + t + "/analytics.min.js"; var a = document.getElementsByTagName("script")[0]; a.parentNode.insertBefore(n, a); analytics._loadOptions = e }; analytics.SNIPPET_VERSION = "4.15.2";
                }
            }();
        </script>
    </head>
  <body>

    <!--
      NOTE: the embed.js file replaces the inner div below with the iframe

      in order to test different sizes you will need to wrap each inner div inside
      different wrapper divs with differing sizes and styles
    -->

    <!-- TO DO: replace data-projectid with an actual test project id-->
    <div style="width: 25%">
      <div
        class="dff402f7-5be0-4890-b831-95c5b63ddb42"
        data-hostname="http://localhost:3004"
        data-path="/embed"
        data-videoplayerid="52c30b10-91d6-4848-8c47-e4064da01246" 
        data-location="iframe"
      ></div>
    </div>

    <script src="http://localhost:3004/embed.js"></script>
  </body>
</html>
