name: Create Release Candidate

on:
  workflow_dispatch:
    inputs:
      version:
        description: "New Version"
        required: true

jobs:
  bump_version_on_develop:
    runs-on: ubuntu-latest
    name: Bump Version
    steps:
      - uses: actions/checkout@v4
      - name: Extract Branch Name
        shell: bash
        run: echo "branch=$(echo ${GITHUB_REF#refs/heads/})" >> $GITHUB_OUTPUT
        id: extract_branch

      - name: "Bump Version"
        run: |
          git config --global user.name '<PERSON>'
          git config --global user.email '<EMAIL>'
          npm config set tag-version-prefix ''
          npm version ${{ github.event.inputs.version }} -m "Bump version to v${{ github.event.inputs.version }} for release"
          git push --tags origin ${{ steps.extract_branch.outputs.branch }}

      - uses: actions/checkout@v4
        with:
          ref: main

      - name: Reset release branch
        run: |
          git fetch origin ${{ steps.extract_branch.outputs.branch }}:${{ steps.extract_branch.outputs.branch }}
          git reset --hard ${{ steps.extract_branch.outputs.branch }}

      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v3
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          title: Release ${{ github.event.inputs.version }}
          branch: rc-${{ github.event.inputs.version }}
          labels: Release Candidate
