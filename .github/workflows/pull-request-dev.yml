name: Pull Request Staging Deploy

on:
  pull_request:
    types: [opened, synchronize, reopened, closed]
    branches:
      - develop
      - feature/**
      - bug/**
      - chore/**
      - wip/**
      - rc-**

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build_and_deploy_job:
    if: github.event_name == 'push' || (github.event_name == 'pull_request' && github.event.action != 'closed')
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [20.x]
    name: Build and Deploy Job
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true

      - name: Setup npmrc
        run: |
          echo "@switcherstudio:registry=https://npm.pkg.github.com" > .npmrc
          echo "//npm.pkg.github.com/:_authToken=${{ secrets.PAT_FOR_PACKAGES }}" >> .npmrc

      - name: get-npm-version
        id: package-version
        uses: martinbeentjes/npm-get-version-action@master

      - name: "Create Version File"
        uses: "switcherstudio/create-version-file@master"
        with:
          environment: "dev"
          version: ${{ steps.package-version.outputs.current-version }}

      - name: Install
        run: yarn install

      - name: Build
        env:
          VITE_ENV: "dev"
          VITE_VERSION: ${{ steps.package-version.outputs.current-version }}
          VITE_API_URL: "https://api-develop.switcherstudio.com"
          VITE_PLAYER_API_URL: "https://vapi-develop.switcherstudio.com"
          VITE_SEGMENT_CDNKEY: ${{ secrets.SEGMENT_CDNKEY_DEV }}
          VITE_SEGMENT_WRITEKEY: ${{ secrets.SEGMENT_WRITEKEY_DEV }}
          VITE_STRIPE_PUBLISHABLE_KEY: ${{ secrets.STRIPE_PUBLISHABLE_KEY_DEV }}
          VITE_LCR_API_URL: "https://livecommentsfn-develop.azurewebsites.net"
          VITE_STRIPE_CHECKOUT_POLLING_INTERVAL: "1000"
          VITE_ENABLE_CONTROL_REDESIGN: true
          VITE_SHAKA_PLAYER_RECEIVER_ID: 14B5093F
          VITE_ENABLE_CATALOG_SEARCH: true

        run: yarn build

      - name: Deploy
        id: deploy
        uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CF_API_TOKEN }}
          accountId: ${{ secrets.CF_ACCOUNT_ID }}
          projectName: "embedded-video-player"
          directory: "build"
          gitHubToken: ${{ secrets.GITHUB_TOKEN }}
          branch: ${{ github.head_ref }}

      - name: Add URL
        uses: actions/github-script@v6
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: 'Cloudflare Pages: Your preview site is ready! Visit it here: <a href="${{ steps.deploy.outputs.url }}">${{ steps.deploy.outputs.url }}</a>'
            })
  test:
    runs-on: ubuntu-latest
    needs:
      - build_and_deploy_job
    if: success()
    steps:
      - run: echo "tests running not implimented"

  cleanup:
    runs-on: ubuntu-latest
    needs:
      - test
    if: always()
    steps:
      - name: Call Cleanup Endpoint
        uses: JamesIves/fetch-api-data-action@v2
        with:
          endpoint: https://api-develop.switcherstudio.com/api/Actions/Maintenance/PlayerTestingCleanup
          configuration: '{ "method": "POST", "headers": {"Github-Action-Secret": "${{ secrets.TEST_CLEANUP_ACTION_SECRET }}"} }'
