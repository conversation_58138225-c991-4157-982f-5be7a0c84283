name: Push to <PERSON><PERSON><PERSON>

on:
  push:
    branches:
      - develop

jobs:
  build_and_deploy_job:
    if: github.event_name == 'push' || (github.event_name == 'pull_request' && github.event.action != 'closed')
    runs-on: ubuntu-latest
    name: Build and Deploy Job
    strategy:
      max-parallel: 1
      matrix:
        node-version: [20.x]
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true

      - name: Setup npmrc
        run: |
          echo "@switcherstudio:registry=https://npm.pkg.github.com" > .npmrc
          echo "//npm.pkg.github.com/:_authToken=${{ secrets.PAT_FOR_PACKAGES }}" >> .npmrc

      - name: "Automated Version Bump"
        uses: "phips28/gh-action-bump-version@master"
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          minor-wording: "[minor],[MINOR]"
          major-wording: "[major],[MAJOR]"
          patch-wording: "[patch],[PATCH]"
          commit-message: "CI: bumps version to {{version}} for dev build"

      - name: get-npm-version
        id: package-version
        uses: martinbeentjes/npm-get-version-action@master

      - name: "Create Version File"
        uses: "switcherstudio/create-version-file@master"
        with:
          environment: "dev"
          version: ${{ steps.package-version.outputs.current-version }}

      - name: Install
        run: yarn install

      - name: Build
        env:
          VITE_ENV: "dev"
          VITE_VERSION: ${{ steps.package-version.outputs.current-version }}
          VITE_API_URL: "https://api-develop.switcherstudio.com"
          VITE_PLAYER_API_URL: "https://vapi-develop.switcherstudio.com"
          VITE_SEGMENT_CDNKEY: ${{ secrets.SEGMENT_CDNKEY_DEV }}
          VITE_SEGMENT_WRITEKEY: ${{ secrets.SEGMENT_WRITEKEY_DEV }}
          VITE_STRIPE_PUBLISHABLE_KEY: ${{ secrets.STRIPE_PUBLISHABLE_KEY_DEV }}
          VITE_LCR_API_URL: "https://livecommentsfn-develop.azurewebsites.net"
          VITE_STRIPE_CHECKOUT_POLLING_INTERVAL: "1000"
          VITE_ENABLE_CONTROL_REDESIGN: true
          VITE_SHAKA_PLAYER_RECEIVER_ID: 14B5093F
          VITE_ENABLE_CATALOG_SEARCH: true

        run: yarn build

      - name: Deploy
        id: deploy
        uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CF_API_TOKEN }}
          accountId: ${{ secrets.CF_ACCOUNT_ID }}
          projectName: "embedded-video-player"
          directory: "build"
          gitHubToken: ${{ secrets.GITHUB_TOKEN }}
          branch: "develop"

      - name: Purge cache
        uses: fjogeleit/http-request-action@v1
        with:
          url: ${{ format('https://api.cloudflare.com/client/v4/zones/{0}/purge_cache', secrets.CF_ZONE_ID)}}
          method: "POST"
          bearerToken: ${{ secrets.CF_PAGES_PUBLISH_TOKEN }}
          customHeaders: '{"Content-Type": "application/json"}'
          data: '{ "hosts": ["player-develop.switcherstudio.com"] }'

      - name: Notify deploy to Rollbar
        uses: rollbar/github-deploy-action@2.1.2
        id: rollbar_deploy
        with:
          environment: "dev"
          version: ${{ steps.package-version.outputs.current-version }}
          local_username: ${{ github.actor }}
        env:
          ROLLBAR_ACCESS_TOKEN: ${{ secrets.ROLLBAR_ACCESS_TOKEN }}
