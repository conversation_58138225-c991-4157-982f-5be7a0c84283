name: Manual Deploy by Tag (prod)

on:
  workflow_dispatch:
    inputs:
      tag:
        description: "Release Version (0.0.0)"
        required: true
jobs:
  build_and_deploy_job:
    runs-on: ubuntu-latest
    name: Build and Deploy Job
    strategy:
      max-parallel: 1
      matrix:
        node-version: [20.x]
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true
          fetch-depth: 0
          ref: release/${{ github.event.inputs.tag }}

      - name: Setup npmrc
        run: |
          echo "@switcherstudio:registry=https://npm.pkg.github.com" > .npmrc
          echo "//npm.pkg.github.com/:_authToken=${{ secrets.PAT_FOR_PACKAGES }}" >> .npmrc

      - name: get-npm-version
        id: package-version
        uses: martinbeentjes/npm-get-version-action@master

      - name: "Create Version File"
        uses: "switcherstudio/create-version-file@master"
        with:
          environment: "production"
          version: ${{ steps.package-version.outputs.current-version }}

      - name: Install
        run: yarn install

      - name: Build
        env:
          VITE_ENV: "production"
          VITE_VERSION: ${{ steps.package-version.outputs.current-version }}
          VITE_API_URL: "https://api.switcherstudio.com"
          VITE_PLAYER_API_URL: "https://vapi.switcherstudio.com"
          VITE_SEGMENT_CDNKEY: ${{ secrets.SEGMENT_CDNKEY }}
          VITE_SEGMENT_WRITEKEY: ${{ secrets.SEGMENT_WRITEKEY }}
          VITE_STRIPE_PUBLISHABLE_KEY: ${{ secrets.STRIPE_PUBLISHABLE_KEY }}
          VITE_LCR_API_URL: "https://livecommentsfn.azurewebsites.net"
          VITE_STRIPE_CHECKOUT_POLLING_INTERVAL: "1000"
          VITE_ENABLE_CONTROL_REDESIGN: true
          VITE_SHAKA_PLAYER_RECEIVER_ID: 14B5093F
          VITE_ENABLE_CATALOG_SEARCH: true

        run: yarn build

      - name: Deploy
        id: deploy
        uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CF_API_TOKEN }}
          accountId: ${{ secrets.CF_ACCOUNT_ID }}
          projectName: "embedded-video-player"
          directory: "build"
          gitHubToken: ${{ secrets.GITHUB_TOKEN }}
          branch: "main"

      - name: Purge cache
        uses: fjogeleit/http-request-action@v1
        with:
          url: ${{ format('https://api.cloudflare.com/client/v4/zones/{0}/purge_cache', secrets.CF_ZONE_ID)}}
          method: "POST"
          bearerToken: ${{ secrets.CF_PAGES_PUBLISH_TOKEN }}
          customHeaders: '{"Content-Type": "application/json"}'
          data: '{ "hosts": ["player.switcherstudio.com"] }'

      - name: Notify deploy to Rollbar
        uses: rollbar/github-deploy-action@2.1.2
        id: rollbar_deploy
        with:
          environment: "production"
          version: ${{ steps.package-version.outputs.current-version }}
          local_username: ${{ github.actor }}
        env:
          ROLLBAR_ACCESS_TOKEN: ${{ secrets.ROLLBAR_ACCESS_TOKEN }}
