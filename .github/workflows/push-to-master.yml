name: Push to Main

on:
  push:
    branches:
      - main

jobs:
  build_and_deploy_job:
    if: github.event_name == 'push' || (github.event_name == 'pull_request' && github.event.action != 'closed')
    runs-on: ubuntu-latest
    name: Build and Deploy Job
    strategy:
      max-parallel: 1
      matrix:
        node-version: [20.x]
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true
          fetch-depth: 0

      - name: Setup npmrc
        run: |
          echo "@switcherstudio:registry=https://npm.pkg.github.com" > .npmrc
          echo "//npm.pkg.github.com/:_authToken=${{ secrets.PAT_FOR_PACKAGES }}" >> .npmrc

      - name: get-npm-version
        id: package-version
        uses: martinbeentjes/npm-get-version-action@master

      - name: "Create Version File"
        uses: "switcherstudio/create-version-file@master"
        with:
          environment: "production"
          version: ${{ steps.package-version.outputs.current-version }}

      - name: Install
        run: yarn install

      - name: Build
        env:
          VITE_ENV: "production"
          VITE_VERSION: ${{ steps.package-version.outputs.current-version }}
          VITE_API_URL: "https://api.switcherstudio.com"
          VITE_PLAYER_API_URL: "https://vapi.switcherstudio.com"
          VITE_SEGMENT_CDNKEY: ${{ secrets.SEGMENT_CDNKEY }}
          VITE_SEGMENT_WRITEKEY: ${{ secrets.SEGMENT_WRITEKEY }}
          VITE_STRIPE_PUBLISHABLE_KEY: ${{ secrets.STRIPE_PUBLISHABLE_KEY }}
          VITE_LCR_API_URL: "https://livecommentsfn.azurewebsites.net"
          VITE_STRIPE_CHECKOUT_POLLING_INTERVAL: "1000"
          VITE_ENABLE_CONTROL_REDESIGN: true
          VITE_SHAKA_PLAYER_RECEIVER_ID: 14B5093F
          VITE_ENABLE_CATALOG_SEARCH: true

        run: yarn build

      - name: Deploy
        id: deploy
        uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CF_API_TOKEN }}
          accountId: ${{ secrets.CF_ACCOUNT_ID }}
          projectName: "embedded-video-player"
          directory: "build"
          gitHubToken: ${{ secrets.GITHUB_TOKEN }}
          branch: "main"

      - name: Purge cache
        uses: fjogeleit/http-request-action@v1
        with:
          url: ${{ format('https://api.cloudflare.com/client/v4/zones/{0}/purge_cache', secrets.CF_ZONE_ID)}}
          method: "POST"
          bearerToken: ${{ secrets.CF_PAGES_PUBLISH_TOKEN }}
          customHeaders: '{"Content-Type": "application/json"}'
          data: '{ "hosts": ["player.switcherstudio.com"] }'

      - name: "Get Latest Tag"
        shell: bash
        run: |
          git fetch --all --tags
          echo "tag=$(git describe --tags --abbrev=0)" >> $GITHUB_OUTPUT
        id: extract_latest_tag

      # Drafts and creates release
      - name: "Draft and Create Release"
        uses: release-drafter/release-drafter@v5
        with:
          # Publish the release immediately
          publish: true
          version: ${{ steps.extract_latest_tag.outputs.tag }}
          commitish: main
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      # Notify rollbar of deployment
      - name: Notify deploy to Rollbar
        uses: rollbar/github-deploy-action@2.1.2
        id: rollbar_deploy
        with:
          environment: "production"
          version: ${{ steps.package-version.outputs.current-version }}
          local_username: ${{ github.actor }}
        env:
          ROLLBAR_ACCESS_TOKEN: ${{ secrets.ROLLBAR_ACCESS_TOKEN }}

      # Create release branch
      - name: "Create Release Branch"
        uses: peterjgrainger/action-create-branch@v3.0.0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          branch: "release/${{ steps.extract_latest_tag.outputs.tag }}"
