@use "sass:math";

@mixin prep-glow-shadow() {
    filter: drop-shadow(rgba(0, 0, 0, 0) rem(0) rem(0) rem(0));
    transition: filter 0.15s ease 0s;
    will-change: filter;
}

@mixin prep-fade-out($delay: 2s, $duration: 0.15s) {
    opacity: 1;
    pointer-events: all;
    transition: opacity $duration ease $delay;
    will-change: opacity pointer-events;
}

@mixin fade-out() {
    opacity: 0;
    pointer-events: none;
}

@mixin prep-pop-in($duration: 0.15s) {
    transition: opacity $duration ease;
    will-change: opacity;
}

@mixin pop-in() {
    opacity: 1;
    pointer-events: all;
}

@mixin hover-glow-shadow($color: white) {
    filter: drop-shadow(rem(0) rem(0) 0.4rem $color);
}

@mixin glow-shadow($color: white) {
    @include prep-glow-shadow();

    &:hover {
        @include hover-glow-shadow($color);
    }
}

@mixin mobile-landscape() {
    @media screen and (max-width: $mobile-small-breakpoint) and (orientation: landscape) {
        @content;
    }
}

@mixin mobile-portrait-in-global() {
    @media screen and (max-width: $mobile-large-breakpoint) and (orientation: portrait) {
        &:global(.is-landscape-player) {
            @content;
        }
    }

    @media screen and (max-width: $mobile-small-breakpoint) and (orientation: portrait) {
        &:global(.is-portrait-player) {
            @content;
        }
    }
}

@mixin mobile-portrait() {
    // This is a "fix" to allow these mixins to be defined at the top level
    // "Top-level selectors may not contain the parent selector "&"."
    $s: #{if(&, "&", "")};

    @media screen and (max-width: $mobile-large-breakpoint) and (orientation: portrait) {
        :global(.is-landscape-player) #{$s} {
            @content;
        }
    }

    @media screen and (max-width: $mobile-small-breakpoint) and (orientation: portrait) {
        :global(.is-portrait-player) #{$s} {
            @content;
        }
    }
}

@mixin mobile-in-global() {
    @media screen and (max-width: $mobile-large-breakpoint) and (orientation: portrait) {
        &:global(.is-landscape-player) {
            @content;
        }
    }
    @media screen and (max-width: $mobile-small-breakpoint) and (orientation: landscape) {
        &:global(.is-landscape-player) {
            @content;
        }
    }

    @media screen and (max-width: $mobile-small-breakpoint) {
        &:global(.is-portrait-player) {
            @content;
        }
    }
}

@mixin mobile-no-orientation() {
    @media screen and (max-width: $mobile-large-breakpoint) {
        @content;
    }
}

@mixin mobile() {
    // This is a "fix" to allow these mixins to be defined at the top level
    // "Top-level selectors may not contain the parent selector "&"."
    $s: #{if(&, "&", "")};

    @media screen and (max-width: $mobile-large-breakpoint) and (orientation: portrait) {
        :global(.is-landscape-player) #{$s} {
            @content;
        }
    }
    @media screen and (max-width: $mobile-small-breakpoint) and (orientation: landscape) {
        :global(.is-landscape-player) #{$s} {
            @content;
        }
    }

    @media screen and (max-width: $mobile-small-breakpoint) {
        :global(.is-portrait-player) #{$s} {
            @content;
        }
    }
}

@mixin tablet() {
    @media screen and (min-width: math.round($mobile-small-breakpoint)) and (max-width: $mobile-xlarge-breakpoint) {
        @content;
    }
}

@mixin desktop() {
    @media screen and (min-width: math.round($mobile-large-breakpoint)) {
        @content;
    }
}

@mixin sixteenByNine() {
    position: relative;
    height: 0 !important;
    width: 100% !important;
    padding-top: 56.25% !important;
}

@mixin nineBySixteen() {
    position: relative;
    height: 0 !important;
    width: 100% !important;
    padding-top: 177.78% !important;
}

@mixin wrapTextOverflow() {
    overflow-wrap: anywhere;
    word-break: normal;
}
