import {
    getAnonymousId,
    pathToPage,
    trackSegmentPage
} from "helpers/analyticsHelpers";
import { useState, useCallback, useMemo } from "react";
import { useSelector } from "react-redux";
import { useLocation } from "react-router-dom";
import { RootState } from "store/reducers";

export const usePageTracking = () => {
    const location = useLocation();
    const [pageTracked, setPageTracked] = useState(false);
    const isMicrosite = useSelector(
        (s: RootState) => s.videoSession.isMicrosite
    );
    const playerCreator = useSelector((s: RootState) => s.playerCreator);
    const {
        configuredCatalogId,
        configuredCollectionId,
        configuredBroadcastId
    } = useSelector((s: RootState) => s.catalogState);

    const propertiesData = useMemo(
        () => ({
            playerId: configuredCollectionId,
            broadcastId: configuredBroadcastId,
            catalogId: configuredCatalogId,
            userId: playerCreator?.details?.userId,
            orgId: playerCreator?.details?.organizationId,
            stripeAccountId: playerCreator?.details?.stripeAccountId,
            viewSource: isMicrosite
                ? "Microsite"
                : pathToPage(location?.pathname),
            viewerId: getAnonymousId()
        }),
        [
            location,
            playerCreator,
            configuredCatalogId,
            configuredCollectionId,
            configuredBroadcastId,
            isMicrosite
        ]
    );

    const trackPage = useCallback(() => {
        // Only track page load once
        if (pageTracked) return;

        // Only track if we have the necessary data
        const { userId, catalogId, playerId, broadcastId } = propertiesData;
        if (!userId || (!catalogId && !playerId && !broadcastId)) return;

        if (catalogId) trackSegmentPage("Catalog Page", propertiesData);
        if (playerId) trackSegmentPage("Player Page", propertiesData);
        if (broadcastId) trackSegmentPage("Broadcast Page", propertiesData);

        setPageTracked(true);
    }, [propertiesData, pageTracked]);

    return { trackPage };
};
