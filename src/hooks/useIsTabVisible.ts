import { useState, useEffect } from "react";

export const useIsTabVisible = () => {
    const [isTabVisible, setIsTabVisible] = useState(true);
    useEffect(() => {
        const handleVisChange = () => {
            if (document.hidden) {
                setIsTabVisible(false);
            } else {
                setIsTabVisible(true);
            }
        };
        document.addEventListener("visibilitychange", handleVisChange);

        return () =>
            document.removeEventListener("visibilitychange", handleVisChange);
    }, []);

    return isTabVisible;
};
