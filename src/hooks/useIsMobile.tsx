import useCollectionWithVideos from "./useCollectionWithVideos";
import { useMediaQuery } from "./useMediaQuery";

/** Returns if the viewport matches mobile breakpoints. Does not detect device type */
export const useIsMobile = () => {
    const { collection } = useCollectionWithVideos();

    const isLandscapeMobile = useMediaQuery({
        maxWidth: 776,
        orientation: "landscape"
    });
    const isPortraitMobile = useMediaQuery({
        maxWidth:
            collection?.details?.aspectRatio === "SixteenByNine" ? 915 : 776,
        orientation: "portrait"
    });

    return {
        isMobile: isLandscapeMobile || isPortraitMobile,
        isLandscapeMobile,
        isPortraitMobile
    };
};
