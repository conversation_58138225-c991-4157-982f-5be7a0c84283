import {
    CreatorCustomerSubscriptionPortalResponse,
    payments
} from "api/payment/client";
import { useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "store/reducers";
import { useGatedContent } from "./useGatedContent";
import {
    EntitlementResponse,
    PurchaseResponse
} from "@switcherstudio/player-api-client";

export const useManageSubscriptionLinks = () => {
    const {
        catalogInvoices,
        playerInvoices,
        playerPurchaseEntitlements,
        catalogPurchaseEntitlements,
        playlistBroadcastPurchaseEntitlements
    } = useGatedContent();
    const { details } = useSelector((s: RootState) => s.playerCreator);

    const [subscriptionPurchaseResponseMap, setInvoicesByResponse] = useState<
        Map<CreatorCustomerSubscriptionPortalResponse, PurchaseResponse>
    >(new Map());

    const subscriptionResponses = useMemo(() => {
        if (!details?.stripeAccountId) return [];

        const invoices = [
            ...(catalogInvoices ?? []),
            ...(playerInvoices ?? [])
        ];

        const filteredInvoices = invoices.filter(
            (invoice) =>
                invoice.details?.purchaseType === "Subscription" &&
                invoice?.details?.customerId &&
                invoice?.details?.id
        );

        return filteredInvoices;
    }, [details?.stripeAccountId, catalogInvoices, playerInvoices]);

    useEffect(() => {
        if (!details?.stripeAccountId) return;

        const fetch = async () => {
            const newResponses: Map<
                CreatorCustomerSubscriptionPortalResponse,
                PurchaseResponse
            > = new Map();

            for (const subscription of subscriptionResponses) {
                const response = payments.GetSessionPortal({
                    stripeAccountId: details?.stripeAccountId ?? "",
                    customerId: subscription?.details?.customerId ?? "",
                    subscriptionId: subscription?.details?.id ?? ""
                });

                newResponses.set(await response, subscription);
            }

            setInvoicesByResponse(newResponses);
        };

        fetch();
    }, [subscriptionResponses, details?.stripeAccountId]);

    const oneTimePassPurchaseResponses = useMemo(() => {
        const allEntitlements: EntitlementResponse[] = [
            ...playerPurchaseEntitlements,
            ...catalogPurchaseEntitlements,
            ...playlistBroadcastPurchaseEntitlements
        ];

        if (!allEntitlements) return [];

        return allEntitlements
            .flatMap((invoice) => invoice?.purchases ?? [])
            .filter(
                (purchase): purchase is PurchaseResponse =>
                    purchase !== undefined &&
                    purchase.details.purchaseType !== "Subscription"
            );
    }, [
        playerPurchaseEntitlements,
        catalogPurchaseEntitlements,
        playlistBroadcastPurchaseEntitlements
    ]);

    return {
        subscriptionPurchaseResponseMap,
        oneTimePassPurchaseResponses
    };
};
