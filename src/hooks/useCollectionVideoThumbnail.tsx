import { useMemo } from "react";
import { CollectionVideoResponse } from "@switcherstudio/player-api-client";
import { getCorrectedAspectRatioThumbnailURL } from "helpers/imageHelpers";
import { isInFuture } from "helpers/time";
import { Countdown } from "components/Countdown";
import { useTranslation } from "react-i18next";
import { useUpdateTrigger } from "./useUpdateTrigger";

interface CollectionVideoThumbnailProps {
    collectionVideo: CollectionVideoResponse | null | undefined;
    variant?: "full" | "compact" | "compact-vertical";
}

export const useCollectionVideoThumbnail = ({
    collectionVideo,
    variant = "compact"
}: CollectionVideoThumbnailProps) => {
    const { t } = useTranslation();
    const { updateKey, updateTrigger } = useUpdateTrigger();

    const thumbnail = useMemo(() => {
        return getCorrectedAspectRatioThumbnailURL(
            collectionVideo?.broadcast?.thumbnail?.details?.url ??
                collectionVideo?.broadcast?.videos?.[0]?.details?.thumbnail
        );
    }, [collectionVideo?.broadcast]);

    const isScheduledUpload = useMemo<boolean>(() => {
        const StartsAt = collectionVideo?.broadcast?.details?.startsAt;
        return (
            !!StartsAt &&
            isInFuture(StartsAt) &&
            collectionVideo?.broadcast?.details?.broadcastStatus === "Ended"
        );
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [
        collectionVideo?.broadcast?.details?.startsAt,
        collectionVideo?.broadcast?.details?.broadcastStatus,
        updateKey
    ]);

    const isScheduledLive = useMemo<boolean>(() => {
        const StartsAt = collectionVideo?.broadcast?.details?.startsAt;
        return (
            !!StartsAt &&
            collectionVideo?.broadcast?.details?.broadcastStatus === "Ready"
        );
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [
        collectionVideo?.broadcast?.details?.startsAt,
        collectionVideo?.broadcast?.details?.broadcastStatus,
        updateKey
    ]);

    const isScheduled = useMemo(() => {
        return isScheduledLive || isScheduledUpload;
    }, [isScheduledLive, isScheduledUpload]);

    const scheduledThumbnail = useMemo(() => {
        if (
            isScheduled &&
            (variant === "full" ||
                ((variant === "compact-vertical" || variant === "compact") &&
                    collectionVideo?.broadcast?.details?.showPremiereCountdown))
        ) {
            return (
                <Countdown
                    key={collectionVideo?.broadcast?.details?.id}
                    title={
                        isScheduledLive
                            ? t("overlays:starting-in")
                            : t("overlays:premiering-in")
                    }
                    postCountdownTitle={
                        isScheduledLive
                            ? t("overlays:starting-soon")
                            : t("overlays:premiering-soon")
                    }
                    countdownTargetDate={
                        collectionVideo?.broadcast?.details?.startsAt
                    }
                    onCountDownEnd={updateTrigger}
                    variant={variant}
                />
            );
        }
    }, [
        collectionVideo,
        isScheduled,
        isScheduledLive,
        t,
        updateTrigger,
        variant
    ]);

    return {
        thumbnail,
        scheduledThumbnail,
        isScheduledUpload,
        isScheduledLive,
        isScheduled,
        updateKey
    };
};
