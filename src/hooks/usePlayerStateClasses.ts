import { useMemo } from "react";
import { useSelector } from "react-redux";
import { RootState } from "store/reducers";
import { isMobile } from "react-device-detect";
import useCollectionWithVideos from "./useCollectionWithVideos";

export const usePlayerStateClasses = () => {
    const { isExpanded, isEmbed, isPlaying, isToolDrawerActive } = useSelector(
        (s: RootState) => s.videoSession
    );
    const { collection } = useCollectionWithVideos();

    const classes = useMemo(() => {
        const classArray: string[] = [];

        // Add classes based on state
        classArray.push(isPlaying ? "is-playing" : "is-paused");
        if (isToolDrawerActive) classArray.push("tool-active");
        if (isEmbed) classArray.push("is-embed");
        if (isExpanded) classArray.push("is-expanded");
        if (isMobile) classArray.push("is-mobile");
        if (collection?.details?.aspectRatio === "NineBySixteen")
            classArray.push("is-portrait-player");
        if (collection?.details?.aspectRatio === "SixteenByNine")
            classArray.push("is-landscape-player");

        // Return classes as a string
        return classArray.join(" ");
    }, [
        isPlaying,
        isToolDrawerActive,
        isEmbed,
        isExpanded,
        collection?.details?.aspectRatio
    ]);

    return classes;
};
