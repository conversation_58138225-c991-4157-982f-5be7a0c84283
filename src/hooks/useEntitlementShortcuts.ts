import {
    CatalogResponse,
    CollectionResponse,
    EntitlementType
} from "@switcherstudio/player-api-client";
import { useMemo } from "react";

/**
 * This hook returns calculated booleans to determine copy/button visibility
 *
 * @param entity - The entity to check for purchase options
 * @returns An object with the following properties:
 * - showPurchaseButton: Whether the entity UI should display a purchase button
 * - isOnlySubscribe: Whether all the entity's active prices are recurring
 */
export const useEntitlementShortcuts = (
    entity: CollectionResponse | CatalogResponse,
    entitlementType: EntitlementType
) => {
    /** True if the entity has a purchase entitlement and no purchases */
    const showPurchaseButton = useMemo(() => {
        return (
            (entity?.entitlements?.some((entitlement) => {
                return (
                    entitlement?.details?.redemptionType === "Purchase" &&
                    entitlement?.details?.type == entitlementType
                );
            }) &&
                !entity?.entitlements?.some((entitlement) => {
                    return (entitlement?.purchases?.length ?? 0) >= 1;
                })) ??
            false
        );
    }, [entity?.entitlements, entitlementType]);

    /** True if all the entity's active prices are recurring */
    const isOnlySubscribe = useMemo(() => {
        return entity?.entitlements?.every((entitlement) =>
            entitlement?.prices
                ?.filter((price) => price?.details?.isActive)
                .every((price) => price?.details?.isRecurring)
        );
    }, [entity?.entitlements]);

    return {
        showPurchaseButton,
        isOnlySubscribe
    };
};
