import { useEffect } from "react";
import { liveCommentsReceiver } from "../api/liveCommentsReceiver/lcr-client";
import { SyncClient } from "twilio-sync";
import { addNewProduct } from "../store/shopping/slice";
import { useDispatch } from "react-redux";
import { useGatedContent } from "./useGatedContent";
import { BroadcastResponse } from "@switcherstudio/player-api-client";

export const useTwilioEventHandler = (
    broadcast: BroadcastResponse | undefined
) => {
    const dispatch = useDispatch();
    const broadcastId = broadcast?.details?.id;
    const broadcastStatus = broadcast?.details?.broadcastStatus;
    const isLiveShopping = broadcast?.details?.enableLiveShopping;
    const { playlistBroadcastIsEntitled, playlistBroadcastIsGated } =
        useGatedContent();

    useEffect(() => {
        if (broadcastId == null || broadcastStatus === undefined) return;
        async function fetch() {
            const credentials = await liveCommentsReceiver.getTwilioSyncToken({
                broadcastId: broadcastId
            });
            const syncClient = new SyncClient(credentials.token);
            syncClient.list(broadcastId).then((list) => {
                list.on("itemAdded", (args) => {
                    const res = args.item.descriptor.data;
                    const product = {
                        title: res.Title,
                        code: res.Code,
                        productId: res.ProductId,
                        handle: res.Handle,
                        image: res.Image,
                        minPrice: res.MinPrice,
                        maxPrice: res.MaxPrice,
                        currencyCode: res.CurrencyCode,
                        availableForSale: res.AvailableForSale,
                        totalInventory: res.totalInventory,
                        variants: res.variants
                    };
                    dispatch(addNewProduct(product));
                });
            });
        }
        if (
            (broadcastStatus === "Created" ||
                broadcastStatus === "Ready" ||
                broadcastStatus === "Active") &&
            isLiveShopping &&
            (playlistBroadcastIsEntitled || !playlistBroadcastIsGated)
        )
            fetch();
    }, [
        broadcastId,
        broadcastStatus,
        isLiveShopping,
        dispatch,
        playlistBroadcastIsEntitled,
        playlistBroadcastIsGated
    ]);

    return {};
};
