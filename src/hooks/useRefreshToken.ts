import { useDispatch } from "react-redux";
import { useEffect, useState, useCallback } from "react";
import { AppDispatch } from "store/store";
import { refreshToken } from "store/CreatorCustomers/thunks";
import { resetNotifications } from "store/notification/slice";

export interface UseRefreshTokenOptions {
    lazyLoad?: boolean;
}

export function useRefreshToken(
    { lazyLoad }: UseRefreshTokenOptions = { lazyLoad: false }
) {
    const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
    const dispatch = useDispatch<AppDispatch>();
    // Get a new refresh token and ticket

    const getNewRefreshToken = useCallback(async () => {
        try {
            await dispatch(refreshToken());
            setIsAuthenticated(true);
        } catch (err) {
            dispatch(resetNotifications());
        }
    }, [dispatch]);

    useEffect(() => {
        if (!lazyLoad) {
            getNewRefreshToken();
        }
    }, [getNewRefreshToken, lazyLoad]);

    return { isAuthenticated, getNewRefreshToken };
}
