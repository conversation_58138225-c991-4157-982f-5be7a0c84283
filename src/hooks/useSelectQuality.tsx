import { KebabMenuSelectItemOption } from "components/ShakaPlayer/components/KebabMenu";
import { useMemo, useCallback, useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "store/reducers";
import {
    setUserSelectedQualityLevel,
    setShakaVideoQualityIsAuto
} from "store/VideoSession/slice";
import { QualityLevelOptions } from "store/VideoSession/types";

export interface useSelectQualityOptions {
    shakaPlayer: shaka.Player | undefined;
}

/**
 * A custom hook for managing video quality selection using a Shaka player instance.
 *
 * This hook allows components to select and manage video quality levels in response
 * to media playback events and user interactions. It listens for media loading events
 * to retrieve available quality levels, provides methods to change the video quality
 * level, and ensures that the selected quality level remains in sync with user
 * preferences and available tracks.
 *
 * @param {shaka.Player | undefined} options.shakaPlayer - The Shaka player instance used to manage quality levels.
 *
 * @returns {Object} An object containing the following properties and methods:
 * - isAuto: A boolean indicating whether the video quality is set to auto.
 * - levels: An array of available quality levels, based on the current media.
 * - changeLevel: A function to change the quality level, either to a specific level or 'auto'.
 * - userSelectedQualityLevel: The quality level selected by the user.
 * - constrainedSelectedQualityLevel: The currently constrained quality level.
 * - getLevelLabel: A function to retrieve the display label for a given quality level ID.
 *
 * The hook handles potential changes in quality levels when the user-selected level is not
 * available and resets the level based on user selections or the current media context.
 */
export const useSelectQuality = ({ shakaPlayer }: useSelectQualityOptions) => {
    const dispatch = useDispatch();

    const {
        userSelectedQualityLevel,
        constrainedSelectedQualityLevel,
        shakaVideoQuality: {
            levelInfo: { newTrack },
            isAuto
        }
    } = useSelector((s: RootState) => s.videoSession);

    const [levels, setLevels] = useState<shaka.extern.Track[]>();

    // Add event listener for media loading complete
    useEffect(() => {
        if (!shakaPlayer) return;

        const onMediaLoaded = () => {
            setLevels(
                shakaPlayer
                    ?.getVariantTracks()
                    ?.sort((a, b) => b.bandwidth - a.bandwidth)
            );
        };

        shakaPlayer.addEventListener("loaded", onMediaLoaded);

        return () => {
            shakaPlayer.removeEventListener("loaded", onMediaLoaded);
        };
    }, [shakaPlayer]);

    const levelNumbers = useMemo(() => levels?.map((l) => l.id), [levels]);

    const changeLevel = useCallback(
        (level: shaka.extern.Track | "auto", fromUser: boolean = false) => {
            if (!shakaPlayer) return;

            if (fromUser) {
                dispatch(
                    setUserSelectedQualityLevel(
                        level === "auto" ? level : level?.id
                    )
                );
            }

            if (level === "auto") {
                shakaPlayer.configure({ abr: { enabled: true } });
                dispatch(setShakaVideoQualityIsAuto(true));
                return;
            }

            shakaPlayer.configure({ abr: { enabled: false } });
            shakaPlayer.selectVariantTrack(level, true, 5); // clears buffer and switches to new quality after 2 seconds
            dispatch(setShakaVideoQualityIsAuto(false));
        },
        [dispatch, shakaPlayer]
    );

    const getLevelLabel = useCallback(
        (id: "auto" | QualityLevelOptions): string => {
            return id === "auto"
                ? "Auto"
                : `${levels?.find((l) => l.id === id)?.height}p`;
        },
        [levels]
    );

    /** Handle quality level bump down when previous level no longer available on current video */
    useEffect(() => {
        if (!levels || !levelNumbers) return;

        const bumpDownLevel = () => {
            const nextHighestLevel = levels[0];
            changeLevel(nextHighestLevel);
        };

        const resetLevelToUserSelection = () => {
            const userSelectedLevelObject = levels.find(
                (l) => l.id === userSelectedQualityLevel
            );
            if (userSelectedLevelObject) {
                changeLevel(userSelectedLevelObject);
            }
        };

        if (userSelectedQualityLevel === "auto" || !levels?.length) return;

        if (!levelNumbers.includes(userSelectedQualityLevel)) {
            // user's selected quality level no longer available, need to bump down to next highest level
            bumpDownLevel();
        } else if (newTrack?.id !== userSelectedQualityLevel) {
            // user's selected quality level is available, but is different from current constrained level
            // need to reset constrained level to match previous selection
            resetLevelToUserSelection();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [levels, dispatch, userSelectedQualityLevel]);

    const qualityOptions = useMemo<
        KebabMenuSelectItemOption<number | "auto">[]
    >(() => {
        return [
            {
                id: "auto",
                label: "Auto",
                selected: isAuto
            },
            ...(levels?.map((level) => ({
                id: level.id,
                label: `${level?.height}p`,
                selected: level.id === userSelectedQualityLevel
            })) ?? [])
        ];
    }, [isAuto, levels, userSelectedQualityLevel]);

    const onQualityOptionsSelected = useCallback(
        (level: number | "auto") => {
            changeLevel(levels?.find((l) => l.id === level) ?? "auto", true);
        },
        [changeLevel, levels]
    );

    return {
        isAuto,
        levels,
        changeLevel,
        userSelectedQualityLevel,
        constrainedSelectedQualityLevel,
        getLevelLabel,
        qualityOptions,
        onQualityOptionsSelected
    };
};
