import { CollectionVideoResponse } from "@switcherstudio/player-api-client";
import { useCallback } from "react";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { openConfirmation } from "store/Confirmation/slice";
import { RootState } from "store/reducers";
import { clearCart } from "store/shopping/slice";
import { AppDispatch } from "store/store";
import { v4 as uuidv4 } from "uuid";
import {
    setCurrentCollectionVideo,
    setPreselectedBroadcastId,
    setSessionId,
    setVoDState
} from "store/VideoSession/slice";

export const useSetNewCollectionVideo = () => {
    const { cart } = useSelector((s: RootState) => s.shopping);
    const { currentCollectionVideo } = useSelector(
        (s: RootState) => s.videoSession
    );
    const dispatch = useDispatch<AppDispatch>();

    const setNewCollectionVideo = useCallback(
        (collectionVideo: CollectionVideoResponse | undefined | null) => {
            const newBroadcastHandle = (isLiveEnded: boolean) => {
                //console.log("NEW CV", collectionVideo, isLiveEnded);
                // set the current video
                dispatch(
                    setCurrentCollectionVideo({
                        collectionVideo,
                        isLiveEnded
                    })
                );

                // set the VOD state (live or VOD)
                dispatch(
                    setVoDState(
                        collectionVideo?.broadcast?.details?.broadcastStatus !==
                            "Active"
                    )
                );

                // start a new session id
                if (!isLiveEnded) {
                    dispatch(setSessionId(uuidv4()));
                }

                // set legacy preselected broadcast
                dispatch(
                    setPreselectedBroadcastId(
                        collectionVideo?.broadcast?.details?.id ?? null
                    )
                );
            };

            // if the cart is not empty and the current video is different than the new video, show a confirmation dialog
            if (
                cart?.length > 0 &&
                currentCollectionVideo?.broadcast?.details?.id !==
                    collectionVideo?.broadcast?.details?.id
            ) {
                dispatch(
                    openConfirmation({
                        titleText: "Are you sure?",
                        message:
                            "You have items in your cart. If you proceed, you'll lose them.",
                        confirmText:
                            collectionVideo == null
                                ? `Watch "Live Player"`
                                : `Watch "${collectionVideo?.broadcast?.details?.title}"`,
                        cancelText: "Stay Here",
                        onSuccess: () => {
                            newBroadcastHandle(false);
                            dispatch(clearCart());
                        }
                    })
                );
            } else {
                newBroadcastHandle(
                    collectionVideo?.broadcast?.details?.broadcastStatus ===
                        "Ended" &&
                        collectionVideo?.broadcast?.videos?.[0]?.details
                            ?.status === "live-inprogress"
                );
            }
        },
        [dispatch, cart, currentCollectionVideo]
    );

    return setNewCollectionVideo;
};
