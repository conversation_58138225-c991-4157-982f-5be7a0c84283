import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "store/reducers";
import { useGatedContent } from "./useGatedContent";
import {
    CreatorCustomerSubscriptionPortalResponse,
    payments
} from "api/payment/client";

export const useManageSubscriptionLink = () => {
    const { details } = useSelector((s: RootState) => s.playerCreator);

    const [response, setResponse] =
        useState<CreatorCustomerSubscriptionPortalResponse>();

    const { catalogInvoices, playerInvoices } = useGatedContent();

    useEffect(() => {
        const fetch = async () => {
            const invoice = catalogInvoices?.[0] ?? playerInvoices?.[0];
            if (details?.stripeAccountId && !!invoice) {
                setResponse(
                    await payments.GetSessionPortal({
                        stripeAccountId: details?.stripeAccountId,
                        customerId: invoice?.details?.customerId ?? "",
                        subscriptionId: invoice?.details?.id ?? ""
                    })
                );
            }
        };
        fetch();
    }, [details?.stripeAccountId, catalogInvoices, playerInvoices]);

    return {
        loading: !response,
        manageSubscriptionLink: response?.Url,
        updateSubscriptionLink: response?.UpdateUrl,
        renewSubscriptionLink: response?.RenewUrl,
        cancelSubscriptionLink: response?.CancelUrl
    };
};
