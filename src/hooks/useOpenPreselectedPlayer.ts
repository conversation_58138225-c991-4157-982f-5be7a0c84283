import { useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useSearchParams } from "react-router-dom";
import { RootState } from "store/reducers";
import {
    setActiveCollectionTab,
    setIsFromShare
} from "store/VideoSession/slice";
import { IFrameType } from "store/VideoSession/types";
import useCollectionWithVideos from "./useCollectionWithVideos";
import { useToggleInteractiveMode } from "./useToggleInteractiveMode";
import { useSetNewCollectionVideo } from "./useSetNewCollectionVideo";
import { useRollbar } from "@rollbar/react";

export const useOpenPreselectedPlayer = () => {
    const [searchParams] = useSearchParams();
    const dispatch = useDispatch();
    const rollbar = useRollbar();
    const { parentFrame, iframeType, hasSetDefault } = useSelector(
        (state: RootState) => state.videoSession
    );

    const [initialLoad, setInitialLoad] = useState(true);

    const { collection, onDemandVideos, upcomingVideos, hasLoaded } =
        useCollectionWithVideos();

    const { toggleInteractive } = useToggleInteractiveMode();
    const setNewCollectionVideo = useSetNewCollectionVideo();

    const catalogId = useMemo<string | null | undefined>(
        () => searchParams.get("c"),
        [searchParams]
    );

    const pbid = useMemo<string | null | undefined>(
        () => searchParams.get("switcher-pbid"),
        [searchParams]
    );

    const pcid = useMemo<string | null | undefined>(
        () => searchParams.get("switcher-pcid"),
        [searchParams]
    );

    useEffect(() => {
        if (initialLoad && pcid && pbid && iframeType === IFrameType.Main) {
            dispatch(setIsFromShare(true));
            if (!hasLoaded) return;
            if (collection?.details?.embeddedDisplay === "DefaultThumbnail") {
                toggleInteractive(true);

                const timeoutId = setTimeout(() => {
                    const currentOnDemandVideo = onDemandVideos.find(
                        (video) => video.broadcast?.details?.id === pbid
                    );

                    const currentUpcomingVideo = upcomingVideos.find(
                        (video) => video.broadcast?.details?.id === pbid
                    );

                    if (currentOnDemandVideo) {
                        // set the current video
                        setNewCollectionVideo(currentOnDemandVideo);
                    } else if (currentUpcomingVideo) {
                        // set the current video and update the active tab
                        setNewCollectionVideo(currentUpcomingVideo);
                        dispatch(setActiveCollectionTab("Upcoming"));
                    }

                    // call toggle interactive on load
                    setInitialLoad(false);
                }, 1000);

                return () => {
                    clearTimeout(timeoutId);
                };
            } else {
                if (parentFrame?.openPlayer) {
                    const timeoutId = setTimeout(() => {
                        parentFrame?.openPlayer(
                            catalogId || "",
                            pcid,
                            null,
                            null,
                            pbid
                        );

                        setInitialLoad(false);
                    }, 1000);

                    return () => {
                        clearTimeout(timeoutId);
                    };
                } else {
                    rollbar.error(
                        "Parent frame not available or does not have openPlayer method"
                    );
                }
            }
        }
    }, [
        collection?.details,
        catalogId,
        pcid,
        pbid,
        parentFrame,
        iframeType,
        dispatch,
        toggleInteractive,
        onDemandVideos,
        upcomingVideos,
        setNewCollectionVideo,
        hasSetDefault,
        initialLoad,
        hasLoaded,
        rollbar
    ]);
};
