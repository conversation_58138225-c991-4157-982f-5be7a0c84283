import { CollectionVideoResponse } from "@switcherstudio/player-api-client";
import { useSelector } from "react-redux";
import { createSelector } from "reselect";
import { RootState } from "store/reducers";

// Shared empty array for referential stability
const EMPTY_ARRAY: readonly any[] = Object.freeze([]);

// Selectors
const selectCollections = (state: RootState) =>
    state.catalogState.collections.collections;
const selectCollectionVideosMap = (state: RootState) =>
    state.catalogState.collectionVideosMap;
const selectHasLoadedOnDemand = (state: RootState) =>
    state.catalogState.hasLoadedCollectionVideosOnDemand;
const selectHasLoadedUpcoming = (state: RootState) =>
    state.catalogState.hasLoadedCollectionVideosUpcoming;
const selectHasLoadedCollections = (state: RootState) =>
    state.catalogState.hasLoadedCollections;

// First select the collection
const selectCollection = createSelector(
    [selectCollections],
    (collections) => collections?.[0]
);

// Select the collection ID
const selectCollectionId = createSelector(
    [selectCollection],
    (collection) => collection?.details?.id ?? ""
);

// Select videos for the collection
const selectCollectionVideos = createSelector(
    [selectCollectionVideosMap, selectCollectionId],
    (collectionVideosMap, collectionId) => collectionVideosMap?.[collectionId]
);

// Select the OnDemand videos
const selectOnDemandVideos = createSelector(
    [selectCollectionVideos],
    (collectionVideos): readonly CollectionVideoResponse[] => {
        const videos = collectionVideos?.OnDemand?.items;
        if (!videos) return EMPTY_ARRAY;

        const filteredVideos = videos.filter((item) => !!item);
        // Return empty array reference if no videos
        if (filteredVideos.length === 0) return EMPTY_ARRAY;

        return filteredVideos;
    }
);

// Select the OnDemand videos count
const selectOnDemandVideosCount = createSelector(
    [selectCollectionVideos],
    (collectionVideos): number => {
        const videosCount = collectionVideos?.OnDemand?.totalRecords;
        if (!videosCount) return 0;

        return videosCount;
    }
);

// Select the OnDemand page count
const selectOnDemandPageCount = createSelector(
    [selectCollectionVideos],
    (collectionVideos): number => {
        return collectionVideos?.OnDemand?.page ?? 0;
    }
);

// Select the Upcoming videos
const selectUpcomingVideos = createSelector(
    [selectCollectionVideos],
    (collectionVideos): readonly CollectionVideoResponse[] => {
        const videos = collectionVideos?.Upcoming?.items;
        if (!videos) return EMPTY_ARRAY;

        const filteredVideos = videos.filter((item) => !!item);
        // Return empty array reference if no videos
        if (filteredVideos.length === 0) return EMPTY_ARRAY;

        return filteredVideos;
    }
);

// Select the Upcoming videos count
const selectUpcomingVideosCount = createSelector(
    [selectCollectionVideos],
    (collectionVideos): number => {
        const videosCount = collectionVideos?.Upcoming?.totalRecords;
        if (!videosCount) return 0;

        return videosCount;
    }
);

// Select the Upcoming page count
const selectUpcomingPageCount = createSelector(
    [selectCollectionVideos],
    (collectionVideos): number => {
        return collectionVideos?.Upcoming?.page ?? 0;
    }
);

// Select the Processing videos
const selectProcessingVideos = createSelector(
    [selectCollectionVideos],
    (collectionVideos) => {
        const videos = collectionVideos?.Processing;
        if (!videos) return EMPTY_ARRAY;

        const filteredVideos = videos.filter((item) => !!item);
        // Return empty array reference if no videos
        if (filteredVideos.length === 0) return EMPTY_ARRAY;

        return filteredVideos;
    }
);

// Select the loading state
const selectHasLoaded = createSelector(
    [
        selectHasLoadedOnDemand,
        selectHasLoadedUpcoming,
        selectHasLoadedCollections
    ],
    (hasLoadedOnDemand, hasLoadedUpcoming, hasLoadedCollections) =>
        hasLoadedOnDemand && hasLoadedUpcoming && hasLoadedCollections
);

// Create a combined array selector that's referentially stable
const selectAllVideos = createSelector(
    [selectOnDemandVideos, selectUpcomingVideos],
    (onDemandVideos, upcomingVideos): readonly CollectionVideoResponse[] => {
        if (onDemandVideos.length === 0 && upcomingVideos.length === 0) {
            return EMPTY_ARRAY;
        }
        return [...upcomingVideos, ...onDemandVideos];
    }
);

// Combined selector
const selectCollectionAndVideos = createSelector(
    [
        selectCollection,
        selectHasLoaded,
        selectOnDemandVideos,
        selectUpcomingVideos,
        selectOnDemandVideosCount,
        selectUpcomingVideosCount,
        selectOnDemandPageCount,
        selectUpcomingPageCount,
        selectAllVideos,
        selectProcessingVideos
    ],
    (
        collection,
        hasLoaded,
        onDemandVideos,
        upcomingVideos,
        onDemandVideosCount,
        upcomingVideosCount,
        onDemandPageCount,
        upcomingPageCount,
        allVideos,
        processingVideos
    ) => ({
        collection,
        hasLoaded,
        onDemandVideos,
        upcomingVideos,
        onDemandVideosCount,
        upcomingVideosCount,
        onDemandPageCount,
        upcomingPageCount,
        allVideos,
        processingVideos
    })
);

const useCollectionWithVideos = () => {
    return useSelector(selectCollectionAndVideos);
};

export default useCollectionWithVideos;
