import { useCallback, useEffect, useRef, useState, useMemo } from "react";
import { useSelector } from "react-redux";
import { RootState } from "../store/reducers";
import {
    FinalVariant,
    ProductOption,
    ShoppingViews,
    VariantOption
} from "../store/shopping/types";
import { LCRApiResponse } from "../api/types";
import { liveCommentsReceiver } from "../api/liveCommentsReceiver/lcr-client";
import { useDispatch } from "react-redux";
import {
    changeScreen,
    addToCart as addToCartAction,
    setShoppingLoading
} from "store/shopping/slice";
import { addNotification } from "store/notification/slice";
import { NotificationType } from "store/notification/types";
import { openConfirmation } from "../store/Confirmation/slice";
import { useEventTracking } from "./useEventTracking";

export const useShopping = (broadcastId: string | null | undefined) => {
    const { selectedProduct, shopId, cart } = useSelector(
        (s: RootState) => s.shopping
    );
    const dispatch = useDispatch();
    const [availableOptions, setAvailableOptions] = useState<ProductOption[]>(
        []
    );
    const [availableQty, setAvailableQty] = useState<number>(1);
    const [answers, setAnswers] = useState<VariantOption[]>([]); //customer selections
    const [response, setResponse] = useState<LCRApiResponse>();
    const ansRef = useRef<VariantOption[]>([]);
    ansRef.current = answers;
    const { video } = useSelector((s: RootState) => s.videoSession);
    const { trackEvent } = useEventTracking();

    const fetchProductOptions = useCallback(
        async (answers: VariantOption[], choiceIndex: number) => {
            if (!selectedProduct?.productId) return;

            try {
                dispatch(setShoppingLoading(true));
                const res = await liveCommentsReceiver.getProductOptions(
                    {},
                    {
                        "BroadcastId": broadcastId,
                        "ProductId": selectedProduct?.productId,
                        "ChoiceIndex": choiceIndex,
                        "Answers": answers,
                        "ShopId": shopId
                    }
                );
                setAvailableOptions(res.options);
                if (res.finalVariant.quantityAvailable <= 0) {
                    res.finalVariant.quantityAvailable = 0;
                }
                setResponse(res);
                setAnswers(res.answerList);

                if (!res.returnMsg) {
                    //return msg indicates that the item is out of stock
                    if (res.finalVariant.quantityAvailable > 20) {
                        setAvailableQty(19);
                    } else {
                        setAvailableQty(res.finalVariant.quantityAvailable);
                    }
                }
                dispatch(setShoppingLoading(false));
            } catch (e) {
                dispatch(setShoppingLoading(false));
                throw e;
            }
        },
        [broadcastId, selectedProduct?.productId, shopId, dispatch]
    );

    useEffect(() => {
        fetchProductOptions([], -1); //choice index -1 means that there has been no customer selections yet
    }, [fetchProductOptions, broadcastId, selectedProduct?.productId, shopId]);

    const onOptionChange = useCallback(
        async (val: string, name: string | undefined) => {
            let choiceIndex = 0;
            const newAns = ansRef.current?.map(function (a, i) {
                if (a.name === name) {
                    a.value = val;
                    choiceIndex = i;
                }
                return a;
            });
            setAnswers(newAns);

            await fetchProductOptions(newAns, choiceIndex);
        },
        [fetchProductOptions]
    );

    const addToCart = useCallback(
        (payload: FinalVariant) => {
            let constrainedQty = payload.qty;

            cart.forEach((item) => {
                const maxQty = availableQty || 0;
                const newQty = item.qty + payload.qty;

                if (item?.finalVariant?.id === payload?.finalVariant?.id) {
                    if (newQty > maxQty && maxQty !== 0) {
                        constrainedQty = maxQty - item.qty;
                        dispatch(
                            addNotification({
                                message: "alerts:cart-max-quantity-reached",
                                type: NotificationType.Info,
                                class: "shopping"
                            })
                        );
                    }
                }
            });

            dispatch(
                addToCartAction({
                    finalVariant: payload.finalVariant,
                    qty: constrainedQty,
                    title: payload.title
                })
            );

            dispatch(changeScreen(ShoppingViews.Products));
        },
        [cart, dispatch, availableQty]
    );

    const goToCheckout = useCallback(async () => {
        let newWindow = window.open();
        await liveCommentsReceiver
            .getCheckout(
                {},
                {
                    BroadcastId: broadcastId,
                    FinalVariants: cart.map((p) => ({
                        "FinalVariantId": p.finalVariant?.id,
                        "Qty": p.qty
                    })),
                    "ShopId": shopId
                }
            )
            .then((response) => {
                newWindow!.location = response.url;
            });
    }, [broadcastId, shopId, cart]);

    const closePip = useCallback(async () => {
        if (document.pictureInPictureElement != null) {
            await document.exitPictureInPicture();
        }
    }, []);

    const subTotal = useMemo(() => {
        return cart
            ?.reduce((accumulator, obj) => {
                return (
                    accumulator +
                    obj.qty * Number(obj.finalVariant?.priceV2?.amount)
                );
            }, 0)
            .toLocaleString(undefined, {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
    }, [cart]);

    const checkoutConfirmation = useCallback(
        async (skipConfirmation?: boolean) => {
            trackEvent(
                "Checkout Selected",
                {
                    cart: cart,
                    productCount: cart.length,
                    subTotal: subTotal
                },
                false,
                true
            );

            if (skipConfirmation || !document.pictureInPictureEnabled) {
                goToCheckout();
            } else {
                video?.requestPictureInPicture();
                dispatch(
                    openConfirmation({
                        titleText: "Ready to check out?",
                        message:
                            "Don't worry — you can still watch the video while you finalize your purchase.",
                        confirmText: "Continue",
                        cancelText: "Never Mind",
                        onSuccess: () => {
                            goToCheckout();
                        },
                        onCancel: () => {
                            closePip();
                        }
                    })
                );
            }
        },
        [dispatch, goToCheckout, video, closePip, cart, trackEvent, subTotal]
    );

    return {
        onOptionChange,
        availableOptions,
        availableQty,
        answers,
        response,
        ansRef,
        addToCart,
        checkoutConfirmation,
        subTotal
    };
};
