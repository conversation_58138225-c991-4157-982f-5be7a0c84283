import { eventBus } from "helpers/eventBus";
import { useCallback } from "react";
import { isMobile } from "react-device-detect";
import { useSelector } from "react-redux";
import { RootState } from "store/reducers";

export const usePlayPause = (video: HTMLVideoElement) => {
    const { isInitialPlay, buffering } = useSelector(
        (s: RootState) => s.videoSession
    );

    const handlePlayPause = useCallback(
        (action: "play" | "pause") => {
            if (isInitialPlay && buffering && !isMobile) return;

            if (action === "play") {
                if (video?.paused) {
                    try {
                        video?.play();
                        eventBus.emit("playVideo");
                    } catch (err) {
                        if (
                            !(err instanceof DOMException) ||
                            err.message.indexOf(
                                "The play() request was interrupted"
                            ) === -1
                        ) {
                            throw err;
                        }
                    }
                }
            } else {
                if (video && !video.paused) {
                    video.pause();
                    eventBus.emit("pauseVideo");
                }
            }
        },
        [buffering, isInitialPlay, video]
    );

    const playVideo = useCallback(
        () => handlePlayPause("play"),
        [handlePlayPause]
    );

    const pauseVideo = useCallback(
        () => handlePlayPause("pause"),
        [handlePlayPause]
    );

    const togglePlayPause = useCallback(() => {
        return handlePlayPause(video?.paused ? "play" : "pause");
    }, [handlePlayPause, video?.paused]);

    return {
        playVideo,
        pauseVideo,
        togglePlayPause
    };
};
