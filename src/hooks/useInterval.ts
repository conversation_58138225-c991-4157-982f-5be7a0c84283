import { useEffect, useRef } from "react";

export function useInterval(callback: () => void, delay: number | null) {
    const intervalId = useRef<NodeJS.Timeout | null>(null);
    const savedCallback = useRef<() => void>(callback);

    useEffect(() => {
        savedCallback.current = callback;
    }, [callback]);

    useEffect(() => {
        function tick() {
            savedCallback.current();
        }
        if (delay !== null) {
            intervalId.current = setInterval(tick, delay);
        }
        return () => clearInterval(intervalId.current || undefined);
    }, [delay]);

    return intervalId.current;
}
