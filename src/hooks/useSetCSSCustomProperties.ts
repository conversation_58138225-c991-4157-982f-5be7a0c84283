import { useCallback, useEffect, useMemo } from "react";
import { useSelector } from "react-redux";
import { RootState } from "../store/reducers";
import {
    getContrastColor,
    getContrastedColorHover
} from "helpers/colorHelpers";
import { hexToRgb, pSBC } from "helpers/colorHelper";
import useCollectionWithVideos from "./useCollectionWithVideos";
import { IFrameType } from "store/VideoSession/types";

export const useSetCSSCustomProperties = () => {
    const { video, isExpanded, isToolDrawerActive, iframeType } = useSelector(
        (s: RootState) => s.videoSession
    );

    const { collection } = useCollectionWithVideos();
    const { configuredCatalogId, catalog } = useSelector(
        (s: RootState) => s.catalogState
    );

    const embed = useMemo(() => {
        return configuredCatalogId ? catalog : collection;
    }, [catalog, collection, configuredCatalogId]);

    const handleResize = useCallback(() => {
        document.documentElement.style.setProperty(
            "--video-player-height",
            video?.offsetHeight ? `${video.offsetHeight}px` : "0px"
        );
        document.documentElement.style.setProperty(
            "--video-player-width",
            video?.offsetWidth ? `${video.offsetWidth}px` : "0px"
        );
    }, [video]);

    const setBrandProfile = useCallback(() => {
        const primaryColor =
            (configuredCatalogId
                ? catalog?.details?.buttonBackgroundsLinksColor
                : collection?.details?.primaryColor) ?? "#000000";
        const secondaryColor =
            (configuredCatalogId
                ? catalog?.details?.buttonTextColor
                : collection?.details?.secondaryColor) ?? "#FFFFFF";
        const primaryContrastColor = getContrastColor(primaryColor);
        const primaryContrastColorInversed =
            getContrastColor(primaryContrastColor);
        const secondaryContrastColor = getContrastColor(secondaryColor);
        const secondaryContrastColorInversed = getContrastColor(
            secondaryContrastColor
        );
        const embedBackgroundContrastColor = getContrastColor(
            embed?.details?.embedBackgroundColor ?? "#FFFFFF"
        );
        const embedTextContrastColor = getContrastColor(
            embed?.details?.embedTextColor ?? "#000000"
        );
        const interactivePanelBackgroundContrastColor = getContrastColor(
            embed?.details?.interactivePanelBackgroundColor ?? "#FFFFFF"
        );
        const interactivePanelTextContrastColor = getContrastColor(
            embed?.details?.interactivePanelTextColor ?? "#000000"
        );
        const upcomingTextContrastColor = getContrastColor(
            embed?.details?.upcomingTextColor ?? "#000000"
        );
        const root = document.documentElement;

        root.style.setProperty("--brand-color-primary", primaryColor);
        root.style.setProperty(
            "--player-controls-color",
            embed?.details?.playerControlsColor ?? "#FFFFFF"
        );
        root.style.setProperty(
            "--player-controls-color-rgb",
            hexToRgb(embed?.details?.playerControlsColor ?? "#000000")
        );
        root.style.setProperty(
            "--brand-color-primary-lighter",
            pSBC(0.4, primaryColor, false, true)
        );
        root.style.setProperty(
            "--brand-color-primary-light",
            pSBC(0.2, primaryColor, false, true)
        );
        root.style.setProperty(
            "--brand-color-primary-dark",
            pSBC(-0.2, primaryColor, false, true)
        );
        root.style.setProperty(
            "--brand-color-primary-darker",
            pSBC(-0.4, primaryColor, false, true)
        );
        root.style.setProperty(
            "--brand-color-primary-rgb",
            hexToRgb(primaryColor ?? "#000000")
        );
        root.style.setProperty("--brand-color-secondary", secondaryColor);
        root.style.setProperty(
            "--brand-color-secondary-lighter",
            pSBC(0.4, secondaryColor, false, true)
        );
        root.style.setProperty(
            "--brand-color-secondary-light",
            pSBC(0.2, secondaryColor, false, true)
        );
        root.style.setProperty(
            "--brand-color-secondary-dark",
            pSBC(-0.2, secondaryColor, false, true)
        );
        root.style.setProperty(
            "--brand-color-secondary-darker",
            pSBC(-0.4, secondaryColor, false, true)
        );
        root.style.setProperty(
            "--brand-color-secondary-rgb",
            hexToRgb(secondaryColor ?? "#000000")
        );
        root.style.setProperty(
            "--donate-button-color",
            collection?.details?.donateButtonColor ?? "#000000"
        );
        root.style.setProperty(
            "--donate-button-text-color",
            collection?.details?.donateButtonTextColor ?? "#000000"
        );
        root.style.setProperty(
            "--primary-contrast-color-rgb",
            primaryContrastColor
        );
        root.style.setProperty(
            "--primary-contrast-color-inversed-rgb",
            primaryContrastColorInversed
        );
        root.style.setProperty(
            "--primary-contrast-color-inversed-hover-rgb",
            getContrastedColorHover(primaryContrastColorInversed)
        );
        root.style.setProperty(
            "--secondary-contrast-color-rgb",
            secondaryContrastColor
        );
        root.style.setProperty(
            "--secondary-contrast-color-inversed-rgb",
            secondaryContrastColorInversed
        );
        root.style.setProperty(
            "--embed-background-contrast-color-rgb",
            embedBackgroundContrastColor
        );
        root.style.setProperty(
            "--embed-text-contrast-color-rgb",
            embedTextContrastColor
        );
        root.style.setProperty(
            "--interactive-panel-background-contrast-color-rgb",
            interactivePanelBackgroundContrastColor
        );
        root.style.setProperty(
            "--interactive-panel-text-contrast-color-rgb",
            interactivePanelTextContrastColor
        );
        root.style.setProperty(
            "--upcoming-text-contrast-color-rgb",
            upcomingTextContrastColor
        );
        root.style.setProperty(
            "--interactive-panel-text-color-dark",
            pSBC(-0.2, embed?.details?.interactivePanelTextColor, false, true)
        );
        root.style.setProperty(
            "--embed-background-color",
            embed?.details?.embedBackgroundColor ?? "#FFFFFF"
        );
        root.style.setProperty(
            "--embed-background-color-rgb",
            hexToRgb(embed?.details?.embedBackgroundColor ?? "#FFFFFF")
        );
        root.style.setProperty(
            "--embed-text-color",
            embed?.details?.embedTextColor ?? "#000000"
        );
        root.style.setProperty(
            "--embed-text-color-rgb",
            hexToRgb(embed?.details?.embedTextColor ?? "#000000")
        );
        root.style.setProperty(
            "--interactive-panel-background-color",
            embed?.details?.interactivePanelBackgroundColor ?? "#FFFFFF"
        );
        root.style.setProperty(
            "--interactive-panel-background-color-rgb",
            hexToRgb(
                embed?.details?.interactivePanelBackgroundColor ?? "#FFFFFF"
            )
        );
        root.style.setProperty(
            "--interactive-panel-text-color",
            embed?.details?.interactivePanelTextColor ?? "#000000"
        );
        root.style.setProperty(
            "--interactive-panel-text-color-rgb",
            hexToRgb(embed?.details?.interactivePanelTextColor ?? "#000000")
        );
        root.style.setProperty(
            "--upcoming-text-color",
            embed?.details?.upcomingTextColor ?? "#000000"
        );
        root.style.setProperty(
            "--upcoming-text-color-rgb",
            hexToRgb(embed?.details?.upcomingTextColor ?? "#000000")
        );

        if (
            embed?.details?.googleFontFamily &&
            iframeType !== IFrameType.Main
        ) {
            // Set the Google Fonts link dynamically
            const link = document.createElement("link");
            link.rel = "stylesheet";
            link.href = `https://fonts.googleapis.com/css2?family=${embed.details.googleFontFamily.replace(
                " ",
                "+"
            )}&display=swap`;
            root.appendChild(link);

            // Set the CSS custom property for the font family
            root.style.setProperty(
                "--embed-font-family",
                `'${embed.details.googleFontFamily}', 'Noto Sans', sans-serif`
            );
        } else {
            // Remove property to allow for middleware to take precedence
            root.style.removeProperty("--embed-font-family");
        }
    }, [
        embed?.details,
        catalog?.details,
        collection?.details,
        configuredCatalogId,
        iframeType
    ]);

    /** Initialize static custom properties */
    useEffect(() => {
        setBrandProfile();
    }, [setBrandProfile]);

    /** Set state of toolbar */
    useEffect(() => {
        document.documentElement.style.setProperty(
            "--tool-drawer-active",
            isToolDrawerActive ? "1" : "0"
        );
    }, [isToolDrawerActive]);

    /** Set state of isExpanded */
    useEffect(() => {
        document.documentElement.style.setProperty(
            "--is-expanded",
            isExpanded ? "1" : "0"
        );
    }, [isExpanded]);

    /** Add an event listener for window resize events */
    useEffect(() => {
        window.addEventListener("resize", handleResize);
        return () => window.removeEventListener("resize", handleResize);
    }, [handleResize]);

    /** Handle user-driven resize events, set delay to wait for animations to finish */
    useEffect(() => {
        setTimeout(() => {
            handleResize();
        }, 275);
    }, [isExpanded, isToolDrawerActive, handleResize]);
};
