import { useCallback, useEffect, useMemo, useRef } from "react";
import { useSelector } from "react-redux";
import { RootState } from "store/reducers";
import { useSetNewCollectionVideo } from "hooks/useSetNewCollectionVideo";
import { useToggleInteractiveMode } from "hooks/useToggleInteractiveMode";
import useCollectionWithVideos from "hooks/useCollectionWithVideos";
import { IFrameType } from "store/VideoSession/types";

interface VideoHistoryState {
    iframeType: IFrameType;
    videoId: string | undefined;
    catalogId: string | undefined;
    collectionId: string | undefined;
    autoPlayVideo?: boolean | null;
    onDemandPageCount?: number | null;
    upcomingPageCount?: number | null;
}

export const usePlayerHistoryNavigation = () => {
    const { currentCollectionVideo, iframeType, parentFrame } = useSelector(
        (s: RootState) => s.videoSession
    );
    const { toggleInteractive } = useToggleInteractiveMode();
    const setNewCollectionVideo = useSetNewCollectionVideo();
    const {
        collection,
        upcomingVideos,
        onDemandVideos,
        onDemandPageCount,
        upcomingPageCount
    } = useCollectionWithVideos();
    const catalogId = useSelector(
        (s: RootState) => s.catalogState?.catalog?.details?.id
    );

    const isHandlingNavigationRef = useRef(false);

    const autoPlayVideo = useMemo<boolean>(
        () =>
            currentCollectionVideo?.isEntitled ||
            collection?.details?.idleBroadcastId ===
                currentCollectionVideo?.details?.id,
        [
            currentCollectionVideo?.isEntitled,
            collection?.details?.idleBroadcastId,
            currentCollectionVideo?.details?.id
        ]
    );

    const handlePopState = useCallback(
        (event: PopStateEvent) => {
            isHandlingNavigationRef.current = true;
            const stateExists =
                event.state !== null && event.state !== undefined;

            // Get target values from history state
            const targetVideoId = stateExists ? event.state.videoId : null;
            const targetVideo = [...onDemandVideos, ...upcomingVideos].find(
                (video) => video?.details?.id === targetVideoId
            );
            const targetIframeType = stateExists
                ? event.state.iframeType
                : IFrameType.Main;
            const targetCatalogId = stateExists ? event.state.catalogId : null;
            const targetCollectionId = stateExists
                ? event.state.collectionId
                : null;
            const targetAutoPlayVideo = stateExists
                ? event.state.autoPlayVideo
                : null;
            const targetUpcomingPageCount = stateExists
                ? event.state.upcomingPageCount ?? 1
                : 1;
            const targetOnDemandPageCount = stateExists
                ? event.state.onDemandPageCount ?? 1
                : 1;

            if (targetIframeType === IFrameType.AuxPlayer) {
                // no-op if the player is already open
                parentFrame?.openPlayer(
                    targetCatalogId,
                    targetCollectionId,
                    targetUpcomingPageCount,
                    targetOnDemandPageCount,
                    targetVideoId,
                    targetAutoPlayVideo
                );

                // Always set the new video when in Aux mode and target video exists
                if (targetVideo) {
                    setNewCollectionVideo(targetVideo);
                }
            } else if (targetIframeType === IFrameType.Main) {
                // Close the player
                parentFrame?.closePlayer();
                toggleInteractive(false);
            }
            // Don't reset the flag immediately
            setTimeout(() => {
                isHandlingNavigationRef.current = false;
            }, 300);
        },
        [
            onDemandVideos,
            parentFrame,
            setNewCollectionVideo,
            toggleInteractive,
            upcomingVideos
        ]
    );

    // Set up the event listener for popstate events
    useEffect(() => {
        window.addEventListener("popstate", handlePopState);
        return () => {
            window.removeEventListener("popstate", handlePopState);
        };
    }, [handlePopState]);

    useEffect(() => {
        if (isHandlingNavigationRef.current || !iframeType) return;

        const currentVideoId = currentCollectionVideo?.details?.id;
        /** Set state.iframetype to main if aux without video because that's invalid
         *  (we are using this to determine when to open and close the player)
         */
        const validIframeType =
            iframeType === IFrameType.AuxPlayer && !currentVideoId
                ? IFrameType.Main
                : iframeType;

        // Create the state object with validated values
        const newState: VideoHistoryState = {
            iframeType: validIframeType,
            videoId:
                validIframeType === IFrameType.AuxPlayer
                    ? currentVideoId
                    : undefined,
            catalogId: catalogId,
            collectionId: collection?.details?.id,
            autoPlayVideo,
            onDemandPageCount,
            upcomingPageCount
        };

        // Special handling for idx:0(bottom of state stack) or malformed state
        const isInvalidCurrentState =
            !history.state ||
            typeof history.state !== "object" ||
            "idx" in history.state;

        // We are only interested in the iframeType and videoId for comparison as the useEffect might add more states than we want to track
        const stateChanged =
            isInvalidCurrentState ||
            history.state.iframeType !== newState.iframeType ||
            history.state.videoId !== newState.videoId;

        if (stateChanged) {
            // For initial state use replaceState, otherwise use pushState
            if (isInvalidCurrentState) {
                history.replaceState(newState, "", window.location.href);
            } else {
                history.pushState(newState, "", window.location.href);
            }
        }
    }, [
        autoPlayVideo,
        catalogId,
        collection?.details?.id,
        currentCollectionVideo,
        iframeType,
        onDemandPageCount,
        upcomingPageCount
    ]);
};
