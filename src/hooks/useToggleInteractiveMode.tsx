import { useRollbar } from "@rollbar/react";
import { usePlayPause } from "hooks/usePlayPause";
import { useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/reducers";
import { addNotification } from "store/notification/slice";
import { NotificationType } from "store/notification/types";
import { AppDispatch } from "store/store";
import { setExpandedState, setIsIframeVisible } from "store/VideoSession/slice";
import { IFrameType } from "store/VideoSession/types";
import useCollectionWithVideos from "./useCollectionWithVideos";
import { eventBus } from "helpers/eventBus";

export const useToggleInteractiveMode = (video?: HTMLVideoElement) => {
    const dispatch = useDispatch<AppDispatch>();
    const rollbar = useRollbar();
    const { pauseVideo } = usePlayPause(video!);

    const { collection } = useCollectionWithVideos();

    const {
        parentFrame,
        iframeType,
        video: storeVideo
    } = useSelector((s: RootState) => s.videoSession);

    const toggleInteractive = useCallback(
        (open: boolean) => {
            if (!collection?.details?.id) {
                rollbar.error(
                    "No video player ID found in useToggleInteractiveMode"
                );
                return;
            }

            if (!!parentFrame) {
                if (
                    iframeType === IFrameType.Main &&
                    collection?.details?.embeddedDisplay === "DefaultThumbnail"
                ) {
                    // We are in a legacy "thumbnail" mode embed. In this instance, we will
                    // expand the player iframe instead of loading the player iframe
                    eventBus.emit("toggleInteractiveMode");
                    dispatch(setExpandedState(open));
                    if (open) {
                        parentFrame.expand();
                    } else {
                        parentFrame.collapse(
                            collection?.details?.aspectRatio,
                            collection?.details?.enableMaxEmbedWidth
                                ? collection?.details?.maxEmbedWidth
                                : undefined
                        );
                    }
                } else {
                    // We are in a new catalog embed configuration. In this instance, we will
                    // we will only ever be "closing" the player iframe and never opening it
                    if (!open) {
                        pauseVideo();
                        dispatch(setIsIframeVisible(false));
                        parentFrame.closePlayer();
                    } else {
                        rollbar.error(
                            "Attempted to open player from player iframe"
                        );
                    }
                }
            } else {
                if (video) {
                    pauseVideo();
                } else if (storeVideo) {
                    storeVideo.pause();
                }

                const watchUrl = `${window.location.protocol}//${window.location.host}/watch?p=${collection?.details?.id}`;
                let windowOpenResponse: Window | null;
                if ((windowOpenResponse = window.open(watchUrl, "_blank"))) {
                    windowOpenResponse.focus();
                } else {
                    // Show notification if window.open fails
                    try {
                        dispatch(
                            addNotification({
                                class: "global",
                                message: "alerts:popup-blocked",
                                type: NotificationType.Info
                            })
                        );
                    } catch (e) {
                        rollbar.error(
                            "Failed to show notification for popup blocked: dispatch(addNotification) failed"
                        );
                    }
                }
            }
        },
        [
            parentFrame,
            video,
            storeVideo,
            pauseVideo,
            rollbar,
            collection?.details?.id,
            collection?.details?.embeddedDisplay,
            collection?.details?.aspectRatio,
            collection?.details?.enableMaxEmbedWidth,
            collection?.details?.maxEmbedWidth,
            iframeType,
            dispatch
        ]
    );

    return {
        toggleInteractive
    };
};
