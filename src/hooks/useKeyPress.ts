import { useEffect } from "react";

export interface UseKeyPressOptions {
    disabled?: boolean;
}

export const useKeyPress = (
    targetKey: string,
    action: (event: KeyboardEvent) => void,
    { disabled }: UseKeyPressOptions = { disabled: false }
) => {
    useEffect(() => {
        function downHandler(event: KeyboardEvent) {
            if (event.key === targetKey && !disabled) {
                action(event);
            }
        }

        document.addEventListener("keydown", downHandler);

        return () => {
            document.removeEventListener("keydown", downHandler);
        };
    }, [targetKey, action, disabled]);
};
