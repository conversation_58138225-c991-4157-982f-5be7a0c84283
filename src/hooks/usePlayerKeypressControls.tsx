import { useEffect, useCallback, useRef } from "react";
import {
    setPlayingState,
    setVolume,
    setVolumeMuted,
    setVolumeSliderVisible
} from "store/VideoSession/slice";
import { useKeyPress } from "./useKeyPress";
import { useDispatch } from "react-redux";
import { roundTo } from "helpers/numbers";

export interface UsePlayerKeypressControlsOptions {
    toggleCaptions?: () => void;
    disabled?: boolean;
}

export const usePlayerKeypressControls = (
    videoToUse: HTMLMediaElement | null | undefined,
    { toggleCaptions, disabled }: UsePlayerKeypressControlsOptions = {
        toggleCaptions: () => {},
        disabled: false
    }
) => {
    const dispatch = useDispatch();

    useEffect(() => {
        videoToUse?.focus();
    }, [videoToUse]);

    const togglePlayback = useCallback(
        (event: KeyboardEvent) => {
            if (videoToUse) {
                event.preventDefault();
                if (videoToUse.paused) {
                    videoToUse.play();
                    dispatch(setPlayingState(true));
                } else {
                    videoToUse.pause();
                    dispatch(setPlayingState(false));
                }
            }
        },
        [videoToUse, dispatch]
    );

    useKeyPress(
        " ", // Space key
        togglePlayback,
        { disabled }
    );

    useKeyPress("k", togglePlayback, { disabled });

    const volumeRef = useRef<number>();
    const hideVolumeSliderTimeout = useRef<NodeJS.Timeout | null>(null);
    const showVolumeSlider = useCallback(() => {
        if (videoToUse) {
            if (hideVolumeSliderTimeout.current) {
                clearTimeout(hideVolumeSliderTimeout.current);
                hideVolumeSliderTimeout.current = null;
            }

            dispatch(setVolumeSliderVisible(true));

            hideVolumeSliderTimeout.current = setTimeout(() => {
                dispatch(setVolumeSliderVisible(false));
                hideVolumeSliderTimeout.current = null;
            }, 2000);
        }
    }, [dispatch, videoToUse]);

    const toggleMute = useCallback(() => {
        if (videoToUse) {
            if (videoToUse.volume > 0) {
                volumeRef.current = videoToUse.volume;
                videoToUse.volume = 0;
                dispatch(setVolumeMuted(true));
            } else {
                videoToUse.volume = volumeRef.current ?? 1;
                dispatch(setVolumeMuted(false));
            }
        }
    }, [dispatch, videoToUse]);

    useKeyPress("m", toggleMute, { disabled });

    const scrubForward = useCallback(() => {
        if (videoToUse) {
            videoToUse.currentTime += 5; // Scrub forward 5 seconds
        }
    }, [videoToUse]);

    useKeyPress("ArrowRight", scrubForward, { disabled });

    const scrubBackward = useCallback(() => {
        if (videoToUse) {
            videoToUse.currentTime -= 5; // Scrub backward 5 seconds
        }
    }, [videoToUse]);

    useKeyPress("ArrowLeft", scrubBackward, { disabled });

    const increaseVolume = useCallback(() => {
        showVolumeSlider();

        if (videoToUse && videoToUse.volume < 1) {
            videoToUse.volume = roundTo(
                Math.min(videoToUse.volume + 0.1, 1),
                1
            );
            dispatch(setVolume(videoToUse.volume));
            dispatch(setVolumeMuted(false));
        }
    }, [showVolumeSlider, videoToUse, dispatch]);

    useKeyPress("ArrowUp", increaseVolume, { disabled });

    const decreaseVolume = useCallback(() => {
        showVolumeSlider();

        if (videoToUse && videoToUse.volume > 0) {
            videoToUse.volume = roundTo(
                Math.max(videoToUse.volume - 0.1, 0),
                1
            );
            dispatch(setVolume(videoToUse.volume));
            if (videoToUse.volume === 0) {
                dispatch(setVolumeMuted(true));
            }
        }
    }, [showVolumeSlider, videoToUse, dispatch]);

    useKeyPress("ArrowDown", decreaseVolume, { disabled });

    const toggleFullscreen = useCallback(() => {
        if (videoToUse) {
            if (document.fullscreenElement) {
                document.exitFullscreen();
            } else {
                videoToUse.requestFullscreen();
            }
        }
    }, [videoToUse]);

    useKeyPress("f", toggleFullscreen, { disabled });

    useKeyPress("c", toggleCaptions ?? (() => {}), { disabled });
};
