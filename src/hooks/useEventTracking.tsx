import {
    getAnonymousId,
    pathToPage,
    trackSegmentEvent
} from "helpers/analyticsHelpers";
import { useCallback, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { useLocation } from "react-router-dom";
import { RootState } from "store/reducers";
import useCollectionWithVideos from "./useCollectionWithVideos";

export const useEventTracking = (propertiesData?: object) => {
    const [singularEvents, setSingularEvents] = useState<string[]>([]);

    const location = useLocation();
    const { collection } = useCollectionWithVideos();
    const {
        sessionId,
        video,
        currentCollectionVideo,
        isMicrosite,
        isFromShare
    } = useSelector((s: RootState) => s.videoSession);
    const { configuredCatalogId } = useSelector(
        (s: RootState) => s.catalogState
    );
    const { details: playerCreatorDetails } = useSelector(
        (s: RootState) => s.playerCreator
    );

    const defaultProperties = useMemo(
        () => ({
            broadcastId: currentCollectionVideo?.broadcast?.details?.id,
            broadcastType:
                currentCollectionVideo?.broadcast?.details?.broadcastType,
            catalogId: configuredCatalogId,
            videoPlayerId: collection?.details?.id,
            videoPlayerPlaylistId:
                currentCollectionVideo?.details?.videoPlayerPlaylistId,
            videoPlayerPlaylistBroadcastId: currentCollectionVideo?.details?.id,
            userId: playerCreatorDetails?.userId,
            stripeAccountId: playerCreatorDetails?.stripeAccountId,
            videoTitle: currentCollectionVideo?.broadcast?.details?.title,
            isGated: currentCollectionVideo?.isGated,
            isEntitled: currentCollectionVideo?.isEntitled,
            livestream: !!currentCollectionVideo
                ? currentCollectionVideo?.broadcast?.details
                      ?.broadcastStatus === "Active"
                : undefined,
            viewSource: isMicrosite
                ? "Microsite"
                : pathToPage(location?.pathname),
            viewerId: getAnonymousId(),
            ...propertiesData
        }),
        [
            currentCollectionVideo,
            collection,
            location,
            propertiesData,
            isMicrosite,
            configuredCatalogId,
            playerCreatorDetails
        ]
    );

    const trackEvent = useCallback(
        async (
            name: string,
            properties?: any,
            singular: boolean = false,
            includeSession: boolean = false
        ) => {
            if (singular && singularEvents.includes(name)) return;

            const sessionProps = includeSession
                ? {
                      session_id: sessionId,
                      position: video?.currentTime,
                      isFromShare
                  }
                : {};

            trackSegmentEvent(name, {
                ...defaultProperties,
                ...properties,
                ...sessionProps
            });
            if (singular) setSingularEvents([...singularEvents, name]);
        },
        [defaultProperties, singularEvents, sessionId, video, isFromShare]
    );

    return {
        trackEvent
    };
};
