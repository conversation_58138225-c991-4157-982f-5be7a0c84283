import { useState } from "react";

/**
 * A custom hook that provides a way to trigger re-renders for hooks that take deps - like useMemo or useEffect.
 * This is particularly useful when you want to force an update in a component
 * that doesn't rely on changing props or state.
 *
 * @returns {Object} An object containing:
 *   - update (number): The state that is updated. Use this in the dependency array of the hook
 *     you want to trigger an update for.
 *   - trigger (function): A callback that, when called, will increment the update state
 *     and prompt a re-render.
 */

export const useUpdateTrigger = (): {
    /** The state that is updated. Put this in the deps of the hook you want to trigger an update for. */
    updateKey: number;
    /** A callback that triggers the update */
    updateTrigger: () => void;
} => {
    const [updateKey, setUpdateKey] = useState<number>(0);

    return { updateKey, updateTrigger: () => setUpdateKey((s) => s + 1) };
};
