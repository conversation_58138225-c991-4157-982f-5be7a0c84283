import { useCallback } from "react";

export interface useLinkNavigationOptions {
    link: string;
}

export const useLinkNavigation = ({ link }: useLinkNavigationOptions) => {
    return useCallback(() => {
        // add // to the url if protocol is missing
        if (!/^http[s]?:\/\//.test(link)) {
            window.open("//" + link, "_blank");
        } else {
            window.open(link, "_blank");
        }
    }, [link]);
};
