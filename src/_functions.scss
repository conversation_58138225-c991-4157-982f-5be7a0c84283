@use "sass:math";

//@description Remove units for a string
@function strip-unit($number) {
    @if type-of($number) == "number" and not unitless($number) {
        @return math.div($number, ($number * 0 + 1));
    }

    @return $number;
}

//@description Convert unitless number (px) to rem
@function rem($number) {
    $base: 16;
    $val: math.div(strip-unit($number), $base);
    $return: $val * 1rem;
    @return $return;
}
