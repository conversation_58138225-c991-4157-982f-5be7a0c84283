interface Window {
    WebKitPlaybackTargetAvailabilityEvent: typeof WebKitPlaybackTargetAvailabilityEvent;
}

interface WebKitPlaybackTargetAvailabilityEvent extends Event {
    availability: "available" | "not-available";
}

interface HTMLVideoElement {
    /**
     * Displays the playback target picker, allowing users to select AirPlay devices.
     */
    webkitShowPlaybackTargetPicker(): void;
}

declare module "shaka-player/dist/shaka-player.compiled" {
    export = shaka;
}
