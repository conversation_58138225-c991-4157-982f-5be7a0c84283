/** The ordinal rank. */
export interface OrdinalRank {
    /** Gets or Sets the rank. */
    Rank?: string | undefined;
    /** Gets or Sets the ordinal. */
    Ordinal?: string | undefined;
}
interface IOrdinal {
    OrdinalRank?: OrdinalRank;
}

export const ordinalSort = <T extends IOrdinal>(array: T[]): T[] => {
    return array.sort((a, b) => {
        return (b?.OrdinalRank?.Rank ?? "1a") > (a?.OrdinalRank?.Rank ?? "1a")
            ? 1
            : -1;
    });
};
