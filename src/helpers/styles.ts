export const getStyle = (element: HTMLElement, styleProp: string) => {
    if (element) {
        const value = document.defaultView
            ?.getComputedStyle(element, null)
            .getPropertyValue(styleProp);

        if (typeof value === "number") return parseInt(value);
    }
};

export type ZIndexValue = number | "auto" | "inherit" | "";

export const getZIndex = (element: HTMLElement | null): number => {
    if (!element) return 0;

    let index: ZIndexValue = (getStyle(element, "z-index") ??
        "") as ZIndexValue;

    if (!index || index === "auto" || index === "inherit") {
        index = getZIndex(element.parentElement);
    }

    return index;
};

export const stripPx = (value: string): number =>
    parseFloat(value.replace("px", "") ?? 0);

export const appendPx = (value: number): string => `${value}px`;

export const calcPx = (
    a: string,
    b: string,
    operation: (a: number, b: number) => number
): string => appendPx(operation(stripPx(a) ?? 0, stripPx(b) ?? 0));
