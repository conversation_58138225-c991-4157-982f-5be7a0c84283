import { hexToRgb } from "helpers/colorHelper";

interface RGBColor {
    red: number;
    green: number;
    blue: number;
}

export const getContrastColor = (bgColor: RGBColor | string) => {
    const transformHexToRGB = (hex: string): RGBColor => {
        const rgbString = hexToRgb(hex);
        return transformCSVtoColor(rgbString);
    };

    const transformCSVtoColor = (rgbCSV: string): RGBColor => {
        return rgbCSV.split(",").reduce((memo, currentValue, currentIndex) => {
            switch (currentIndex) {
                case 0:
                    return {
                        ...memo,
                        red: Number(currentValue)
                    };
                case 1:
                    return {
                        ...memo,
                        green: Number(currentValue)
                    };
                case 2:
                    return {
                        ...memo,
                        blue: Number(currentValue)
                    };
                default:
                    return memo;
            }
        }, {} as RGBColor);
    };

    const getLuminosity = (rgb: RGBColor): number => {
        // https://www.w3.org/TR/WCAG20/#relativeluminancedef
        return Object.entries(rgb)
            .map(([color, value]) => {
                const sRGBValue = value / 255.0;

                return {
                    color,
                    value:
                        sRGBValue <= 0.03928
                            ? sRGBValue / 12.92
                            : ((sRGBValue + 0.055) / 1.055) ** 2.4
                };
            })
            .reduce((memo, currentColor) => {
                switch (currentColor.color) {
                    case "red":
                        return memo + currentColor.value * 0.2126;
                    case "green":
                        return memo + currentColor.value * 0.7152;
                    case "blue":
                        return memo + currentColor.value * 0.0722;
                    default:
                        return memo;
                }
            }, 0);
    };

    const getContrastedColor = (
        luminosity: number
    ): "255,255,255" | "0,0,0" => {
        // https://stackoverflow.com/a/3943023
        return luminosity > 0.179 ? "0,0,0" : "255,255,255";
    };

    const isRGB = (color: RGBColor | string): color is RGBColor =>
        !!(color as RGBColor)?.red || (color as RGBColor)?.red === 0;
    const isRGBCSV = (color: string) =>
        color.match(/(\d{1,3}),(\d{1,3}),(\d{1,3})/gi);

    if (isRGB(bgColor)) {
        return getContrastedColor(getLuminosity(bgColor));
    } else if (isRGBCSV(bgColor)) {
        return getContrastedColor(getLuminosity(transformCSVtoColor(bgColor)));
    } else {
        return getContrastedColor(getLuminosity(transformHexToRGB(bgColor)));
    }
};

export const getContrastedColorHover = (
    color: "255,255,255" | "0,0,0"
): string => {
    return color === "0,0,0" ? "48,49,52" : "241,243,244";
};
