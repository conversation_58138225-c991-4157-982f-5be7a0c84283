export const timeSince = (date: Date | undefined) => {
    if (!date) return "";

    let seconds = Math.max(0, Math.floor((Date.now() - date.getTime()) / 1000));

    let interval = seconds / 31536000;

    if (interval > 1) {
        const years = Math.floor(interval);
        return years + ` year${years > 1 ? "s" : ""} ago`;
    }
    interval = seconds / 2592000;
    if (interval > 1) {
        const months = Math.floor(interval);
        return months + ` month${months > 1 ? "s" : ""} ago`;
    }
    interval = seconds / 86400;
    if (interval > 1) {
        const days = Math.floor(interval);
        return days + ` day${days > 1 ? "s" : ""} ago`;
    }
    interval = seconds / 3600;
    if (interval > 1) {
        const hours = Math.floor(interval);
        return hours + ` hour${hours > 1 ? "s" : ""} ago`;
    }
    interval = seconds / 60;
    if (interval > 1) {
        const minutes = Math.floor(interval);
        return minutes + ` minute${minutes > 1 ? "s" : ""} ago`;
    }
    return Math.floor(seconds) + " seconds ago";
};

export default timeSince;
