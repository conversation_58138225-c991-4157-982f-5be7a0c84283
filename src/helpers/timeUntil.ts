export const timeUntil = (date: Date | undefined) => {
    if (!date) return "";

    let seconds = Math.max(0, Math.floor((date.getTime() - Date.now()) / 1000));

    let interval = seconds / 31536000;

    if (interval > 1) {
        const years = Math.floor(interval);
        return `in ${years} year${years > 1 ? "s" : ""}`;
    }
    interval = seconds / 2592000;
    if (interval > 1) {
        const months = Math.floor(interval);
        return `in ${months} month${months > 1 ? "s" : ""}`;
    }
    interval = seconds / 86400;
    if (interval > 1) {
        const days = Math.floor(interval);
        return `in ${days} day${days > 1 ? "s" : ""}`;
    }
    interval = seconds / 3600;
    if (interval > 1) {
        const hours = Math.floor(interval);
        return `in ${hours} hour${hours > 1 ? "s" : ""}`;
    }
    interval = seconds / 60;
    if (interval > 1) {
        const minutes = Math.floor(interval);
        return `in ${minutes} minute${minutes > 1 ? "s" : ""}`;
    }
    return `in ${Math.floor(seconds)} seconds`;
};

export default timeUntil;
