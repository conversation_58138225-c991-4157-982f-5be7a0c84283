/**
 * A helper to find the greater of two numbers
 * @param a a number
 * @param b a number
 * @returns the greater of the two numbers
 */
export const greater = (a: number, b: number) => (a > b ? a : b);

/**
 * A helper to find the least of two numbers
 * @param a a number
 * @param b a number
 * @returns the lesser of the two numbers
 */
export const lesser = (a: number, b: number) => (a < b ? a : b);

/**
 * A helper to round a number to a specified precision
 * @param value the number to round
 * @param precision the number of decimal places to round to
 * @returns the rounded number
 */
export const roundTo = (value: number, precision: number) => {
    const factor = Math.pow(10, precision);
    return Math.round(value * factor) / factor;
};
