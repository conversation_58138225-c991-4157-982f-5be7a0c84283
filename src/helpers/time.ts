import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
dayjs.extend(duration);
dayjs.extend(utc);
dayjs.extend(timezone);

export const formatVideoDuration = (seconds: number) => {
    return seconds > 60 * 60
        ? dayjs.duration(seconds, "seconds").format("HH:mm:ss")
        : dayjs.duration(seconds, "seconds").format("m:ss");
};

/**
 * Determines if a given date is in the past.
 *
 * @param {string | Date | undefined} date - The date to check, in string format.
 * @returns {boolean} - True if the date is in the past, false otherwise.
 */
export const isInPast = (date: string | Date | undefined): boolean => {
    if (date) {
        return dayjs.utc(date).isBefore(dayjs.utc());
    }
    return false;
};

/**
 * Determines if a given date is in the future.
 *
 * @param {string | Date | undefined} date - The date to check, in string format.
 * @returns {boolean} - True if the date is in the future, false otherwise.
 */
export const isInFuture = (date: string | Date | undefined): boolean => {
    if (date) {
        return dayjs.utc(date).isAfter(dayjs.utc());
    }
    return false;
};

/**
 * Determines if a given date is the same as the current day.
 *
 * @param {string | Date | undefined} date - The date to check, in string format.
 * @returns {boolean} - True if the date is the same as the current day, false otherwise.
 */
export const isDayOf = (date: string | Date | undefined): boolean => {
    if (date) {
        return dayjs
            .utc()
            .startOf("day")
            .isSame(dayjs.utc(date).startOf("day"));
    }
    return false;
};

/**
 * Converts a given date to the local time zone and formats it.
 *
 * @param {string | Date | undefined} date - The date to convert, in string format.
 * @param {string} format - The format to use for the output date string.
 * @returns {string} - The date string in the local time zone and the specified format.
 */
export const localizeTime = (
    date: string | Date | undefined,
    format: string
): string => {
    if (date) {
        return dayjs(date)
            .tz(Intl.DateTimeFormat().resolvedOptions().timeZone)
            .format(format);
    }
    return "";
};

/**
 * Gets the time difference of a given date to now.
 *
 * @param {string | Date | undefined} date - The date to check, in string format.
 * @returns {number} - The number of milliseconds between the date and now.
 */
export const timeDiff = (date: string | Date | undefined | null): number => {
    if (date) {
        return dayjs.utc(date).diff(dayjs.utc());
    }
    return 0;
};
