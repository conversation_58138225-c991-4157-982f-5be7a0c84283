import {
    ProductVariant,
    ShopifyInfoProduct,
    TruthyProductOption,
    VariantOption
} from "../store/shopping/types";

export interface IFetchOptions {
    service?: string;
    resource?: string;
    method?: string;
    body?: any;
    headers?: any;
    fields?: any;
    version?: string;
}

export interface ShopifyProductWithOptions extends ShopifyInfoProduct {
    availableForSale: boolean;
    totalInventory: number;
    variants: ProductVariant[];
}

export interface LCRApiResponse {
    options: TruthyProductOption[];
    finalVariant: ProductVariant;
    answerList: VariantOption[];
    returnMsg: string;
    products: ShopifyProductWithOptions[];
    shopId: string;
}
