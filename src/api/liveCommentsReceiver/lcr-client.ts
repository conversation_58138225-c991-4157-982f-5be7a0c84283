import lcr from "./lcr-helper";

export const liveCommentsReceiver = {
    getProductOptions: (fields: any, body: any) =>
        lcr({
            method: "POST",
            resource: "/api/LiveCommentsFn_HttpGetProductOptions",
            fields,
            body: body
        }),
    getBroadcastProducts: (fields: any) =>
        lcr({
            resource: "/api/LiveCommentsFn_HttpGetBroadcastProducts",
            fields
        }),
    getCheckout: (fields: any, body: any) =>
        lcr({
            method: "POST",
            resource: "/api/LiveCommentsFn_HttpGetCheckout",
            fields,
            body: body
        }),
    getTwilioSyncToken: (fields: any) =>
        lcr({
            resource: "/api/LiveCommentsFn_HttpGetTwilioToken",
            fields
        })
};
