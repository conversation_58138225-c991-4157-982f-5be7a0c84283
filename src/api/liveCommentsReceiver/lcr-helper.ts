import { IFetchOptions } from "../types";

const baseUrl =
    import.meta.env.VITE_ENV === "local" || import.meta.env.VITE_ENV === "dev"
        ? import.meta.env.VITE_LCR_API_URL
        : `${window.location.protocol}//${window.location.host}/lcr-proxy`;

export default async function lcrFetch(options: IFetchOptions, uri = baseUrl) {
    const url = `${uri}${options.resource}?${parseFields(options)}`;

    try {
        const response = await fetch(url, {
            method: options.method || "GET",
            body: JSON.stringify(options.body) || null,
            headers: {
                ...(options.headers || {}),
                "Content-Type": "application/json"
            }
        });

        const body = await handleResponse(response);

        if (response.status >= 400) {
            throw new Error(body?.error?.message);
        }

        return body;
    } catch (e) {
        throw e;
    } finally {
    }
}

async function handleResponse(response: Response) {
    const text = await response.text();

    try {
        let result = null;
        result = text === "" ? null : JSON.parse(text);

        return result;
    } catch (e) {
        throw new Error("Invalid JSON");
    }
}

function parseFields(options: IFetchOptions) {
    let fieldsArray: string[] = [];
    for (let i in options.fields) {
        // if field has value add it to array
        if (options.fields[i]) {
            fieldsArray.push(`${i}=${encodeURIComponent(options.fields[i])}`);
        }
    }
    return fieldsArray.join("&");
}
