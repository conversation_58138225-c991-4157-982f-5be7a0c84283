@import url("https://fonts.googleapis.com/css2?family=Noto+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,400&display=swap");

html,
body {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    font-family: var(--embed-font-family);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow: auto;

    // BASE FONT SIZE FOR SCALING
    font-size: 16px;

    @media screen and (max-width: 668px) and (orientation: landscape) {
        font-size: 14px;
    }

    @media screen and (max-width: 376px) and (orientation: portrait) {
        font-size: 14px;
    }

    @media screen and (min-width: 3839px) {
        font-size: 50px;
    }

    &.disable-transitions * {
        transition: none !important;
    }
}

* {
    box-sizing: border-box;
}

code {
    font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
        monospace;
}

#root {
    height: 100%;
    width: 100%;
    //overflow: hidden;
}

.fixed-aspect-ratio-child {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

button {
    font-family: var(--embed-font-family);

    &:disabled {
        background-color: #c1c7cd; // a fallback value if not explicitly set
        cursor: not-allowed;
    }
    &:hover {
        cursor: pointer;
    }
}

input {
    font-family: var(--embed-font-family);
    font-size: rem(16);

    &:focus-visible {
        outline: none !important;
    }
}

.btn {
    color: #fff;
    padding: rem(10) rem(24);
    border-radius: rem(22);
    font-size: rem(16);
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s ease-in-out;
    border: none;
    font-family: var(--embed-font-family);
    cursor: pointer;

    &.btn-primary {
        background-color: var(--brand-color-primary);
        color: var(--brand-color-secondary);

        &:hover {
            background-color: var(--brand-color-primary-dark);
        }

        &:disabled {
            background-color: rgba(var(--brand-color-primary-rgb), 0.5);
            cursor: unset;
        }
    }

    &.btn-outline {
        border-width: rem(1);
        border-style: solid;
        border-color: var(--brand-color-primary);
        background-color: var(--brand-color-primary);
        color: var(--brand-color-secondary);

        &:hover {
            background-color: var(--brand-color-primary-dark);
            border-color: var(--brand-color-primary-dark);
        }
    }

    &.btn-generic-overlay {
        background-color: rgba(#fff, 0.6);
        color: #000;

        &:hover {
            background-color: rgba(#fff, 1);
        }
    }

    &.btn-secondary {
        background-color: var(--brand-color-secondary);
        color: #000;

        &:hover {
            background-color: var(--brand-color-secondary-dark);
        }
    }

    &.btn-tertiary {
        background-color: var(--brand-color-tertiary);

        &:hover {
            background-color: var(--brand-color-tertiary-dark);
        }
    }

    &.btn-black {
        transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
        background-color: #000;
        color: #fff;
        border: rem(2) solid #000;

        &:hover {
            background-color: #fff;
            color: #000;
        }
    }

    &.btn-text {
        padding: 0;
        border-radius: 0;
        color: var(--brand-color-primary);
        background-color: transparent;
        font-size: rem(14);
        line-height: rem(22);
        transition: 0.2s text-decoration ease-in-out;

        &:hover {
            text-decoration: underline;
        }
    }
}

.badge {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: rem(4) rem(10);
    gap: rem(6);
    border-radius: rem(8);

    font-weight: 400;
    font-size: rem(12);
    line-height: rem(16);
    color: rgba(var(--interactive-panel-background-color-rgb));
    text-transform: capitalize;

    &.badge-out-of-stock {
        background-color: rgba(
            var(--interactive-panel-background-color-rgb),
            0.6
        );
    }
}

.w-100 {
    width: 100%;
}
