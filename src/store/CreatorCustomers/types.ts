import { EntitlementType } from "@switcherstudio/player-api-client";

export interface CreatorCustomersState {
    [creatorProjectId: string]: CreatorCustomerProfile;
}

interface CreatorCustomerProfile {
    ticket: ICreatorCustomerTicket;
    passwordClaims?: PasswordClaim[];
}

export interface ICreatorCustomerTicket {
    access_token?: string;
    token_type?: string;
    /** expires in as milliseconds */
    expires_in?: number;
    refresh_token?: string;
    /** the customer email as userName */
    userName?: string;
    email?: string;
    /** Stripe customer id */
    customerId?: string;
    /** Creator Stripe account id */
    stripeAccountId?: string;
    /** Creator Project id */
    projectId?: string;
    type?: string;
    ".issued"?: string;
    ".expires"?: string;
    "as:client_id"?: string;
}

export interface TokenPayload {
    ticket: ICreatorCustomerTicket;
    creatorProjectId: string;
}

export interface PasswordClaim {
    Password: string;
    EntityId: string;
    EntityType: EntitlementType;
    DateRedeemed: Date;
}
