import { createAsyncThunk } from '@reduxjs/toolkit';
import { chatService } from '../../services/chatService';
import { 
    JoinRoomPayload, 
    SendMessagePayload, 
    ChatUser, 
    ChatRoom, 
    ChatMessage 
} from '../../types/chat';
import { 
    joinRoomStart, 
    joinRoomSuccess, 
    joinRoomFailure,
    sendMessageStart,
    sendMessageSuccess,
    sendMessageFailure,
    setMessages,
    setOnlineUsers,
    addMessage,
    setConnected,
    setError
} from './slice';
import { RootState } from '../reducers';

// Join a chat room
export const joinChatRoom = createAsyncThunk<
    { user: ChatUser; room: ChatRoom },
    JoinRoomPayload,
    { state: RootState }
>(
    'chat/joinRoom',
    async (payload, { dispatch, rejectWithValue }) => {
        try {
            dispatch(joinRoomStart(payload));

            // Join the room and get user info
            const user = await chatService.joinRoom(payload.roomId, payload.username);

            // Create room object (in a real app, you might fetch this from Firebase)
            const room: ChatRoom = {
                id: payload.roomId,
                name: `Video Chat ${payload.roomId}`,
                description: 'Chat for video viewers',
                createdAt: { seconds: Date.now() / 1000, nanoseconds: 0 } as any,
                isActive: true,
                participantCount: 1,
                lastActivity: { seconds: Date.now() / 1000, nanoseconds: 0 } as any
            };

            // Set up message listener
            chatService.subscribeToMessages(payload.roomId, (messages: ChatMessage[]) => {
                dispatch(setMessages(messages));
            });

            // Set up users listener
            chatService.subscribeToUsers((users: ChatUser[]) => {
                dispatch(setOnlineUsers(users));
            });

            dispatch(joinRoomSuccess({ user, room }));
            dispatch(setConnected(true));

            return { user, room };
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to join chat room';
            dispatch(joinRoomFailure(errorMessage));
            return rejectWithValue(errorMessage);
        }
    }
);

// Send a message
export const sendChatMessage = createAsyncThunk<
    void,
    SendMessagePayload,
    { state: RootState }
>(
    'chat/sendMessage',
    async (payload, { dispatch, getState, rejectWithValue }) => {
        try {
            const state = getState();
            const currentUser = state.chat.currentUser;

            if (!currentUser) {
                throw new Error('User not logged in');
            }

            if (!payload.message.trim()) {
                throw new Error('Message cannot be empty');
            }

            // Dispatch optimistic update
            dispatch(sendMessageStart(payload));

            // Send message to Firebase
            await chatService.sendMessage(payload.roomId, payload.message, currentUser);

            dispatch(sendMessageSuccess());
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to send message';
            dispatch(sendMessageFailure(errorMessage));
            return rejectWithValue(errorMessage);
        }
    }
);

// Leave chat room
export const leaveChatRoom = createAsyncThunk<void, void, { state: RootState }>(
    'chat/leaveRoom',
    async (_, { dispatch }) => {
        try {
            await chatService.leaveRoom();
            dispatch(setConnected(false));
        } catch (error) {
            console.error('Error leaving chat room:', error);
            // Still disconnect even if there's an error
            dispatch(setConnected(false));
        }
    }
);

// Update user online status
export const updateUserOnlineStatus = createAsyncThunk<
    void,
    { userId: string; isOnline: boolean },
    { state: RootState }
>(
    'chat/updateUserStatus',
    async ({ userId, isOnline }, { dispatch }) => {
        try {
            await chatService.updateUserStatus(userId, isOnline);
        } catch (error) {
            console.error('Error updating user status:', error);
            dispatch(setError('Failed to update user status'));
        }
    }
);

// Initialize chat connection (called when component mounts)
export const initializeChat = createAsyncThunk<void, string, { state: RootState }>(
    'chat/initialize',
    async (roomId, { dispatch }) => {
        try {
            // Auto-join room with generated username
            await dispatch(joinChatRoom({ roomId }));
        } catch (error) {
            console.error('Error initializing chat:', error);
            dispatch(setError('Failed to initialize chat'));
        }
    }
);

// Cleanup chat (called when component unmounts)
export const cleanupChat = createAsyncThunk<void, void, { state: RootState }>(
    'chat/cleanup',
    async (_, { dispatch }) => {
        try {
            await dispatch(leaveChatRoom());
        } catch (error) {
            console.error('Error cleaning up chat:', error);
        }
    }
);
