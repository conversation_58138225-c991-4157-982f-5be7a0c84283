import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ChatState, ChatMessage, ChatUser, ChatRoom, SendMessagePayload, JoinRoomPayload } from '../../types/chat';

const initialState: ChatState = {
    currentUser: null,
    currentRoom: null,
    messages: [],
    onlineUsers: [],
    isConnected: false,
    isLoading: false,
    error: null
};

const chatSlice = createSlice({
    name: 'chat',
    initialState,
    reducers: {
        // Connection management
        setConnected: (state, action: PayloadAction<boolean>) => {
            state.isConnected = action.payload;
            if (!action.payload) {
                state.error = 'Connection lost';
            } else {
                state.error = null;
            }
        },

        setLoading: (state, action: PayloadAction<boolean>) => {
            state.isLoading = action.payload;
        },

        setError: (state, action: PayloadAction<string | null>) => {
            state.error = action.payload;
            state.isLoading = false;
        },

        // User management
        setCurrentUser: (state, action: PayloadAction<ChatUser | null>) => {
            state.currentUser = action.payload;
        },

        updateUserStatus: (state, action: PayloadAction<{ userId: string; isOnline: boolean }>) => {
            const { userId, isOnline } = action.payload;
            
            // Update current user if it matches
            if (state.currentUser?.id === userId) {
                state.currentUser.isOnline = isOnline;
            }

            // Update in online users list
            const userIndex = state.onlineUsers.findIndex(user => user.id === userId);
            if (userIndex !== -1) {
                if (isOnline) {
                    state.onlineUsers[userIndex].isOnline = isOnline;
                } else {
                    // Remove from online users if offline
                    state.onlineUsers.splice(userIndex, 1);
                }
            }
        },

        setOnlineUsers: (state, action: PayloadAction<ChatUser[]>) => {
            state.onlineUsers = action.payload;
        },

        addOnlineUser: (state, action: PayloadAction<ChatUser>) => {
            const existingUser = state.onlineUsers.find(user => user.id === action.payload.id);
            if (!existingUser) {
                state.onlineUsers.push(action.payload);
            }
        },

        removeOnlineUser: (state, action: PayloadAction<string>) => {
            state.onlineUsers = state.onlineUsers.filter(user => user.id !== action.payload);
        },

        // Room management
        setCurrentRoom: (state, action: PayloadAction<ChatRoom | null>) => {
            state.currentRoom = action.payload;
            // Clear messages when changing rooms
            if (action.payload === null) {
                state.messages = [];
                state.onlineUsers = [];
            }
        },

        // Message management
        setMessages: (state, action: PayloadAction<ChatMessage[]>) => {
            state.messages = action.payload;
        },

        addMessage: (state, action: PayloadAction<ChatMessage>) => {
            // Avoid duplicates
            const existingMessage = state.messages.find(msg => msg.id === action.payload.id);
            if (!existingMessage) {
                state.messages.push(action.payload);
                
                // Keep only the last 100 messages to prevent memory issues
                if (state.messages.length > 100) {
                    state.messages = state.messages.slice(-100);
                }
            }
        },

        clearMessages: (state) => {
            state.messages = [];
        },

        // Action creators for async operations (to be used with thunks)
        joinRoomStart: (state, action: PayloadAction<JoinRoomPayload>) => {
            state.isLoading = true;
            state.error = null;
        },

        joinRoomSuccess: (state, action: PayloadAction<{ user: ChatUser; room: ChatRoom }>) => {
            state.currentUser = action.payload.user;
            state.currentRoom = action.payload.room;
            state.isConnected = true;
            state.isLoading = false;
            state.error = null;
        },

        joinRoomFailure: (state, action: PayloadAction<string>) => {
            state.error = action.payload;
            state.isLoading = false;
            state.isConnected = false;
        },

        sendMessageStart: (state, action: PayloadAction<SendMessagePayload>) => {
            // Optimistically add message to UI (will be replaced by real message from Firebase)
            if (state.currentUser) {
                const optimisticMessage: ChatMessage = {
                    id: `temp_${Date.now()}`,
                    userId: state.currentUser.id,
                    username: state.currentUser.username,
                    message: action.payload.message,
                    timestamp: { seconds: Date.now() / 1000, nanoseconds: 0 } as any,
                    roomId: action.payload.roomId,
                    userColor: state.currentUser.color
                };
                state.messages.push(optimisticMessage);
            }
        },

        sendMessageSuccess: (state) => {
            // Remove optimistic message when real message arrives
            state.messages = state.messages.filter(msg => !msg.id.startsWith('temp_'));
        },

        sendMessageFailure: (state, action: PayloadAction<string>) => {
            state.error = action.payload;
            // Remove optimistic message on failure
            state.messages = state.messages.filter(msg => !msg.id.startsWith('temp_'));
        },

        leaveRoom: (state) => {
            state.currentRoom = null;
            state.messages = [];
            state.onlineUsers = [];
            state.isConnected = false;
            state.error = null;
        },

        // Reset entire chat state
        resetChat: () => initialState
    }
});

export const {
    setConnected,
    setLoading,
    setError,
    setCurrentUser,
    updateUserStatus,
    setOnlineUsers,
    addOnlineUser,
    removeOnlineUser,
    setCurrentRoom,
    setMessages,
    addMessage,
    clearMessages,
    joinRoomStart,
    joinRoomSuccess,
    joinRoomFailure,
    sendMessageStart,
    sendMessageSuccess,
    sendMessageFailure,
    leaveRoom,
    resetChat
} = chatSlice.actions;

export default chatSlice.reducer;
