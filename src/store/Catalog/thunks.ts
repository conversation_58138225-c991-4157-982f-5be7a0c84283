import { createAsyncThunk } from "@reduxjs/toolkit";
import {
    BroadcastResponse,
    CatalogResponse,
    CatalogSearchPagedResponse,
    CollectionResponse,
    CollectionsPagedResponse,
    CollectionVideoResponse,
    CollectionVideosPagedResponse
} from "@switcherstudio/player-api-client";
import { RootState } from "store/reducers";
import { AppDispatch } from "store/store";
import { client } from "api/playerClient";
import {
    DEFAULT_COLLECTION_VIDEO_PAGE_SIZE,
    DEFAULT_COLLECTIONS_PAGE_SIZE,
    DEFAULT_LARGE_GRID_VIDEO_PAGE_SIZE
} from "./slice";
import { IFrameType } from "store/VideoSession/types";
import { isInPast } from "helpers/time";

/**
 * Fetches the catalog data from the API and optionally loads associated collections.
 */
export const getCatalog = createAsyncThunk<
    CatalogResponse,
    { catalogId: string; loadCollections?: boolean },
    { state: RootState; dispatch: AppDispatch }
>(
    "catalogState/getCatalogState",
    async ({ catalogId, loadCollections = true }, { dispatch, getState }) => {
        const catalog = await client.catalog(catalogId);
        const parentFrame = getState().videoSession.parentFrame;
        const iframeType = getState().videoSession.iframeType;

        // If in the main frame, set the details on the aux frame to allow it to have customized data when it attempts to load (such as brand colors).
        // This eliminates flashing when the modal loads.
        if (parentFrame && catalog.details && iframeType === IFrameType.Main) {
            parentFrame?.setCatalogDetails(catalog.details);
        }

        if (loadCollections) {
            const currentPage = getState().catalogState.collections.page ?? 0;

            if (
                // The current page is the same as the total loadedCollectionPageCount
                getState().catalogState.loadedCollectionPageCount ===
                currentPage
            ) {
                // Then we want to load the next page
                dispatch(getNextCollections({ catalogId }));
            } else {
                // Loop through collections from the current loaded page to the loadedCollectionPageCount. Happens when collections are reloaded after purchase.
                for (
                    let pageNumber = currentPage;
                    pageNumber <=
                    getState().catalogState.loadedCollectionPageCount;
                    pageNumber++
                ) {
                    dispatch(getCollections({ catalogId, pageNumber }));
                }
            }
        }
        return catalog;
    }
);

/**
 * Gets at a given page number.
 */
export const getCollections = createAsyncThunk<
    CollectionsPagedResponse | void,
    {
        catalogId: string;
        pageSize?: number;
        pageNumber?: number;
    },
    { state: RootState; dispatch: AppDispatch }
>(
    "catalogState/getCollectionsState",
    async (
        { catalogId, pageSize = DEFAULT_COLLECTIONS_PAGE_SIZE, pageNumber },
        { dispatch, getState }
    ) => {
        pageNumber ??= getState().catalogState.collections.page ?? 0;

        if (pageNumber === 0) return;

        const collections = await client.collections(
            catalogId,
            pageNumber,
            pageSize
        );
        const hasOneCollection: boolean = collections.collections?.length === 1;

        collections.collections?.forEach((collection) => {
            const collectionId = collection.details?.id ?? "";
            const isGrid: boolean =
                collection.details?.embeddedDisplay === "Grid";
            /** if user displays only one collection in grid mode in catalog, show up to 23 videos on initial load */
            const largeInitialGridPage =
                pageNumber === 0 && hasOneCollection && isGrid;

            dispatch(
                getCollectionVideosOnDemand({
                    collectionId,
                    isInCatalog: true,
                    pageSize: largeInitialGridPage
                        ? DEFAULT_LARGE_GRID_VIDEO_PAGE_SIZE
                        : undefined
                })
            );
            dispatch(
                getCollectionVideosUpcoming({
                    collectionId,
                    isInCatalog: true,
                    pageSize: largeInitialGridPage
                        ? DEFAULT_LARGE_GRID_VIDEO_PAGE_SIZE
                        : undefined
                })
            );
        });

        return collections;
    }
);

/** Gets the next page from the loaded collections, incrementing + 1 from the current page. */
export const getNextCollections = createAsyncThunk<
    CollectionsPagedResponse | void,
    | {
          catalogId?: string;
          pageSize?: number;
      }
    | undefined,
    { state: RootState; dispatch: AppDispatch }
>(
    "catalogState/getNextCollections",
    async (
        { catalogId, pageSize } = {
            pageSize: DEFAULT_COLLECTIONS_PAGE_SIZE
        },
        { dispatch, getState }
    ) => {
        catalogId ??= getState().catalogState.catalog.details?.id;

        if (catalogId) {
            dispatch(
                getCollections({
                    catalogId,
                    pageSize,
                    pageNumber:
                        (getState().catalogState.collections.page ?? 0) + 1
                })
            );
        }
    }
);

export const getCollection = createAsyncThunk<
    CollectionResponse,
    {
        /** The Collection id to fetch. */
        collectionId: string;
    },
    { state: RootState; dispatch: AppDispatch }
>(
    "catalogState/getCollectionState",
    async ({ collectionId }, { dispatch, getState }) => {
        const { catalogState } = getState();
        const isInCatalog = !!catalogState.configuredCatalogId;
        const collection = await client.collections2(collectionId);

        const {
            parentFrame,
            iframeType,
            preselectedBroadcastId,
            upcomingPreselectedPageNumber,
            onDemandPreselectedPageNumber,
            currentCollectionVideo
        } = getState().videoSession;

        const isCatalogView =
            iframeType === IFrameType.Main &&
            collection.details?.embeddedDisplay !== "DefaultThumbnail";

        // If in the main frame, set the details on the aux frame to allow it to have customized data when it attempts to load (such as brand colors).
        // This eliminates flashing when the modal loads.
        if (isCatalogView && collection.details) {
            parentFrame?.setCollectionDetails(collection.details);
        }

        if (preselectedBroadcastId) {
            const isOnDemand = isInPast(
                currentCollectionVideo?.broadcast?.details?.startsAt
            );

            if (isOnDemand) {
                dispatch(
                    paginateCollectionVideosOnDemandUntilFound({
                        collectionId: collectionId ?? "",
                        broadcastId: preselectedBroadcastId
                    })
                );

                // Get each page of upcoming videos, and await returning the collection til finished
                for (
                    let page = 1;
                    page <= (upcomingPreselectedPageNumber ?? 1);
                    page++
                ) {
                    // Execute each request in sequence
                    await dispatch(
                        getCollectionVideosUpcoming({
                            collectionId,
                            isInCatalog,
                            pageNumber: page,
                            pageSize: isCatalogView
                                ? DEFAULT_LARGE_GRID_VIDEO_PAGE_SIZE
                                : DEFAULT_COLLECTION_VIDEO_PAGE_SIZE
                        })
                    );
                }
            } else {
                dispatch(
                    paginateCollectionVideosUpcomingUntilFound({
                        collectionId: collectionId ?? "",
                        broadcastId: preselectedBroadcastId
                    })
                );

                // Get each page of on demand videos, and await returning the collection til finished
                for (
                    let page = 1;
                    page <= (onDemandPreselectedPageNumber ?? 1);
                    page++
                ) {
                    // Execute each request in sequence
                    await dispatch(
                        getCollectionVideosOnDemand({
                            collectionId,
                            isInCatalog,
                            pageNumber: page,
                            pageSize: isCatalogView
                                ? DEFAULT_LARGE_GRID_VIDEO_PAGE_SIZE
                                : DEFAULT_COLLECTION_VIDEO_PAGE_SIZE
                        })
                    );
                }
            }
        } else {
            // Get each page of upcoming videos, and await returning the collection til finished
            for (
                let page = 1;
                page <= (upcomingPreselectedPageNumber ?? 1);
                page++
            ) {
                // Execute each request in sequence
                await dispatch(
                    getCollectionVideosUpcoming({
                        collectionId,
                        isInCatalog,
                        pageNumber: page,
                        pageSize: isCatalogView
                            ? DEFAULT_LARGE_GRID_VIDEO_PAGE_SIZE
                            : DEFAULT_COLLECTION_VIDEO_PAGE_SIZE
                    })
                );
            }

            // Get each page of on demand videos, and await returning the collection til finished
            for (
                let page = 1;
                page <= (onDemandPreselectedPageNumber ?? 1);
                page++
            ) {
                // Execute each request in sequence
                await dispatch(
                    getCollectionVideosOnDemand({
                        collectionId,
                        isInCatalog,
                        pageNumber: page,
                        pageSize: isCatalogView
                            ? DEFAULT_LARGE_GRID_VIDEO_PAGE_SIZE
                            : DEFAULT_COLLECTION_VIDEO_PAGE_SIZE
                    })
                );
            }
        }

        return collection;
    }
);
export const getCollectionVideosOnDemand = createAsyncThunk<
    CollectionVideosPagedResponse,
    {
        collectionId: string;
        isInCatalog?: boolean | undefined;
        pageNumber?: number;
        pageSize?: number;
    },
    { state: RootState; dispatch: AppDispatch }
>(
    "catalogState/getCollectionVideosOnDemandState",
    async (
        {
            collectionId,
            isInCatalog = false,
            pageNumber = null,
            pageSize = DEFAULT_COLLECTION_VIDEO_PAGE_SIZE
        },
        { getState }
    ) => {
        const collectionVideosMap = getState().catalogState.collectionVideosMap;
        const page =
            pageNumber ??
            (collectionVideosMap?.[collectionId]?.OnDemand?.page ?? 0) + 1;

        return client.collectionVideos(
            collectionId,
            isInCatalog,
            "OnDemand",
            page,
            pageSize
        );
    }
);

export const getCollectionVideosUpcoming = createAsyncThunk<
    CollectionVideosPagedResponse,
    {
        collectionId: string;
        isInCatalog?: boolean | undefined;
        pageNumber?: number;
        pageSize?: number;
    },
    { state: RootState; dispatch: AppDispatch }
>(
    "catalogState/getCollectionVideosUpcomingState",
    async (
        {
            collectionId,
            isInCatalog = false,
            pageNumber = null,
            pageSize = DEFAULT_COLLECTION_VIDEO_PAGE_SIZE
        },
        { getState }
    ) => {
        const collectionVideosMap = getState().catalogState.collectionVideosMap;
        const page =
            pageNumber ??
            (collectionVideosMap?.[collectionId]?.Upcoming?.page ?? 0) + 1;

        return client.collectionVideos(
            collectionId,
            isInCatalog,
            "Upcoming",
            page,
            pageSize
        );
    }
);

export const paginateCollectionVideosOnDemandUntilFound = createAsyncThunk<
    CollectionVideosPagedResponse,
    {
        /** The Collection id to fetch. */
        collectionId: string;
        /** The broadcastId to search for in the collection. */
        broadcastId: string;
        isInCatalog?: boolean | undefined;
        pageNumber?: number;
        pageSize?: number;
    },
    { state: RootState; dispatch: AppDispatch }
>(
    "catalogState/paginateCollectionVideosOnDemandUntilFound",
    async (
        {
            collectionId,
            broadcastId,
            isInCatalog = false,
            pageNumber = null,
            pageSize = DEFAULT_COLLECTION_VIDEO_PAGE_SIZE
        },
        { getState }
    ) => {
        const collectionVideosMap = getState().catalogState.collectionVideosMap;
        const page =
            pageNumber ??
            (collectionVideosMap?.[collectionId]?.OnDemand?.page ?? 0) + 1;

        const collectionVideosResponse = await client.collectionVideos(
            collectionId,
            isInCatalog,
            "OnDemand",
            page,
            pageSize
        );

        // If the video is found in the current page, return the response

        while (
            collectionVideosResponse.items
                ?.map((video) => video?.broadcast?.details?.id)
                .includes(broadcastId) === false
        ) {
            // If the video is not found, check if there are more pages
            if (
                (collectionVideosResponse.page ?? 0) >=
                (collectionVideosResponse.totalPages ?? 0)
            ) {
                // If no more pages, return the response
                return collectionVideosResponse;
            }

            // Otherwise, increment the page and fetch again
            const nextPage = (collectionVideosResponse.page ?? 0) + 1;
            const nextResponse = await client.collectionVideos(
                collectionId,
                isInCatalog,
                "OnDemand",
                nextPage,
                pageSize
            );

            // Update the response to the next page
            collectionVideosResponse.items = [
                ...(collectionVideosResponse.items ?? []),
                ...(nextResponse.items ?? [])
            ];

            collectionVideosResponse.page = nextPage;
        }

        return collectionVideosResponse;
    }
);

export const paginateCollectionVideosUpcomingUntilFound = createAsyncThunk<
    CollectionVideosPagedResponse,
    {
        /** The Collection id to fetch. */
        collectionId: string;
        /** The broadcastId to search for in the collection. */
        broadcastId: string;
        isInCatalog?: boolean | undefined;
        pageNumber?: number;
        pageSize?: number;
    },
    { state: RootState; dispatch: AppDispatch }
>(
    "catalogState/paginateCollectionVideosUpcomingUntilFound",
    async (
        {
            collectionId,
            broadcastId,
            isInCatalog = false,
            pageNumber = null,
            pageSize = DEFAULT_COLLECTION_VIDEO_PAGE_SIZE
        },
        { getState }
    ) => {
        const collectionVideosMap = getState().catalogState.collectionVideosMap;
        const page =
            pageNumber ??
            (collectionVideosMap?.[collectionId]?.Upcoming?.page ?? 0) + 1;

        const collectionVideosResponse = await client.collectionVideos(
            collectionId,
            isInCatalog,
            "Upcoming",
            page,
            pageSize
        );

        // If the video is found in the current page, return the response

        while (
            collectionVideosResponse.items
                ?.map((video) => video?.broadcast?.details?.id)
                .includes(broadcastId) === false
        ) {
            // If the video is not found, check if there are more pages
            if (
                (collectionVideosResponse.page ?? 0) >=
                (collectionVideosResponse.totalPages ?? 0)
            ) {
                // If no more pages, return the response
                return collectionVideosResponse;
            }

            // Otherwise, increment the page and fetch again
            const nextPage = (collectionVideosResponse.page ?? 0) + 1;
            const nextResponse = await client.collectionVideos(
                collectionId,
                isInCatalog,
                "Upcoming",
                nextPage,
                pageSize
            );

            // Update the response to the next page
            collectionVideosResponse.items = [
                ...(collectionVideosResponse.items ?? []),
                ...(nextResponse.items ?? [])
            ];

            collectionVideosResponse.page = nextPage;
        }

        return collectionVideosResponse;
    }
);

export const getCollectionVideosUpcomingPolling = createAsyncThunk<
    CollectionVideosPagedResponse,
    {
        collectionId: string;
    },
    { state: RootState; dispatch: AppDispatch }
>(
    "catalogState/getCollectionVideosUpcomingPollingState",
    async ({ collectionId }, { getState }) => {
        const collectionData =
            getState().catalogState.collectionVideosMap[collectionId];

        if (!collectionData?.Upcoming) {
            return client.collectionVideos(
                collectionId,
                false,
                "Upcoming",
                1,
                DEFAULT_COLLECTION_VIDEO_PAGE_SIZE
            );
        }

        const currentPage = collectionData.Upcoming.page || 1;
        const totalLoadedItems = collectionData.Upcoming.items?.length || 0;

        // Calculate the page size needed to fetch all items in one request
        // Using either the total loaded items or current page * default page size
        const calculatedPageSize = Math.max(
            totalLoadedItems,
            currentPage * DEFAULT_COLLECTION_VIDEO_PAGE_SIZE
        );

        // Make a single request for all loaded data
        return client.collectionVideos(
            collectionId,
            false,
            "Upcoming",
            1,
            calculatedPageSize
        );
    }
);

export const getCollectionVideosProcessingPolling = createAsyncThunk<
    BroadcastResponse[],
    {
        collectionId: string;
    },
    { state: RootState; dispatch: AppDispatch }
>(
    "catalogState/getCollectionVideosProcessingPollingState",
    async ({ collectionId }, { getState }) => {
        const processingVideos =
            getState().catalogState.collectionVideosMap?.[collectionId]
                ?.Processing;

        if (!processingVideos?.length) {
            return [];
        }

        // Create an array of promises for each processing video
        const videoPromises = processingVideos.map((BroadcastResponse) =>
            client.videos(BroadcastResponse!.details!.id!)
        );

        // Wait for all requests to complete and return the results
        return Promise.all(videoPromises);
    }
);

export const getSingleBroadcastResponse = createAsyncThunk<
    CollectionVideoResponse,
    {
        broadcastId: string;
    },
    { state: RootState; dispatch: AppDispatch }
>("catalogState/getSingleBroadcastResponse", async ({ broadcastId }, {}) => {
    return client.videos(broadcastId);
});

/**
 * Searches for videos across collections in the catalog
 *
 * @param projectId - The project/catalog ID to search within
 * @param searchTerm - The text to search for in video titles and descriptions
 * @param page - The page number (1-indexed)
 * @param pageSize - Number of results per page
 */
export const searchCatalogVideos = createAsyncThunk<
    CatalogSearchPagedResponse,
    {
        projectId: string;
        searchTerm: string;
        page?: number;
        pageSize?: number;
    }
>(
    "catalog/searchCatalogVideos",
    async ({
        projectId,
        searchTerm,
        page = 1,
        pageSize = DEFAULT_LARGE_GRID_VIDEO_PAGE_SIZE
    }) => {
        const response = await client.bySearch(
            projectId,
            searchTerm.trim(),
            page,
            pageSize
        );

        return response;
    }
);

/** Gets the next page from the loaded search videos, incrementing + 1 from the current page. */
export const getNextSearchVideos = createAsyncThunk<
    CatalogSearchPagedResponse,
    | {
          projectId?: string;
          pageSize?: number;
      }
    | undefined,
    { state: RootState; dispatch: AppDispatch }
>(
    "catalogState/getNextSearchVideos",
    async (
        { projectId, pageSize } = {
            pageSize: DEFAULT_LARGE_GRID_VIDEO_PAGE_SIZE
        },
        { getState }
    ) => {
        const catalogState = getState().catalogState;
        const playerCreator = getState().playerCreator;
        projectId ??= playerCreator.details?.projectId;
        const searchTerm = catalogState.currentSearchTerm;
        const page = catalogState.searchResultsResponse?.page ?? 1;

        return await client.bySearch(projectId, searchTerm, page + 1, pageSize);
    }
);
