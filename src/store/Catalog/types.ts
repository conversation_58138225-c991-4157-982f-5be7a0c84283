import {
    BroadcastResponse,
    CatalogResponse,
    CatalogSearchPagedResponse,
    CollectionsPagedResponse,
    CollectionVideosPagedResponse
} from "@switcherstudio/player-api-client";

export type CollectionVideosGrouping = {
    OnDemand: CollectionVideosPagedResponse;
    Upcoming: CollectionVideosPagedResponse;
    Processing: BroadcastResponse[];
};

export type CatalogState = {
    // This is the id of the catalog sent into the iframe on initialization
    configuredCatalogId: string | null;
    // This is the id of the collection sent into the iframe on initialization
    configuredCollectionId: string | null;
    // This is the id of the video sent into the iframe on initialization
    configuredBroadcastId: string | null;
    catalog: CatalogResponse;
    collections: CollectionsPagedResponse;
    collectionVideosMap: {
        [key: string]: CollectionVideosGrouping;
    };
    singleBroadcastResponse: BroadcastResponse | null;
    customizedKeys: string[];
    hasLoadedCatalog: boolean;
    hasLoadedCollections: boolean;
    hasLoadedCollectionVideosOnDemand: boolean;
    hasLoadedCollectionVideosUpcoming: boolean;
    hasLoadedSingleBroadcastResponse: boolean;
    hasLoadedSearchResultsResponse: boolean;
    hasLoadingError: boolean;
    isGatedContentDisabled: boolean;
    loadedCollectionPageCount: number;
    currentSearchTerm: string;
    searchResultsResponse: CatalogSearchPagedResponse;
};
