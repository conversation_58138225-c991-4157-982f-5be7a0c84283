export interface ConfirmationState {
    confirmationOpen: boolean;
    message: string;
    titleText: string;
    cancelText: string;
    confirmText: string;
    onSuccess: (...args: any) => any;
    htmlMessage: boolean;
    onCancel: (...args: any) => any;
}

export interface Confirmation {
    message: string;
    onSuccess: (...args: any) => any;
    htmlMessage?: boolean;
    cancelText?: string;
    confirmText?: string;
    titleText?: string;
    onCancel?: (...args: any) => any;
}
