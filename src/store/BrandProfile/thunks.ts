import { createAsyncThunk } from "@reduxjs/toolkit";

export const getBrandProfile = createAsyncThunk(
    "brandProfile/getBrandProfile",
    async ({ playerId }: { playerId?: string | null | undefined }) => {
        // TODO: uncomment this puppy once we've got this on the vapi
        // const { client } = await import("api/switcherClient");

        let brandProfile;
        if (playerId) {
            // HERE - use VAPI client
            // TODO: uncomment this puppy once we've got this on the vapi
            //   brandProfile = await client.videoPlayers_GetVideoPlayerBrandProfile(playerId, true);
        }

        return brandProfile;
    }
);
