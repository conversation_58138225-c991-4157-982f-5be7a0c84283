import { createSlice, Reducer } from "@reduxjs/toolkit";
import { BrandProfileState } from "./types";
import { getBrandProfile } from "./thunks";

const initialState = {
    brandProfile: undefined
} as BrandProfileState;

export const brandProfileSlice = createSlice({
    name: "brandProfile",
    initialState: initialState,
    reducers: {
        // TODO: uncomment this puppy once we've got this on the vapi
        // setBrandProfile: (state, { payload }: PayloadAction<BrandProfile>) => {
        //   state.brandProfile = payload;
        // }
    },
    extraReducers(builder) {
        builder
            // .addCase(getBrandProfile.pending, (state, action) => {
            //   state.status = 'loading'
            // })
            .addCase(getBrandProfile.fulfilled, (state, action) => {
                //state.status = 'succeeded'
                state.brandProfile = action.payload;
            });
        // .addCase(getBrandProfile.rejected, (state, action) => {
        //   state.status = 'failed'
        //   state.error = action.error.message
        // })
    }
});

export const {
    // TODO: uncomment this puppy once we've got this on the vapi
    //   setBrandProfile,
} = brandProfileSlice.actions;

export default brandProfileSlice.reducer as Reducer<BrandProfileState>;
