// Lists of adjectives and nouns for generating friendly usernames
const adjectives = [
    '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', 'Cheerful', '<PERSON>', 'Lively', 'Peaceful', '<PERSON><PERSON>', '<PERSON>',
    'Curious', 'Energetic', '<PERSON><PERSON>', '<PERSON>est', 'Loyal', 'Patient', 'Playful', '<PERSON>ite', 'Proud', 'Quiet',
    'Reliable', 'Sincere', 'Thoughtful', 'Trusty', 'Vibrant', 'Witty', 'Zesty', 'Awesome', 'Brilliant', 'Charming'
];

const nouns = [
    '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>lphin', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 'Owl', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', 'Giraffe', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>ra', '<PERSON><PERSON><PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', 'Octopus', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>',
    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Platypus', 'Hedgehog', 'Squirrel', 'Chipmunk', 'Otter', 'Seal'
];

// Color palette for user avatars/names
const colors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F',
    '#BB8FCE', '#85C1E9', '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2', '#A3E4D7',
    '#FAD7A0', '#D5A6BD', '#AED6F1', '#A9DFBF', '#F9E79F', '#D2B4DE', '#7FB3D3', '#76D7C4'
];

/**
 * Generates a random friendly username in the format "AdjectiveNoun###"
 * @returns A unique username string
 */
export const generateUsername = (): string => {
    const randomAdjective = adjectives[Math.floor(Math.random() * adjectives.length)];
    const randomNoun = nouns[Math.floor(Math.random() * nouns.length)];
    const randomNumber = Math.floor(Math.random() * 1000);
    
    return `${randomAdjective}${randomNoun}${randomNumber}`;
};

/**
 * Generates a random color for the user
 * @returns A hex color string
 */
export const generateUserColor = (): string => {
    return colors[Math.floor(Math.random() * colors.length)];
};

/**
 * Validates if a username meets the requirements
 * @param username The username to validate
 * @returns True if valid, false otherwise
 */
export const validateUsername = (username: string): boolean => {
    // Username should be 3-20 characters, alphanumeric with optional underscores
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
    return usernameRegex.test(username);
};

/**
 * Sanitizes a username by removing invalid characters
 * @param username The username to sanitize
 * @returns A sanitized username
 */
export const sanitizeUsername = (username: string): string => {
    // Remove invalid characters and limit length
    return username
        .replace(/[^a-zA-Z0-9_]/g, '')
        .substring(0, 20);
};
