import {
    collection,
    doc,
    addDoc,
    onSnapshot,
    query,
    orderBy,
    limit,
    where,
    serverTimestamp,
    updateDoc,
    setDoc,
    deleteDoc,
    Timestamp,
    Unsubscribe
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { 
    Chat<PERSON><PERSON>age, 
    ChatUser, 
    ChatRoom, 
    ChatMessageDoc, 
    ChatUserDoc, 
    ChatRoomDoc 
} from '../types/chat';
import { generateUsername, generateUserColor } from './usernameGenerator';

class ChatService {
    private messageUnsubscribe: Unsubscribe | null = null;
    private usersUnsubscribe: Unsubscribe | null = null;
    private currentUserId: string | null = null;

    /**
     * Creates or joins a chat room for a specific video/collection
     */
    async joinRoom(roomId: string, username?: string): Promise<ChatUser> {
        try {
            // Generate user ID and username if not provided
            const userId = this.generateUserId();
            const finalUsername = username || generateUsername();
            const userColor = generateUserColor();

            // Create user document
            const userDoc: ChatUserDoc = {
                username: finalUsername,
                isOnline: true,
                lastSeen: serverTimestamp() as Timestamp,
                color: userColor
            };

            // Set user in Firestore
            await setDoc(doc(db, 'chatUsers', userId), userDoc);

            // Create or update room
            await this.createOrUpdateRoom(roomId);

            // Store current user ID
            this.currentUserId = userId;

            return {
                id: userId,
                username: finalUsername,
                isOnline: true,
                lastSeen: Timestamp.now(),
                color: userColor
            };
        } catch (error) {
            console.error('Error joining room:', error);
            throw error;
        }
    }

    /**
     * Sends a message to the chat room
     */
    async sendMessage(roomId: string, message: string, user: ChatUser): Promise<void> {
        try {
            const messageDoc: ChatMessageDoc = {
                userId: user.id,
                username: user.username,
                message: message.trim(),
                timestamp: serverTimestamp() as Timestamp,
                roomId,
                userColor: user.color
            };

            await addDoc(collection(db, 'chatMessages'), messageDoc);

            // Update room last activity
            await this.updateRoomActivity(roomId);
        } catch (error) {
            console.error('Error sending message:', error);
            throw error;
        }
    }

    /**
     * Subscribes to messages for a specific room
     */
    subscribeToMessages(
        roomId: string, 
        callback: (messages: ChatMessage[]) => void,
        messageLimit: number = 50
    ): Unsubscribe {
        const messagesQuery = query(
            collection(db, 'chatMessages'),
            where('roomId', '==', roomId),
            orderBy('timestamp', 'desc'),
            limit(messageLimit)
        );

        this.messageUnsubscribe = onSnapshot(messagesQuery, (snapshot) => {
            const messages: ChatMessage[] = snapshot.docs
                .map(doc => ({
                    id: doc.id,
                    ...doc.data()
                } as ChatMessage))
                .reverse(); // Reverse to show oldest first

            callback(messages);
        });

        return this.messageUnsubscribe;
    }

    /**
     * Subscribes to online users for a specific room
     */
    subscribeToUsers(
        callback: (users: ChatUser[]) => void
    ): Unsubscribe {
        const usersQuery = query(
            collection(db, 'chatUsers'),
            where('isOnline', '==', true),
            orderBy('lastSeen', 'desc')
        );

        this.usersUnsubscribe = onSnapshot(usersQuery, (snapshot) => {
            const users: ChatUser[] = snapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            } as ChatUser));

            callback(users);
        });

        return this.usersUnsubscribe;
    }

    /**
     * Updates user's online status
     */
    async updateUserStatus(userId: string, isOnline: boolean): Promise<void> {
        try {
            const userRef = doc(db, 'chatUsers', userId);
            await updateDoc(userRef, {
                isOnline,
                lastSeen: serverTimestamp()
            });
        } catch (error) {
            console.error('Error updating user status:', error);
        }
    }

    /**
     * Leaves the chat room and cleans up
     */
    async leaveRoom(): Promise<void> {
        try {
            if (this.currentUserId) {
                await this.updateUserStatus(this.currentUserId, false);
            }

            // Unsubscribe from listeners
            if (this.messageUnsubscribe) {
                this.messageUnsubscribe();
                this.messageUnsubscribe = null;
            }

            if (this.usersUnsubscribe) {
                this.usersUnsubscribe();
                this.usersUnsubscribe = null;
            }

            this.currentUserId = null;
        } catch (error) {
            console.error('Error leaving room:', error);
        }
    }

    /**
     * Creates or updates a chat room
     */
    private async createOrUpdateRoom(roomId: string): Promise<void> {
        try {
            const roomRef = doc(db, 'chatRooms', roomId);
            const roomDoc: ChatRoomDoc = {
                name: `Video Chat ${roomId}`,
                description: 'Chat for video viewers',
                createdAt: serverTimestamp() as Timestamp,
                isActive: true,
                participantCount: 1,
                lastActivity: serverTimestamp() as Timestamp
            };

            await setDoc(roomRef, roomDoc, { merge: true });
        } catch (error) {
            console.error('Error creating/updating room:', error);
        }
    }

    /**
     * Updates room's last activity timestamp
     */
    private async updateRoomActivity(roomId: string): Promise<void> {
        try {
            const roomRef = doc(db, 'chatRooms', roomId);
            await updateDoc(roomRef, {
                lastActivity: serverTimestamp()
            });
        } catch (error) {
            console.error('Error updating room activity:', error);
        }
    }

    /**
     * Generates a unique user ID
     */
    private generateUserId(): string {
        return `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Cleans up old messages (optional maintenance function)
     */
    async cleanupOldMessages(roomId: string, daysOld: number = 7): Promise<void> {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysOld);
            
            const oldMessagesQuery = query(
                collection(db, 'chatMessages'),
                where('roomId', '==', roomId),
                where('timestamp', '<', Timestamp.fromDate(cutoffDate))
            );

            // Note: In a production environment, this should be done server-side
            // for better performance and security
        } catch (error) {
            console.error('Error cleaning up old messages:', error);
        }
    }
}

export const chatService = new ChatService();
