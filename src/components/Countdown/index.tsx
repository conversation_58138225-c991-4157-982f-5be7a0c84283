import { useInterval } from "hooks/useInterval";
import { useCallback, useEffect, useMemo, useState } from "react";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import { timeDiff } from "helpers/time";
const cx = classNames.bind(styles);

interface CountDownProps {
    countdownTargetDate: Date | null | undefined;
    onCountDownEnd?: () => void;
    title?: string;
    postCountdownTitle?: string;
    variant?: "full" | "compact" | "compact-vertical";
}

export const Countdown = ({
    countdownTargetDate,
    onCountDownEnd,
    title,
    postCountdownTitle,
    variant = "compact"
}: CountDownProps) => {
    const [days, setDays] = useState<number>(0);
    const [hours, setHours] = useState<number>(0);
    const [minutes, setMinutes] = useState<number>(0);
    const [seconds, setSeconds] = useState<number>(0);
    const [countdownEnded, setCountdownEnded] = useState<boolean>(false);

    const getTimeRemaining = useCallback(() => {
        const total = timeDiff(countdownTargetDate);
        const seconds = Math.floor((total / 1000) % 60);
        const minutes = Math.floor((total / 1000 / 60) % 60);
        const hours = Math.floor((total / 1000 / 60 / 60) % 24);
        const days = Math.floor(total / 1000 / 60 / 60 / 24);

        return {
            total,
            days,
            hours,
            minutes,
            seconds
        };
    }, [countdownTargetDate]);

    const [total, setTotal] = useState(getTimeRemaining().total);

    const updateTimer = useCallback(() => {
        if (countdownEnded) return;

        let { total, days, hours, minutes, seconds } = getTimeRemaining();

        setTotal(total);
        if (total >= 0) {
            setDays(days);
            setHours(hours);
            setMinutes(minutes);
            setSeconds(seconds);
        }
        if (total <= 0) {
            setTotal(0);
            setDays(0);
            setHours(0);
            setMinutes(0);
            setSeconds(0);
            setCountdownEnded(true);
            onCountDownEnd && onCountDownEnd();
        }
    }, [getTimeRemaining, onCountDownEnd, countdownEnded]);

    useInterval(
        () => {
            updateTimer();
        },
        total >= 0 ? 1000 : null
    );

    // Update the timer on mount
    useEffect(() => {
        updateTimer();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const { showDays, showHours, showMinutes, showSeconds } = useMemo(() => {
        // Show all blocks in full mode
        if (variant === "full") {
            return {
                showDays: true,
                showHours: true,
                showMinutes: true,
                showSeconds: true
            };
        }

        // In compact vertical mode, we show the first two blocks
        if (variant === "compact-vertical") {
            return {
                showDays: days > 0,
                showHours: days > 0 || hours > 0,
                showMinutes: days === 0 && (hours > 0 || minutes > 0),
                showSeconds:
                    days === 0 && hours === 0 && (minutes > 0 || seconds > 0)
            };
        }

        // In compact mode, we show the first three blocks
        return {
            showDays: days > 0,
            showHours: days > 0 || hours > 0,
            showMinutes: days > 0 || hours > 0 || minutes > 0,
            showSeconds: days === 0 && (hours > 0 || minutes > 0 || seconds > 0)
        };
    }, [days, hours, minutes, seconds, variant]);

    return (
        <div
            className={cx("countdown-container", {
                "compact": variant === "compact",
                "compact-vertical": variant === "compact-vertical",
                "full": variant === "full"
            })}
        >
            <div className={cx("countdown-title")}>
                {countdownEnded ? postCountdownTitle : title ?? "Premieres In"}
            </div>
            {!countdownEnded && (
                <div className={cx("countdown-blocks")}>
                    {showDays && (
                        <div className={cx("countdown-block")}>
                            <div className={cx("countdown-value")}>
                                {days?.toLocaleString("en-US", {
                                    minimumIntegerDigits:
                                        variant === "full" ? 2 : 1,
                                    useGrouping: false
                                })}
                            </div>
                            <div className={cx("countdown-unit")}>
                                {`D${variant === "full" ? "ays" : ""}`}
                            </div>
                        </div>
                    )}
                    {showHours && (
                        <div className={cx("countdown-block")}>
                            <div className={cx("countdown-value")}>
                                {hours?.toLocaleString("en-US", {
                                    minimumIntegerDigits:
                                        variant === "full" ? 2 : 1,
                                    useGrouping: false
                                })}
                            </div>
                            <div className={cx("countdown-unit")}>
                                {`H${variant === "full" ? "ours" : ""}`}
                            </div>
                        </div>
                    )}
                    {showMinutes && (
                        <div className={cx("countdown-block")}>
                            <div className={cx("countdown-value")}>
                                {minutes?.toLocaleString("en-US", {
                                    minimumIntegerDigits:
                                        variant === "full" ? 2 : 1,
                                    useGrouping: false
                                })}
                            </div>
                            <div className={cx("countdown-unit")}>
                                {`M${variant === "full" ? "inutes" : ""}`}
                            </div>
                        </div>
                    )}
                    {showSeconds && (
                        <div className={cx("countdown-block")}>
                            <div className={cx("countdown-value")}>
                                {seconds?.toLocaleString("en-US", {
                                    minimumIntegerDigits:
                                        variant === "full" ? 2 : 1,
                                    useGrouping: false
                                })}
                            </div>
                            <div className={cx("countdown-unit")}>
                                {`S${variant === "full" ? "econds" : ""}`}
                            </div>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};
