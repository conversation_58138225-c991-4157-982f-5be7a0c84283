.countdown-container {
    text-align: center;
    color: white;

    .countdown-title {
        font-weight: 600;
        font-size: rem(22);
        line-height: rem(28);
    }

    .countdown-blocks {
        margin-top: rem(24);
        display: flex;
        justify-content: center;
        gap: rem(25);

        .countdown-block {
            background-color: rgba(white, 0.16);
            border-radius: rem(16);
            width: rem(80);
            height: rem(96);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;

            &:first-child {
                &:before {
                    display: none;
                }
            }

            &:before {
                position: absolute;
                content: ":";
                font-weight: 700;
                font-size: rem(32);
                line-height: rem(80);
                color: rgba(white, 0.6);

                left: rem(-17);
                top: rem(-2);
            }

            .countdown-value {
                font-weight: 700;
                font-size: rem(32);
                line-height: rem(48);
            }

            .countdown-unit {
                font-size: rem(12);
                line-height: rem(16);
                text-transform: uppercase;
            }
        }
    }

    &.compact {
        .countdown-title {
            font-size: rem(20);
            line-height: rem(16);
        }

        .countdown-blocks {
            gap: rem(8);
            margin-top: rem(24);

            .countdown-block {
                width: auto;
                height: auto;
                background-color: transparent;
                flex-direction: row;
                align-items: flex-end;
                gap: rem(2);

                &::before {
                    display: none;
                }

                .countdown-value {
                    font-size: rem(20);
                    line-height: 0;
                }

                .countdown-unit {
                    text-transform: lowercase;
                    font-size: rem(20);
                    line-height: 0;
                    font-weight: bold;
                }
            }
        }

        @container video-overlay (width < 200px) {
            .countdown-title {
                font-size: rem(18);
                line-height: rem(12);
            }

            .countdown-blocks {
                gap: rem(6);
                margin-top: rem(12);

                .countdown-block {
                    background-color: transparent;

                    .countdown-value {
                        font-size: rem(18);
                        line-height: rem(16);
                    }
                    .countdown-unit {
                        font-size: rem(18);
                        line-height: rem(16);
                    }
                }
            }
        }

        @container video-overlay (width < 150px) {
            .countdown-title {
                font-size: rem(12);
                line-height: rem(8);
            }

            .countdown-blocks {
                margin-top: rem(8);

                .countdown-block {
                    gap: 0;

                    .countdown-value {
                        font-size: rem(12);
                        line-height: rem(12);
                    }
                    .countdown-unit {
                        font-size: rem(12);
                        line-height: rem(12);
                    }
                }
            }
        }
    }

    &.compact-vertical {
        .countdown-title {
            font-size: rem(20);
            line-height: rem(16);
        }

        .countdown-blocks {
            gap: rem(8);
            margin-top: rem(24);

            .countdown-block {
                width: auto;
                height: auto;
                background-color: transparent;
                flex-direction: row;
                align-items: flex-end;
                gap: rem(2);

                &::before {
                    display: none;
                }

                .countdown-value {
                    font-size: rem(20);
                    line-height: 0;
                }

                .countdown-unit {
                    text-transform: lowercase;
                    font-size: rem(20);
                    line-height: 0;
                    font-weight: bold;
                }
            }
        }

        @container video-overlay (width < 200px) {
            .countdown-title {
                font-size: rem(8);
                line-height: rem(12);
            }

            .countdown-blocks {
                gap: rem(6);
                margin-top: rem(12);

                .countdown-block {
                    background-color: transparent;

                    .countdown-value {
                        font-size: rem(10);
                        line-height: rem(16);
                    }
                    .countdown-unit {
                        font-size: rem(10);
                        line-height: rem(16);
                    }
                }
            }
        }

        @container video-overlay (width < 150px) {
            .countdown-title {
                font-size: rem(8);
                line-height: rem(8);
            }

            .countdown-blocks {
                margin-top: rem(3.2);

                .countdown-block {
                    gap: 0;

                    .countdown-value {
                        font-size: rem(10);
                        line-height: rem(12);
                    }
                    .countdown-unit {
                        font-size: rem(10);
                        line-height: rem(12);
                    }
                }
            }
        }
    }
}
