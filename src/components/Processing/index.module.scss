.loading-container {
    width: rem(60);
    position: relative;

    .dot {
        width: rem(10);
        height: rem(10);
        background: rgba(var(--interactive-panel-text-color-dark), 0.32);
        position: absolute;
        animation: wave 1s infinite;
        border-radius: 50%;
    }

    .dot:nth-child(1) {
        left: 10.42%;
        right: 68.75%;
        top: 39.58%;
        bottom: 39.58%;
        animation-delay: 0s;
    }

    .dot:nth-child(2) {
        left: 41.67%;
        right: 41.67%;
        top: 41.67%;
        bottom: 41.67%;
        animation-delay: 0.1s;
    }

    .dot:nth-child(3) {
        left: 70.83%;
        right: 12.5%;
        top: 41.67%;
        bottom: 41.67%;
        animation-delay: 0.2s;
    }

    @keyframes wave {
        0% {
            transform: scale(110%);
            background: var(--interactive-panel-text-color-dark);
        }
        100% {
            transform: scale(100%);
            background: rgba(var(--interactive-panel-text-color-dark), 0.32);
        }
    }
}
