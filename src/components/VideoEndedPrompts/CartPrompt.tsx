import React from "react";
import styles from "./index.module.scss";
import CartIcon from "assets/icons/cart.svg?react";
import { useSelector } from "react-redux";
import { RootState } from "store/reducers";
import { useShopping } from "hooks/useShopping";

export const CartPrompt = () => {
    const shoppingState = useSelector((s: RootState) => s.shopping);
    const { currentCollectionVideo } = useSelector(
        (s: RootState) => s.videoSession
    );
    const { checkoutConfirmation } = useShopping(
        currentCollectionVideo?.broadcast?.details?.id
    );

    return (
        <div className={styles["cart-container"]}>
            <div className={styles["cart-info"]}>
                <CartIcon />
                {shoppingState.cart.length}
                {` item${
                    shoppingState.cart.length > 1 ? "s" : ""
                } in your cart`}
            </div>

            <button
                type="button"
                className={`btn btn-black`}
                onClick={() => checkoutConfirmation(true)}
            >
                Go to Checkout
            </button>
        </div>
    );
};
