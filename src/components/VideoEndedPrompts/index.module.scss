.video-ended-prompts-container {
    z-index: 9;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(rem(16));
    transform: translateZ(0);
    border-top-left-radius: 1rem;
    border-bottom-left-radius: 1rem;
    display: flex;
    flex-flow: column nowrap;
    gap: 2rem;
    overflow-y: auto;

    .top-row,
    .middle-row {
        display: flex;
        flex-flow: row nowrap;
        justify-content: center;
        align-items: flex-start;
    }

    .top-row {
        flex-basis: 40%;
    }

    .middle-row {
        flex-basis: 50%;
    }

    &:not(.has-cart) {
        .top-row {
            flex-basis: 0%;
        }

        .middle-row {
            flex-basis: 100%;
            justify-content: center;
            align-items: center;
        }
    }

    button {
        text-transform: uppercase;
    }

    .cart-container {
        margin-top: 2rem;
        display: flex;
        flex-flow: column nowrap;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;
        height: rem(118);
        min-width: max-content;
        width: 60%;
        max-width: rem(600);
        background-color: #ffffff;
        color: #000000;
        border-radius: rem(12);
        padding: 1.25rem;
        text-align: center;

        .cart-info {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            gap: 1rem;
            width: max-content;
        }

        button {
            align-self: stretch;
        }
    }

    .prompts-content-container {
        grid-area: middle;
        display: flex;
        flex-flow: column nowrap;
        justify-content: flex-start;
        align-items: center;
        gap: 1rem;
        width: 90%;
        max-width: rem(800);
        margin: 0 auto;

        .next-video-text {
            color: #ffffff;
        }

        & > div[class*="playlist-video"] {
            border-radius: 0.75rem !important;
            max-width: max-content;
            position: relative;

            &::before {
                content: "Up next";
                display: inherit;
                color: #ffffff;
                position: absolute;
                left: 0;
                top: -2rem;
                border: none;
            }
        }

        .watch-again-btn {
            background-color: #ffffff;
            color: #000000;
        }

        .dismiss-btn {
            color: #ffffff;
            background: none;
            border: none;
            font-size: 1rem;
            font-weight: 500;
            text-shadow: 0 0 1rem black;
            cursor: pointer;
        }

        .more-videos-btn,
        .mobile-more-videos-btn,
        .mobile-view-cart-btn {
            background-color: #414141;
        }

        .mobile-view-cart-btn {
            display: none;
            background-color: #ffffff;
            color: #000000;
        }

        .mobile-more-videos-btn {
            display: none;
        }

        .cart-container {
            display: none;
        }
        .mobile-cart-container {
            display: none;
        }
    }

    @media screen and (max-width: 670px) {
        .video-ended-prompts-container {
            border-radius: 0;
        }
    }
}

/* Styles for specific player states */
:global(.is-expanded) .video-ended-prompts-container {
    border-top-left-radius: 1rem;
    border-bottom-left-radius: 1rem;

    @include mobile-portrait-in-global {
        border-radius: 0;
    }
}

:global(.tool-active:not(.is-embed)) .video-ended-prompts-container {
    @media screen and (max-width: 780px) {
        .cart-container {
            display: none;
        }

        .top-row {
            flex-basis: 0;
        }

        .middle-row {
            flex-basis: 100%;
            align-items: center;
        }

        .prompts-content-container {
            gap: 1rem;

            .mobile-view-cart-btn {
                display: block;
            }

            & > div[class*="playlist-video"] {
                display: none;
            }
        }
    }

    @media screen and (max-width: 600px) {
        .prompts-content-container {
            display: none;
        }
    }

    @media screen and (max-width: 560px) and (orientation: portrait) {
        border-radius: 0;
        gap: 0;

        .cart-container {
            display: none;
        }

        .middle-row {
            flex-basis: 100%;
            align-items: center;
        }

        .top-row {
            flex-basis: 0;
        }

        .prompts-content-container {
            display: flex;
            width: 100%;
            min-width: unset;
            flex-flow: row-reverse wrap;
            justify-content: center;

            .cart-container {
                margin: 0;
                display: flex;
                width: 75%;
            }

            .mobile-view-cart-btn {
                display: none;
            }

            & > div[class*="playlist-video"] {
                display: none;
            }

            .mobile-more-videos-btn {
                display: block;
            }

            .dismiss-btn {
                background-color: #414141;
                padding: 0.625rem 1.5rem;
                text-decoration: none;
            }
        }
    }

    @media screen and (max-width: 375px) and (orientation: portrait) {
        .prompts-content-container {
            width: 90%;

            .cart-container {
                display: none;
            }

            .mobile-view-cart-btn {
                display: block;
                flex-basis: 100%;
            }

            .dismiss-btn,
            .mobile-more-videos-btn {
                flex-grow: 1;
            }
        }
    }
}

:global(.is-embed:not(.is-expanded)) .video-ended-prompts-container {
    border-radius: rem(16) 0 0 0;
    gap: 0;
    padding-top: 5%;

    &.has-cart {
        .top-row,
        .middle-row {
            transform: scale(0.75);
        }
    }

    @media screen and (max-width: 600px) {
        padding-top: unset;
        .top-row,
        .middle-row {
            transform: unset;
        }

        .prompts-content-container {
            display: flex;

            & > div[class*="playlist-video"] {
                display: none;
            }
        }

        .cart-container {
            display: none;
        }

        .mobile-more-videos-btn {
            display: block;
        }

        .mobile-view-cart-btn {
            display: block;
            flex-basis: 100%;
        }
    }

    @media screen and (max-width: 400px) {
        .prompts-content-container.has-cart {
            width: 90%;
            min-width: unset;
            flex-flow: row-reverse wrap;
            justify-content: center;
            gap: 1rem;

            button {
                flex-basis: calc(50% - 0.5rem);
            }

            .mobile-view-cart-btn {
                flex-basis: calc(100% + 1rem);
            }

            .cart-container {
                display: none;
            }

            .dismiss-btn {
                background-color: #414141;
                padding: 0.625rem 1.5rem;
                text-decoration: none;
            }
        }
    }

    @media screen and (max-width: 300px) {
        .prompts-content-container.has-cart {
            .mobile-more-videos-btn {
                display: none;
            }
        }
    }
}
