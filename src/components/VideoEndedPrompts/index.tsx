import { PlaylistVideo } from "components/VideoOnDemand/PlaylistList/PlaylistVideo";
import React, { useCallback, useMemo } from "react";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import { RootState } from "store/reducers";
import {
    setActiveTool,
    setEndedState,
    setLevelLoadError,
    setToolState
} from "store/VideoSession/slice";
import styles from "./index.module.scss";
import { CartPrompt } from "./CartPrompt";
import classNames from "classnames/bind";
import { useToggleInteractiveMode } from "hooks/useToggleInteractiveMode";
import { resetTimeSlider } from "components/ShakaPlayer/helpers";
import { usePlayPause } from "hooks/usePlayPause";
import useCollectionWithVideos from "hooks/useCollectionWithVideos";
const cx = classNames.bind(styles);

export const VideoEndedPrompts = ({ video }: { video: HTMLVideoElement }) => {
    const dispatch = useDispatch();

    const { allVideos } = useCollectionWithVideos();

    const shoppingState = useSelector((s: RootState) => s.shopping);
    const { currentCollectionVideo, isExpanded, isEmbed } = useSelector(
        (s: RootState) => s.videoSession
    );
    const { playVideo } = usePlayPause(video);

    const handleWatchAgain = useCallback(() => {
        dispatch(setEndedState(false));
        dispatch(setLevelLoadError(false));
        if (video) {
            resetTimeSlider();
            video.currentTime = 0;
            playVideo();
        }
    }, [dispatch, playVideo, video]);

    const handleDismiss = useCallback(() => {
        dispatch(setEndedState(false));
        dispatch(setLevelLoadError(false));
    }, [dispatch]);

    const nextPlaylistVideo = useMemo(() => {
        if (!allVideos?.length) return;
        const currentVideoIndex = allVideos?.findIndex(
            (collectionVideo) =>
                collectionVideo?.broadcast?.details?.id ===
                currentCollectionVideo?.broadcast?.details?.id
        );
        const nextVideo = allVideos?.[currentVideoIndex + 1] ?? allVideos?.[0];
        return nextVideo;
    }, [currentCollectionVideo, allVideos]);
    const videoHasShopping = useMemo(
        () => currentCollectionVideo?.broadcast?.details?.enableLiveShopping,
        [currentCollectionVideo]
    );
    const videoHasCart = useMemo(() => {
        return videoHasShopping && !!shoppingState?.cart?.length;
    }, [videoHasShopping, shoppingState]);

    const hasPlaylist = useMemo(() => {
        return !!allVideos && allVideos?.length > 1;
    }, [allVideos]);

    const { toggleInteractive } = useToggleInteractiveMode(video);

    const viewCart = useCallback(() => {
        if (isEmbed && !isExpanded) {
            toggleInteractive(true);
        }
        dispatch(setActiveTool("Cart"));
    }, [dispatch, isEmbed, toggleInteractive, isExpanded]);

    const handleMoreVideos = useCallback(() => {
        if (isEmbed && !isExpanded) {
            toggleInteractive(true);
        } else if (!isExpanded) {
            dispatch(setToolState(true));
        }

        dispatch(setActiveTool("VoD"));
    }, [dispatch, isExpanded, toggleInteractive, isEmbed]);

    return (
        <div
            className={cx(
                "video-ended-prompts-container",
                "fixed-aspect-ratio-child",
                {
                    "has-cart": videoHasCart,
                    "has-playlist": hasPlaylist
                }
            )}
        >
            <div className={styles["top-row"]}>
                {videoHasCart && <CartPrompt />}
            </div>

            <div className={styles["middle-row"]}>
                <div className={styles["prompts-content-container"]}>
                    {videoHasCart && (
                        <>
                            <CartPrompt />
                            <button
                                type="button"
                                className={`btn ${styles["mobile-view-cart-btn"]}`}
                                onClick={viewCart}
                            >
                                View Cart
                                {` (${shoppingState?.cart?.length})`}
                            </button>
                        </>
                    )}

                    {hasPlaylist && (
                        <>
                            {/** PlaylistVideo has its own onClick handler using setNewCollectionVideo */}
                            <PlaylistVideo
                                collectionVideo={nextPlaylistVideo}
                                darkBackdrop
                            />
                            <button
                                type="button"
                                className={`btn ${styles["mobile-more-videos-btn"]}`}
                                onClick={handleMoreVideos}
                            >
                                More Videos
                            </button>
                        </>
                    )}

                    {!hasPlaylist && (
                        <button
                            type="button"
                            onClick={handleWatchAgain}
                            className={`btn ${styles["watch-again-btn"]}`}
                        >
                            Watch Again
                        </button>
                    )}

                    <button
                        onClick={handleDismiss}
                        className={`btn btn-text ${styles["dismiss-btn"]}`}
                    >
                        Dismiss
                    </button>
                </div>
            </div>
        </div>
    );
};
