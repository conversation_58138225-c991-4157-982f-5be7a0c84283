import { ReactNode } from "react";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import useCollectionWithVideos from "hooks/useCollectionWithVideos";
const cx = classNames.bind(styles);

export const VideoPlayerPlaceholderBackground = ({
    children = <></>
}: {
    children: ReactNode | ReactNode[] | undefined;
}) => {
    const { collection } = useCollectionWithVideos();

    const isDisabled = !collection?.creatorIsValid;

    return (
        <div
            className={cx(
                "video-player-placeholder-background",
                "fixed-aspect-ratio-child",
                {
                    "player-disabled": isDisabled
                }
            )}
        >
            {children}
        </div>
    );
};
