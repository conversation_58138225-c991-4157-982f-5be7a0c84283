.video-player-placeholder-background {
    display: flex;
    flex-flow: column nowrap;
    justify-content: center;
    align-items: center;
    color: white;
    gap: 1rem;
    background-color: black;
    color: white;
    transition:
        0.25s background-color ease-in,
        0.25s border-radius ease-in-out;

    border-radius: rem(16) rem(16) rem(0) rem(0);
    overflow: hidden;

    &.player-disabled {
        background-color: #212529;
    }

    @include mobile {
        border-radius: 0;
    }

    .video-player-placeholder-error {
        color: var(--brand-color-primary);
        transition: 0.25s color ease-in;
        font-size: rem(24);
        font-weight: 600;
        line-height: rem(32);
        letter-spacing: rem(0);
        text-align: center;
        opacity: 0;
        transition: 0.25s opacity ease-in;

        &.active {
            opacity: 1;
        }
    }
}

/* Styles for specific player styles */
:global(.tool-active):not(.player-disabled) .video-player-placeholder-background {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

:global(.is-embed:not(.is-expanded)) .video-player-placeholder-background {
    border-radius: 0;
}
