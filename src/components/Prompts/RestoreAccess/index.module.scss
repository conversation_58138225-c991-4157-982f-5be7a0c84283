.video-nav-restore-access {
    font-size: rem(14);
    font-weight: 400;
    line-height: rem(20);
    text-align: center;
    align-items: center;
    gap: rem(2);

    .restore-button {
        font-weight: 600;
        border: none;
        background: none;
        color: rgba(var(--brand-color-primary-rgb));
        padding: 0;
        &:hover {
            cursor: pointer;
            color: var(--brand-color-primary-dark);
        }
    }
}

.catalog-variant {
    color: rgba(var(--embed-text-color-rgb));
    display: flex;

    @include mobile {
        flex-direction: column;
        align-items: flex-start;
        text-align: left;
    }
}

.video-variant {
    color: rgba(var(--interactive-panel-text-color-rgb));
}
