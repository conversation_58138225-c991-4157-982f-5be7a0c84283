import classNames from "classnames/bind";
import styles from "./index.module.scss";
import { Trans } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/reducers";
import { Modals } from "store/Modals/types";
import { IFrameType } from "store/VideoSession/types";
import { AppDispatch } from "store/store";
import { setActiveModal } from "store/Modals/slice";
import useCollectionWithVideos from "hooks/useCollectionWithVideos";

const cx = classNames.bind(styles);

interface RestoreAccessPromptProps {
    variant: "Catalog" | "Video";
}

export const RestoreAccessPrompt = ({ variant }: RestoreAccessPromptProps) => {
    const { parentFrame, iframeType } = useSelector(
        (s: RootState) => s.videoSession
    );
    const catalogState = useSelector((s: RootState) => s.catalogState);
    const { collection } = useCollectionWithVideos();
    const dispatch = useDispatch<AppDispatch>();

    const openRestoreModal = () => {
        const catalogId = catalogState.catalog?.details?.id ?? "";
        if (
            iframeType === IFrameType.Main &&
            parentFrame &&
            collection?.details?.embeddedDisplay !== "DefaultThumbnail"
        ) {
            parentFrame?.openPurchaseModal(
                Modals.RestorePurchase,
                catalogId,
                ""
            );
        } else {
            dispatch(setActiveModal(Modals.RestorePurchase));
        }
    };

    return (
        <div
            className={cx("video-nav-restore-access", {
                "catalog-variant": variant === "Catalog",
                "video-variant": variant === "Video"
            })}
        >
            <Trans
                i18nKey={"prompts:restore-access"}
                components={{
                    link1: (
                        <button
                            onClick={openRestoreModal}
                            className={cx("restore-button")}
                        ></button>
                    )
                }}
            />
        </div>
    );
};
