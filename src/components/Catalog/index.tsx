import React, { useEffect, useRef } from "react";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import { FeaturedTrailer } from "./FeaturedTrailer";
import { useSelector } from "react-redux";
import { RootState } from "store/reducers";
import { Collections } from "./Collections";
import { useTranslation } from "react-i18next";
import { RestoreAccessPrompt } from "components/Prompts/RestoreAccess";
import { PurchaseButton } from "components/Buttons/PurchaseButton";
import { eventBus } from "helpers/eventBus";
import { useEventTracking } from "hooks/useEventTracking";
import { useEntitlementShortcuts } from "hooks/useEntitlementShortcuts";
import { Search } from "components/Search";

const cx = classNames.bind(styles);

const enableCatalogSearch =
    import.meta.env.VITE_ENABLE_CATALOG_SEARCH === "true";

interface CatalogProps {}

export const Catalog: React.FC<CatalogProps> = () => {
    const { parentFrame } = useSelector((s: RootState) => s.videoSession);
    const {
        catalog,
        collections,
        collectionVideosMap,
        currentSearchTerm,
        searchResultsResponse,
        configuredCatalogId,
        configuredCollectionId,
        hasLoadedCatalog,
        hasLoadedCollections
    } = useSelector((s: RootState) => s.catalogState);
    const catalogRef = useRef<HTMLDivElement>(null);
    const resizeTimeoutRef = useRef<NodeJS.Timeout>();
    const { trackEvent } = useEventTracking({
        layout: catalog?.details?.embeddedDisplay
    });
    const { t } = useTranslation();

    const { showPurchaseButton, isOnlySubscribe } = useEntitlementShortcuts(
        catalog,
        "Catalog"
    );

    const lastWidthRef = useRef<number | null>(null); // Ref to store the last known width

    useEffect(() => {
        if (!catalogRef.current || !parentFrame) return;

        const handleResize = () => {
            // debounce handler
            if (resizeTimeoutRef.current)
                clearTimeout(resizeTimeoutRef.current);
            resizeTimeoutRef.current = setTimeout(() => {
                if (!catalogRef.current) return;

                const newWidth = catalogRef.current.clientWidth;
                const newHeight = catalogRef.current.clientHeight;

                // Only resize if the width has changed by more than 20 pixels
                if (Math.abs((lastWidthRef.current ?? 0) - newWidth) >= 20) {
                    parentFrame.resize(newHeight);
                    eventBus.emit("catalogResize");
                    lastWidthRef.current = newWidth;
                }
            }, 100);
        };

        // Add event listener for window resize
        window.addEventListener("resize", handleResize);

        // Initial call to handle potential resize needed on component mount
        handleResize();

        return () => {
            // Cleanup
            if (resizeTimeoutRef.current)
                clearTimeout(resizeTimeoutRef.current);
            window.removeEventListener("resize", handleResize);
            parentFrame.resize();
        };
    }, [parentFrame]);

    // set the height initially and when the catalog, collections, collectionVideosMap, or searchResultsResponse change
    useEffect(() => {
        if (!catalogRef.current || !parentFrame) return;
        parentFrame.resize(catalogRef.current.clientHeight);
    }, [
        parentFrame,
        catalog,
        collections,
        collectionVideosMap,
        searchResultsResponse
    ]);

    // reset the height of the catalog when the search is reset
    useEffect(() => {
        if (parentFrame) {
            const handleSearchReset = () => {
                setTimeout(() => {
                    if (catalogRef.current) {
                        parentFrame.resize(catalogRef.current.clientHeight);
                    }
                }, 50);
            };

            eventBus.on("searchReset", handleSearchReset);
            return () => {
                eventBus.off("searchReset", handleSearchReset);
            };
        }
    }, [parentFrame]);

    useEffect(() => {
        if (!catalog?.details?.id) return;
        trackEvent("Catalog Loaded", undefined, true);
    }, [catalog?.details?.id]); // eslint-disable-line react-hooks/exhaustive-deps

    // Wait until catalog/collection has loaded before rendering
    if (configuredCollectionId ? !hasLoadedCollections : !hasLoadedCatalog) {
        return null;
    }

    // Destructure the Catalog and check if the featuredTrailer is enabled and available
    const { details, featuredTrailer } = catalog;
    const featuredTrailerIsEnabledAndAvailable =
        details?.enableFeaturedBroadcast && featuredTrailer;

    return (
        <div
            ref={catalogRef}
            className={cx("catalog", {
                "no-horizontal-padding": !(
                    (!!configuredCollectionId &&
                        collections?.collections?.[0]?.details
                            ?.enableHorizontalPadding) ||
                    (!!configuredCatalogId && details?.enableHorizontalPadding)
                )
            })}
        >
            {featuredTrailerIsEnabledAndAvailable && !currentSearchTerm && (
                <FeaturedTrailer broadcast={featuredTrailer} />
            )}
            {showPurchaseButton && (
                <div className={cx("purchase-header")}>
                    <div className={cx("title")}>
                        <h2>
                            {isOnlySubscribe
                                ? t("headers:subscribe-to-catalog")
                                : t("headers:purchase-catalog")}
                        </h2>
                        <PurchaseButton isOnlySubscribe={isOnlySubscribe} />
                    </div>
                    <RestoreAccessPrompt variant="Catalog" />
                </div>
            )}
            {enableCatalogSearch &&
                !!configuredCatalogId &&
                catalog?.creatorIsValid && <Search />}
            {!currentSearchTerm && <Collections />}
        </div>
    );
};
