import { ReactNode, useMemo } from "react";
import {
    CollectionResponse,
    CollectionVideoResponse
} from "@switcherstudio/player-api-client";
import TicketIcon from "assets/icons/gated.svg?react";
import LockIcon from "assets/icons/lock.svg?react";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import { useGatedContent } from "hooks/useGatedContent";
const cx = classNames.bind(styles);

export interface VideoBadge {
    slug: string;
    text?: string;
    icon?: ReactNode;
}

interface CollectionVideoBadgesProps {
    collection: CollectionResponse;
    collectionVideo: CollectionVideoResponse;
}

export const CollectionVideoBadges = ({
    collection,
    collectionVideo
}: CollectionVideoBadgesProps) => {
    const {
        playlistBroadcastPurchaseEntitlements,
        playlistBroadcastEmailEntitlements,
        playlistBroadcastPasswordEntitlements,
        playlistBroadcastIsEntitled
    } = useGatedContent(collectionVideo);

    const isFeaturedTrailer = useMemo<boolean>(
        () =>
            collection?.details?.idleBroadcastId ===
                collectionVideo?.details?.id &&
            collection?.details?.idleState === "SelectVideo",
        [collection, collectionVideo]
    );

    const videoBadges = useMemo<VideoBadge[]>(() => {
        let badges = [];

        if (collectionVideo?.broadcast?.details?.broadcastStatus === "Active")
            badges.push({
                slug: "live",
                text: "Live"
            });

        const videoNonPurchaseEntitlements = [
            ...playlistBroadcastEmailEntitlements,
            ...playlistBroadcastPasswordEntitlements
        ];

        if (
            playlistBroadcastPurchaseEntitlements.length > 0 &&
            !playlistBroadcastIsEntitled
        ) {
            badges.push({
                slug: "entitlement",
                icon: <TicketIcon />
            });
        } else if (
            videoNonPurchaseEntitlements.length > 0 &&
            !playlistBroadcastIsEntitled
        ) {
            badges.push({
                slug: "entitlement",
                icon: <LockIcon />
            });
        }

        if (
            collection?.isGated &&
            !collection?.isEntitled &&
            isFeaturedTrailer
        ) {
            badges.push({
                slug: "free",
                text: "FREE"
            });
        }

        return badges;
    }, [
        collection,
        collectionVideo,
        isFeaturedTrailer,
        playlistBroadcastEmailEntitlements,
        playlistBroadcastIsEntitled,
        playlistBroadcastPasswordEntitlements,
        playlistBroadcastPurchaseEntitlements
    ]);

    return (
        <>
            {videoBadges.length > 0 && (
                <div className={cx("video-badges")}>
                    {videoBadges.map(({ slug, icon, text }) => {
                        return (
                            <div className={cx("badge", slug)} key={slug}>
                                {icon}
                                {text && text}
                            </div>
                        );
                    })}
                </div>
            )}
        </>
    );
};
