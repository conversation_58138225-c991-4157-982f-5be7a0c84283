.video-badges {
    position: absolute;
    top: rem(4);
    left: rem(4);
    border-radius: rem(8);
    font-size: rem(10);
    padding: rem(2) rem(6);

    display: flex;
    flex-direction: row;
    gap: rem(1);

    .video-badge {
        color: rgba(var(--interactive-panel-text-color-rgb), 0.8);
        border: 0.09rem solid rgba(var(--interactive-panel-text-color-rgb), 0.8);
    }

    svg {
        width: rem(14);
        height: rem(14);
    }

    .live {
        background-color: #da1f0e;
        color: #ffffff;
        text-transform: uppercase;
        font-weight: 700;
        border: none;
    }

    .entitlement {
        background-color: #000;
        color: #fff;
        svg {
            width: rem(16);
            height: rem(16);
        }
    }

    .free {
        background-color: #000;
        color: #fff;
    }
}
