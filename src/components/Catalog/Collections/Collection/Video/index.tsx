import React from "react";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import { Image } from "components/Image";
import { useSelector } from "react-redux";
import { RootState } from "store/reducers";
import {
    CollectionResponse,
    CollectionVideoResponse
} from "@switcherstudio/player-api-client";
import { CollectionVideoBadges } from "./CollectionVideoBadges";
import { useCollectionVideoThumbnail } from "hooks/useCollectionVideoThumbnail";
import { SkeletonVideo } from "../SkeletonVideo";
import { eventBus } from "helpers/eventBus";
import PlaylistIcon from "assets/icons/playlist.svg?react";

const cx = classNames.bind(styles);

interface VideoProps {
    collection: CollectionResponse;
    collectionVideo: CollectionVideoResponse;
    isVertical: boolean;
    isFeaturedTrailer: boolean;
    showTitle: boolean;
    /**
     * Whether to show the collection chip below the video thumbnail.
     * Appears below title.
     */
    showCollectionChip?: boolean;
    onDemandPreselectedPageNumber: number;
    upcomingPreselectedPageNumber: number;
}

export const Video = ({
    collection,
    collectionVideo,
    isVertical,
    isFeaturedTrailer,
    showTitle,
    showCollectionChip = false,
    onDemandPreselectedPageNumber,
    upcomingPreselectedPageNumber
}: VideoProps) => {
    const { parentFrame } = useSelector((s: RootState) => s.videoSession);

    const catalogId = useSelector(
        (s: RootState) => s.catalogState?.catalog?.details?.id
    );

    const { thumbnail, scheduledThumbnail } = useCollectionVideoThumbnail({
        collectionVideo,
        variant: "compact"
    });

    return (
        <a
            className={cx("video")}
            onClick={() => {
                eventBus.emit("stopCasting");
                parentFrame?.openPlayer(
                    catalogId ?? "",
                    collection?.details?.id ?? "",
                    upcomingPreselectedPageNumber,
                    onDemandPreselectedPageNumber,
                    collectionVideo?.broadcast?.details?.id,
                    collectionVideo?.isEntitled || isFeaturedTrailer
                );
            }}
        >
            <div className={cx("video-overlay")}>
                <div
                    className={cx("video-thumbnail-container", {
                        vertical: isVertical
                    })}
                >
                    {thumbnail && (
                        <Image
                            src={thumbnail}
                            alt="video thumbnail"
                            className={cx("video-thumbnail")}
                            placeholder={
                                <SkeletonVideo isVertical={isVertical} />
                            }
                        />
                    )}
                    {scheduledThumbnail && (
                        <div className={cx("video-thumbnail", "future")}>
                            {scheduledThumbnail}
                        </div>
                    )}
                </div>
            </div>
            {!!collection && (
                <CollectionVideoBadges
                    collection={collection}
                    collectionVideo={collectionVideo}
                />
            )}
            {showTitle && <p>{collectionVideo?.broadcast?.details?.title}</p>}
            {showCollectionChip && (
                <div className={cx("collection-chip")}>
                    <PlaylistIcon />
                    <p>{collection?.details?.name?.toLocaleUpperCase()}</p>
                </div>
            )}
        </a>
    );
};
