.video {
    position: relative;
    cursor: pointer;
    display: block;

    &:hover {
        .video-overlay:before {
            opacity: 0.3;
        }
    }

    p {
        width: 100%;
        font-size: rem(12);
        line-height: rem(14);
        margin-bottom: 0;
        text-align: center;
        color: rgba(var(--embed-text-color-rgb));
        @include wrapTextOverflow();
    }

    .collection-chip {
        border: 1px solid rgba(var(--embed-text-color-rgb));
        border-radius: 0.5rem;
        height: 1.5rem;
        padding: 0.2rem;
        font-size: 0.75rem;
        text-align: center;
        color: rgba(var(--embed-text-color-rgb));
        margin-top: 0.5rem;
        overflow: hidden;
        display: flex;
        align-items: center;

        svg {
            float: left;
            margin-left: 0.5rem;
            color: rgba(var(--embed-text-color-rgb));
        }

        p {
            margin: 0;
            margin-left: -1rem;
            margin-top: 0.05rem;
            text-wrap: nowrap;
        }
    }

    .video-overlay {
        position: relative;
        container: video-overlay / inline-size;

        &:before {
            content: "";
            display: block;
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            background-color: #000;
            opacity: 0;
            border-radius: rem(8);
            transition: 0.3s opacity ease-in-out;
            z-index: 10;
        }

        .video-thumbnail-container {
            display: block;
            width: 100%;
            height: auto;
            position: relative;
            overflow: hidden;

            border-radius: rem(8);
            width: 100%;
            aspect-ratio: 16 / 9;

            &.vertical {
                aspect-ratio: 9 / 16;
            }

            .video-thumbnail {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: contain;
                background-color: #000;

                &.future {
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    background: rgba(0, 0, 0, 0.4);
                    backdrop-filter: blur(rem(4));

                    svg {
                        height: 100%;
                        width: 80%;
                        & > * {
                            fill: white;
                        }
                    }
                }
            }
        }
    }
}
