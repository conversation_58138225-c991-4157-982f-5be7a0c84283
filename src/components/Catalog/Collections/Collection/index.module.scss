.collection {
    margin: 0 0 rem(26) 0;
    display: flex;
    flex-direction: column;

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: rgba(var(--embed-text-color-rgb));

        .info {
            font-size: rem(11);
            font-weight: 500;
            line-height: rem(18);
            margin: 0;
            @include wrapTextOverflow();

            strong {
                font-size: rem(18);
                font-weight: 700;
            }
        }
    }
}

.collection-tab-container {
    .collection-tabs {
        display: block;
        border-bottom: rem(0.5) solid var(--embed-text-color);

        .collection-tab-item {
            font-weight: 500;
            font-size: rem(12);
            padding: 0 rem(8);
            line-height: rem(24);
            height: rem(48);
            text-align: center;
            text-transform: uppercase;
            border: none;
            color: var(--embed-text-color);
            position: relative;
            transition: 0.25s color ease-in-out;
            background: var(--embed-background-color);

            &:after {
                content: "";
                display: block;
                position: absolute;
                opacity: 0;
                left: 0;
                bottom: 0;
                height: rem(3);
                width: 100%;
                background-color: var(--embed-text-color);
                transition: 0.25s opacity ease-in-out;
            }

            &:hover {
                color: var(--embed-text-color);
                &:after {
                    opacity: 0.2;
                }
            }

            &.active {
                color: var(--embed-text-color);
                font-weight: 700;
                &:after {
                    opacity: 1;
                }
            }
        }
    }

    .collection-tab-content-container {
        margin-top: rem(12);
    }
}
