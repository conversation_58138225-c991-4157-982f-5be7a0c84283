import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import "swiper/css";
import SwiperCore from "swiper";
import { Swiper, SwiperSlide } from "swiper/react";
import { Video } from "../Video";
import { Navigation } from "swiper/modules";
import RightChevron from "assets/icons/chevron-right.svg?react";
import {
    CatalogSearchPagedResponse,
    CollectionResponse,
    CollectionVideoResponse,
    CollectionVideosPagedResponse
} from "@switcherstudio/player-api-client";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/reducers";
import { AppDispatch } from "store/store";
import {
    getCollectionVideosOnDemand,
    getCollectionVideosUpcoming,
    getNextSearchVideos
} from "store/Catalog/thunks";
import { LoadMoreButton } from "components/Buttons/LoadMoreButton";
import { useTranslation } from "react-i18next";
import { eventBus } from "helpers/eventBus";
import { useEventTracking } from "hooks/useEventTracking";

const cx = classNames.bind(styles);

interface CollectionProps {
    collection?: CollectionResponse;
    collectionVideoDataType: "on-demand" | "upcoming" | "search";
    /* it is imperative that you check the type of this prop
    by referencing the collectionVideoType before using it */
    collectionVideoData:
        | CollectionVideosPagedResponse
        | CatalogSearchPagedResponse;
}

export const CollectionContent = ({
    collection = [] as unknown as CollectionResponse,
    collectionVideoDataType,
    collectionVideoData
}: CollectionProps) => {
    const swiperRef = useRef<SwiperCore>();
    const [disabledPrev, setDisablePrev] = useState<boolean>(true);
    const [disabledNext, setDisableNext] = useState<boolean>(true);
    const [error, setError] = useState<any>();
    const dispatch = useDispatch<AppDispatch>();
    const { t } = useTranslation();
    const { trackEvent } = useEventTracking();
    const { catalog, configuredCatalogId } = useSelector(
        (s: RootState) => s.catalogState
    );

    useEffect(() => {
        const handleResize = () => {
            swiperRef.current?.update();
        };
        eventBus.on("catalogResize", handleResize);
        return () => {
            eventBus.off("catalogResize", handleResize);
        };
    }, []);

    const showLoadMore = useMemo(() => {
        return (
            (collectionVideoData?.page ?? 0) <
            (collectionVideoData?.totalPages ?? 0)
        );
    }, [collectionVideoData]);

    const isVertical = useMemo(() => {
        return (
            collectionVideoDataType !== "search" &&
            collection?.details?.aspectRatio === "NineBySixteen"
        );
    }, [collection?.details?.aspectRatio, collectionVideoDataType]);

    const showTitles = useMemo(() => {
        if (collectionVideoDataType === "search") {
            return true; // Always show titles in search results
        } else {
            return (
                ((!!configuredCatalogId && catalog?.details?.showTitles) ||
                    (!configuredCatalogId &&
                        collection?.details?.showTitles)) ??
                true
            );
        }
    }, [configuredCatalogId, catalog, collection, collectionVideoDataType]);

    const handleLoadMore = useCallback(async () => {
        const collectionId = collection?.details?.id ?? "";
        try {
            if (collectionVideoDataType === "upcoming") {
                await dispatch(getCollectionVideosUpcoming({ collectionId }));
            }

            if (collectionVideoDataType === "on-demand") {
                await dispatch(getCollectionVideosOnDemand({ collectionId }));
            }

            if (collectionVideoDataType === "search") {
                await dispatch(getNextSearchVideos());
            }

            trackEvent("Load More Videos", {
                collectionId,
                page: collectionVideoData?.page ?? 0,
                type: collectionVideoDataType
            });
        } catch (error) {
            setError(error);
        }
    }, [
        collection?.details?.id,
        collectionVideoDataType,
        collectionVideoData?.page,
        trackEvent,
        dispatch
    ]);

    const onDemandPreselectedPageNumber = useMemo(() => {
        return collectionVideoDataType === "on-demand"
            ? collectionVideoData?.page ?? 1
            : 1;
    }, [collectionVideoData?.page, collectionVideoDataType]);

    const upcomingPreselectedPageNumber = useMemo(() => {
        return collectionVideoDataType === "upcoming"
            ? collectionVideoData?.page ?? 1
            : 1;
    }, [collectionVideoData?.page, collectionVideoDataType]);

    const loadMoreButtonText = useMemo(() => {
        switch (collectionVideoDataType) {
            case "on-demand":
                return t("collection-videos:load-more");
            case "upcoming":
                return t("collection-videos:load-more");
            case "search":
                return t("search-bar:load-more");
            default:
                return t("collection-videos:load-more");
        }
    }, [collectionVideoDataType, t]);

    const LoadMore = useMemo(() => {
        return (
            <LoadMoreButton
                text={loadMoreButtonText}
                variant="video"
                onClick={handleLoadMore}
                error={error}
                isVertical={isVertical}
            />
        );
    }, [error, handleLoadMore, isVertical, loadMoreButtonText]);

    const swiperWrapper = useMemo(() => {
        return (
            <div className={cx("slider")}>
                <Swiper
                    key={`${isVertical}`}
                    spaceBetween={12}
                    slidesPerView={isVertical ? 2.5 : 1.75}
                    modules={[Navigation]}
                    breakpoints={{
                        720: {
                            slidesPerView: isVertical ? 6 : 4
                        },
                        915: {
                            slidesPerView: isVertical ? 9 : 6
                        }
                    }}
                    onBeforeInit={(swiper) => {
                        swiperRef.current = swiper;
                    }}
                    onUpdate={(swiper) => {
                        setDisablePrev(swiper.isBeginning);
                        setDisableNext(swiper.isEnd);
                    }}
                    onActiveIndexChange={(swiper) => {
                        setDisablePrev(swiper.isBeginning);
                        setDisableNext(swiper.isEnd);
                    }}
                    onResize={(swiper) => {
                        // Determine the height of the thumbnail based on the slide width and aspect ratio
                        const height =
                            // @ts-expect-error - Swiper's types are outdated
                            swiper.slidesSizesGrid[0] *
                            (isVertical ? 16 / 9 : 9 / 16);

                        // Set the thumbnail height as a CSS variable to be used to center the prev/next buttons
                        // @ts-expect-error - Swiper's types are outdated
                        swiper.hostEl.parentElement.style.setProperty(
                            "--thumbnail-height",
                            `${height}px`
                        );
                    }}
                >
                    {(
                        collectionVideoData as CollectionVideosPagedResponse
                    )?.items?.map((video, index) => (
                        <SwiperSlide key={video?.details?.id ?? index}>
                            <Video
                                key={index}
                                collection={collection}
                                collectionVideo={video}
                                showTitle={showTitles}
                                isVertical={isVertical}
                                isFeaturedTrailer={
                                    video?.details?.id ===
                                    collection.featuredTrailer?.details?.id
                                }
                                onDemandPreselectedPageNumber={
                                    onDemandPreselectedPageNumber
                                }
                                upcomingPreselectedPageNumber={
                                    upcomingPreselectedPageNumber
                                }
                            />
                        </SwiperSlide>
                    ))}
                    {showLoadMore && (
                        <SwiperSlide
                            key={collection?.details?.id}
                            className={cx("swiper-tile")}
                        >
                            {LoadMore}
                        </SwiperSlide>
                    )}
                </Swiper>
                {!(disabledPrev && disabledNext) && (
                    <>
                        <button
                            className={cx("slider-nav", "slider-nav-prev", {
                                disabled: disabledPrev
                            })}
                            onClick={() => {
                                swiperRef.current?.slidePrev();
                            }}
                            title="Previous"
                            type="button"
                        >
                            <RightChevron />
                        </button>
                        <button
                            className={cx("slider-nav", "slider-nav-next", {
                                disabled: disabledNext
                            })}
                            onClick={() => {
                                swiperRef.current?.slideNext();
                            }}
                            title="Next"
                            type="button"
                        >
                            <RightChevron />
                        </button>
                    </>
                )}
            </div>
        );
    }, [
        LoadMore,
        collection,
        collectionVideoData,
        disabledNext,
        disabledPrev,
        isVertical,
        showLoadMore,
        showTitles,
        onDemandPreselectedPageNumber,
        upcomingPreselectedPageNumber
    ]);

    const gridWrapper = useMemo(() => {
        return collectionVideoDataType !== "search" ? (
            <div className={cx("grid", isVertical ? "vertical" : "")}>
                {(
                    collectionVideoData as CollectionVideosPagedResponse
                )?.items?.map((video, index) => (
                    <Video
                        key={index}
                        collection={collection}
                        collectionVideo={video}
                        showTitle={showTitles}
                        isVertical={isVertical}
                        isFeaturedTrailer={
                            video?.details?.id ===
                            collection?.featuredTrailer?.details?.id
                        }
                        onDemandPreselectedPageNumber={
                            onDemandPreselectedPageNumber
                        }
                        upcomingPreselectedPageNumber={
                            upcomingPreselectedPageNumber
                        }
                    />
                ))}
                {showLoadMore && LoadMore}
            </div>
        ) : (
            <div className={cx("grid")}>
                {(collectionVideoData as CatalogSearchPagedResponse)?.videos
                    ?.length &&
                    (
                        collectionVideoData as CatalogSearchPagedResponse
                    )?.videos?.map((video, index) => (
                        <Video
                            key={index}
                            collection={video?.collection as CollectionResponse}
                            collectionVideo={
                                video?.video as CollectionVideoResponse
                            }
                            showTitle={showTitles}
                            isVertical={false} // Search results are always horizontal
                            isFeaturedTrailer={false} // Search results do not have featured trailers
                            onDemandPreselectedPageNumber={
                                onDemandPreselectedPageNumber
                            }
                            upcomingPreselectedPageNumber={
                                upcomingPreselectedPageNumber
                            }
                            showCollectionChip={true}
                        />
                    ))}
                {showLoadMore && LoadMore}
            </div>
        );
    }, [
        LoadMore,
        collection,
        collectionVideoData,
        isVertical,
        showLoadMore,
        showTitles,
        onDemandPreselectedPageNumber,
        upcomingPreselectedPageNumber,
        collectionVideoDataType
    ]);

    const videos = useMemo(() => {
        switch (
            !!configuredCatalogId
                ? catalog?.details?.embeddedDisplay
                : collection.details?.embeddedDisplay
        ) {
            case "Grid":
                return gridWrapper;
            case "Carousel":
            default:
                return swiperWrapper;
        }
    }, [
        configuredCatalogId,
        catalog?.details?.embeddedDisplay,
        collection.details?.embeddedDisplay,
        gridWrapper,
        swiperWrapper
    ]);

    return (
        <>
            <div className={cx("collection-content")}>
                {collectionVideoDataType !== "search" ? (
                    (collectionVideoData as CollectionVideosPagedResponse)
                        ?.items?.length ? (
                        videos
                    ) : (
                        <p
                            className={cx("empty-state", {
                                "is-vertical": isVertical
                            })}
                        >
                            No {collectionVideoDataType} content in this
                            collection
                        </p>
                    )
                ) : (
                    gridWrapper
                )}
            </div>
        </>
    );
};
