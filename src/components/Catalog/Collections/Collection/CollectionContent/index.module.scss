.collection-content {
    .slider {
        position: relative;
        overflow: visible;

        :global {
            .swiper {
                overflow-y: visible;

                @include mobile-no-orientation {
                    overflow: visible;
                }
            }
        }

        .slider-nav {
            position: absolute;
            z-index: 11;
            background: none;
            border: none;
            width: rem(34);
            height: rem(34);
            background-color: rgba(33, 37, 41, 0.6);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: opacity 0.25s ease-in;

            // center the nav buttons vertically on the image thumbnail
            // 120px is a common/average thumbnail height; actual thumbnail height is set in the swiper's onResize event
            // 34px is the button icon height
            top: calc((var(--thumbnail-height, 120px) - 34px) / 2);

            &.disabled {
                opacity: 0;
                cursor: default;
            }

            > svg {
                color: #fff;
            }

            &.slider-nav-prev {
                left: rem(4);
                transform: rotateY(180deg);
            }

            &.slider-nav-next {
                right: rem(4);
            }

            @include mobile-no-orientation {
                display: none;
            }
        }
    }

    .grid {
        display: grid;
        grid-template-columns: repeat(1, minmax(0, 1fr));
        gap: rem(64) rem(16);

        @include tablet {
            grid-template-columns: repeat(4, minmax(0, 1fr));
        }

        @include desktop {
            grid-template-columns: repeat(6, minmax(0, 1fr));
        }

        button {
            font-size: small;
        }

        &.vertical {
            grid-template-columns: repeat(3, minmax(0, 1fr));
            gap: rem(88) rem(16);

            @include tablet {
                grid-template-columns: repeat(6, minmax(0, 1fr));
            }

            @include desktop {
                grid-template-columns: repeat(9, minmax(0, 1fr));
            }
        }
    }

    .empty-state {
        font-size: rem(14);
        color: var(--embed-text-color);
        width: 100%;

        // adjust margin bottom to limit layout shift
        margin: 0 0 calc(100% / ((16 / 9) * 6) + rem(2));

        @include tablet {
            margin: 0 0 calc(100% / ((16 / 9) * 4) + rem(2));
        }

        @include mobile-no-orientation {
            margin: 0 0 calc(100% / ((16 / 9) * 1.75) + rem(3));
        }

        &.is-vertical {
            margin: 0 0 calc(100% / ((9 / 16) * 9) + rem(-11));

            @include tablet {
                margin: 0 0 calc(100% / ((9 / 16) * 6) + rem(-11));
            }

            @include mobile-no-orientation {
                margin: 0 0 calc(100% / ((9 / 16) * 2.5) + rem(-7));
            }
        }
    }
}
.swiper-tile {
    height: unset !important;
}
