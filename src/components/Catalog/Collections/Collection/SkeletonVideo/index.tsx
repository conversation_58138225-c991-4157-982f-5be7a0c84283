import React from "react";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
const cx = classNames.bind(styles);

interface SkeletonVideoProps {
    // collectionId: string;
    isVertical?: boolean;
}

export const SkeletonVideo = ({ isVertical = false }: SkeletonVideoProps) => {
    return (
        <div className={cx("skeleton-video")}>
            <div
                className={cx("skeleton-thumbnail", {
                    vertical: isVertical
                })}
            />
        </div>
    );
};
