.skeleton-video {
    position: relative;

    .skeleton-thumbnail {
        display: block;
        width: 100%;
        height: auto;
        border-radius: rem(8);
        background-color: #d6d6d6;
        width: 100%;
        object-fit: contain;
        aspect-ratio: 16 / 9;
        animation: fade 1.8s cubic-bezier(0.32, 0, 0.68, 1) infinite;
        &.vertical {
            aspect-ratio: 9 / 16;
        }
    }
}

@keyframes fade {
    0% {
        opacity: 0.3;
    }
    40% {
        opacity: 1;
    }
    60% {
        opacity: 1;
    }
    100% {
        opacity: 0.3;
    }
}
