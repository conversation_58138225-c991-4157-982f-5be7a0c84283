import { useEffect, useMemo, useRef, useState } from "react";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import "swiper/css";
import SwiperCore from "swiper";
import { PurchaseButton } from "components/Buttons/PurchaseButton";
import { CollectionResponse } from "@switcherstudio/player-api-client";
import { RootState } from "store/reducers";
import { eventBus } from "helpers/eventBus";
import { useEntitlementShortcuts } from "hooks/useEntitlementShortcuts";
import { CollectionContent } from "./CollectionContent";
import { useSelector } from "react-redux";

const cx = classNames.bind(styles);

enum CollectionTab {
    OnDemand = "On-Demand",
    Upcoming = "Upcoming"
}
interface CollectionProps {
    collection: CollectionResponse;
}

export const Collection = ({ collection }: CollectionProps) => {
    const swiperRef = useRef<SwiperCore>();
    const { collectionVideosMap, configuredCatalogId } = useSelector(
        (s: RootState) => s.catalogState
    );

    const collectionVideoData = useMemo(
        () => collectionVideosMap?.[collection.details?.id ?? ""],
        [collectionVideosMap, collection.details?.id]
    );

    const [activeTab, setActiveTab] = useState<CollectionTab | undefined>(
        undefined
    );

    // handle setting the default visible tab
    useEffect(() => {
        if (!collectionVideoData) return;

        setActiveTab(
            collection?.details?.defaultCollectionTab === "OnDemand" &&
                !collection?.details?.isLivestreamPlayer
                ? CollectionTab.OnDemand
                : CollectionTab.Upcoming
        );
    }, [
        collection?.details?.defaultCollectionTab,
        collection?.details?.isLivestreamPlayer,
        collectionVideoData
    ]);

    // resize the embed when a tab changes
    useEffect(() => {
        eventBus.emit("catalogResize");
    }, [activeTab]);

    useEffect(() => {
        const handleResize = () => {
            swiperRef.current?.update();
        };
        eventBus.on("catalogResize", handleResize);
        return () => {
            eventBus.off("catalogResize", handleResize);
        };
    }, []);

    const { showPurchaseButton, isOnlySubscribe } = useEntitlementShortcuts(
        collection,
        "Collection"
    );

    const videoCount = useMemo(() => {
        const onDemandCount = collectionVideoData?.OnDemand?.totalRecords ?? 0;
        const upcomingCount = collectionVideoData?.Upcoming?.totalRecords ?? 0;
        const totalCount = onDemandCount + upcomingCount;

        if (totalCount === 0) {
            return "";
        } else if (totalCount === 1) {
            return "1 Video";
        } else {
            return `${totalCount} Videos`;
        }
    }, [collectionVideoData]);

    if (!!configuredCatalogId && !collection.details?.showInCatalog)
        return null;

    return (
        <div className={cx("collection")}>
            <div className={cx("header")}>
                <p className={cx("info")}>
                    <strong>
                        {collection.details?.name !== ""
                            ? collection.details?.name
                            : "Example Collection"}
                    </strong>
                    <br />
                    {videoCount}
                </p>
                {showPurchaseButton && (
                    <PurchaseButton
                        playerId={collection.details?.id}
                        isOnlySubscribe={isOnlySubscribe}
                    />
                )}
            </div>
            <div className={cx("collection-tab-container")}>
                {!collection?.details?.isLivestreamPlayer &&
                    !!collection?.details?.id && (
                        <div className={cx("collection-tabs")}>
                            <button
                                className={cx("collection-tab-item", {
                                    active: activeTab == CollectionTab.OnDemand
                                })}
                                onClick={() =>
                                    setActiveTab(CollectionTab.OnDemand)
                                }
                            >
                                {`On-Demand ${
                                    collectionVideoData?.OnDemand
                                        ?.totalRecords !== undefined
                                        ? `(${collectionVideoData?.OnDemand?.totalRecords})`
                                        : ""
                                }`}
                            </button>
                            <button
                                className={cx("collection-tab-item", {
                                    active: activeTab == CollectionTab.Upcoming
                                })}
                                onClick={() =>
                                    setActiveTab(CollectionTab.Upcoming)
                                }
                            >
                                {`Upcoming ${
                                    collectionVideoData?.Upcoming
                                        ?.totalRecords !== undefined
                                        ? `(${collectionVideoData?.Upcoming?.totalRecords})`
                                        : ""
                                }`}
                            </button>
                        </div>
                    )}
                <div className={cx("collection-tab-content-container")}>
                    {activeTab == CollectionTab.OnDemand && (
                        <CollectionContent
                            collection={collection}
                            collectionVideoData={collectionVideoData?.OnDemand}
                            collectionVideoDataType="on-demand"
                        />
                    )}
                    {activeTab == CollectionTab.Upcoming && (
                        <CollectionContent
                            collection={collection}
                            collectionVideoData={collectionVideoData?.Upcoming}
                            collectionVideoDataType="upcoming"
                        />
                    )}
                </div>
            </div>
        </div>
    );
};
