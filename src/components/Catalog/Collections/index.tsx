import { useDispatch, useSelector } from "react-redux";
import { Collection } from "./Collection";
import styles from "./index.module.scss";
import { RootState } from "store/reducers";
import { useTranslation } from "react-i18next";
import { getNextCollections } from "store/Catalog/thunks";
import { AppDispatch } from "store/store";
import { useMemo, useState } from "react";
import { LoadMoreButton } from "components/Buttons/LoadMoreButton";
import { useEventTracking } from "hooks/useEventTracking";

export const Collections = () => {
    const { t } = useTranslation();
    const dispatch = useDispatch<AppDispatch>();
    const { trackEvent } = useEventTracking();
    const [error, setError] = useState<any>();
    const { collections } = useSelector((s: RootState) => s.catalogState);

    const hasMoreCollectionsToLoad = useMemo<boolean>(
        () => (collections?.page ?? 0) < (collections?.totalPages ?? 0),
        [collections]
    );

    const handleLoadMore = () => {
        try {
            dispatch(getNextCollections());

            trackEvent("Load More Collections", {
                page: collections?.page ?? 0
            });
        } catch (error) {
            setError(error);
        }
    };

    return (
        collections && (
            <div className={styles["collections"]}>
                {collections.collections?.map((collection, index) => (
                    <Collection
                        key={collection.details?.id ?? index}
                        collection={collection}
                    />
                ))}
                {hasMoreCollectionsToLoad && (
                    <div className={styles["load-more"]}>
                        <LoadMoreButton
                            variant="collection"
                            text={t("collections:load-more")}
                            onClick={handleLoadMore}
                            error={error}
                        />
                    </div>
                )}
            </div>
        )
    );
};
