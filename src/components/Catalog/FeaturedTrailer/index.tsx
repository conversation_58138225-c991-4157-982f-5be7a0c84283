import React from "react";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import { EmbeddedVideoPlayerLite } from "components/EmbeddedVideoPlayerLite";
import { BroadcastResponse } from "@switcherstudio/player-api-client";
import { isNullOrWhitespace } from "helpers/stringHelpers";
import timeSince from "helpers/timeFrom";
const cx = classNames.bind(styles);

interface FeaturedTrailerProps {
    broadcast: BroadcastResponse;
}

export const FeaturedTrailer: React.FC<FeaturedTrailerProps> = ({
    broadcast
}) => {
    const isVertical =
        (broadcast?.videos?.[0]?.details?.input?.width ?? 0) <
        (broadcast?.videos?.[0]?.details?.input?.height ?? 0);

    return (
        <div
            className={cx("featured-trailer", {
                "is-vertical": isVertical
            })}
        >
            <div className={cx("player-wrapper")}>
                <div className={cx("player-container")}>
                    <EmbeddedVideoPlayerLite preloadedBroadcast={broadcast} />
                </div>
            </div>
            <div className={cx("info-container")}>
                <div className={cx("title")}>{broadcast.details?.title}</div>
                <div className={cx("meta")}>
                    {`${timeSince(
                        new Date(broadcast?.details?.endedAt ?? Date.now())
                    )} • ${Math.round(
                        (broadcast?.videos?.[0]?.details?.duration ??
                            (0 as number)) / 60
                    )} min`}
                </div>
                {!isNullOrWhitespace(broadcast.details?.description) && (
                    <p>{broadcast.details?.description}</p>
                )}
            </div>
        </div>
    );
};
