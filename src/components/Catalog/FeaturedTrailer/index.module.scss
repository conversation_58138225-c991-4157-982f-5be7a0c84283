$vertical-max-height: rem(640);
$vertical-max-width: calc($vertical-max-height * 9 / 16);

.featured-trailer {
    display: grid;
    align-items: center;
    gap: rem(22);
    grid-template-columns: 62.5% 1fr;

    &.is-vertical {
        max-height: $vertical-max-height;
        grid-template-columns: 1fr 62.5%;

        .player-wrapper {
            .player-container {
                padding-top: 177.77%;
            }

            max-width: $vertical-max-width;
            max-height: $vertical-max-height;

            @container catalog (width < 720px) {
                max-width: none;
                max-height: 100%;
            }
        }

        @container catalog (width < 720px) {
            max-height: none;
        }
    }

    @container catalog (width < 720px) {
        grid-template-columns: 1fr;

        &.is-vertical {
            grid-template-columns: 1fr;
        }
    }

    .player-wrapper {
        width: 100%;

        .player-container {
            position: relative;
            width: 100%;
            height: 0;
            padding-top: 56.25%;

            border-radius: rem(8);
            overflow: hidden;
        }
    }

    .info-container {
        padding-right: rem(21);
        justify-self: start;

        @container catalog (width < 720px) {
            width: 100%;
            padding-right: 0;
        }

        .title {
            color: rgba(var(--embed-text-color-rgb));
            font-weight: 600;
            font-size: rem(16);
            line-height: rem(22);
        }

        .meta {
            margin-top: rem(2);
            font-weight: 400;
            font-size: rem(14);
            line-height: rem(20);
            color: rgba(var(--embed-text-color-rgb), 0.5);
        }
        p {
            padding-top: rem(16);
            border-top: rem(1) solid
                rgba(var(--embed-background-contrast-color-rgb), 0.1);
            margin-top: rem(16);
            font-size: rem(14);
            font-weight: 400;
            line-height: rem(20);
            color: rgba(var(--embed-text-color-rgb));
        }
    }
}
