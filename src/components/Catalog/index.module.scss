.catalog {
    display: flex;
    flex-direction: column;
    gap: rem(24);
    overflow: hidden;
    padding: rem(16) rem(16) rem(64) rem(16);
    background-color: rgba(var(--embed-background-color-rgb));
    container: catalog / inline-size;

    @include tablet {
        padding: rem(24) rem(40) rem(64) rem(40);
    }

    @include desktop {
        padding: rem(24) rem(80) rem(64) rem(80);
    }

    &.no-horizontal-padding {
        padding-left: 0;
        padding-right: 0;
    }
}

.purchase-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: rem(12);

    @include mobile-no-orientation {
        flex-direction: column;
        align-items: flex-start;
        gap: rem(8);
    }

    .title {
        gap: rem(12);
        display: flex;
        align-items: center;
        color: rgba(var(--embed-text-color-rgb));

        @include mobile-no-orientation {
            width: 100%;
            flex-direction: column;
            align-items: flex-start;
            gap: rem(8);
        }

        h2 {
            margin: 0;
        }

        button {
            @include mobile-no-orientation {
                display: block;
                width: 100%;
            }
        }
    }
}
