import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/reducers";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import "shaka-player/dist/controls.css";
import { VideoUrl } from "components/EmbeddedVideoPlayer";
import { ShakaVideoElement } from "./components/ShakaVideoElement";
import { useShakaInit } from "./hooks/useShakaInit";
import { useHideShowControls } from "./hooks/useHideShowControls";
import { MediaControlBar } from "./components/MediaControlBar";
import { VideoEndedPrompts } from "components/VideoEndedPrompts";
import { ContextMenus } from "store/VideoSession/types";
import { CollectionResponse } from "@switcherstudio/player-api-client";
import { GatedOverlay } from "components/Overlays/GatedOverlay";
import { useEffect, useState } from "react";
import { setPlayingState } from "store/VideoSession/slice";
import { usePlayerKeypressControls } from "hooks/usePlayerKeypressControls";
import { eventBus } from "helpers/eventBus";
const cx = classNames.bind(styles);

export interface ShakaPlayerProps {
    src: VideoUrl | undefined | null;
    poster: string | undefined;
    isLive: boolean;
    isFeaturedTrailer?: boolean;
    isLitePlayer?: boolean;
    collection?: CollectionResponse;
    broadcastIsRestricted?: boolean;
}

const ShakaPlayer = ({
    src,
    poster,
    isFeaturedTrailer,
    isLitePlayer,
    collection,
    broadcastIsRestricted
}: ShakaPlayerProps) => {
    const dispatch = useDispatch();
    const {
        video: videoRef,
        videoContainer,
        shakaPlayer,
        castingProvider,
        castingAvailable,
        isCasting,
        handleCast,
        proxyVideo,
        proxyPlayer,
        endTimeSliderInterval,
        beginTimeSliderInterval,
        hasJustCasted,
        setHasJustCasted,
        captionsAvailable,
        toggleCaptions,
        captionsVisible
    } = useShakaInit({
        src
    });

    const { activeContextMenu, isEnded, currentCollectionVideo } = useSelector(
        (s: RootState) => s.videoSession
    );

    const { showControls, fadeControls, onTouch } = useHideShowControls({
        preventFade: activeContextMenu !== ContextMenus.None
    });

    /**
     * If the video is a featured trailer, we need to pause it when it starts
     * automatically after casting session has ended.
     */
    useEffect(() => {
        if (videoRef.current && hasJustCasted) {
            setHasJustCasted(false);
            videoRef.current.addEventListener(
                "play",
                (e: Event) => {
                    const thisVideo = e.target as HTMLVideoElement;
                    thisVideo.pause();
                    dispatch(setPlayingState(false));
                },
                { once: true }
            );
        }
    }, [videoRef, dispatch, hasJustCasted, setHasJustCasted]);

    const playerToUse = isCasting ? proxyPlayer : shakaPlayer.current;
    const videoToUse = isCasting ? proxyVideo : videoRef.current;

    const [disableKeyboardControls, setDisableKeyboardControls] =
        useState(false);

    useEffect(() => {
        const disableKeyboardControls = () => {
            setDisableKeyboardControls(true);
        };

        const enablePlayerControls = () => {
            setDisableKeyboardControls(false);
        };

        eventBus.on("disableKeyboardControls", disableKeyboardControls);

        eventBus.on("enableKeyboardControls", enablePlayerControls);
        return () => {
            eventBus.off("disableKeyboardControls", disableKeyboardControls);
            eventBus.off("enableKeyboardControls", enablePlayerControls);
        };
    }, []);

    usePlayerKeypressControls(videoToUse, {
        toggleCaptions,
        disabled: disableKeyboardControls
    });

    return (
        <>
            {broadcastIsRestricted && collection ? (
                <GatedOverlay
                    poster={poster}
                    collection={collection}
                    collectionVideo={currentCollectionVideo}
                    video={videoToUse as HTMLVideoElement}
                />
            ) : (
                src && (
                    <>
                        {isEnded && (
                            <VideoEndedPrompts
                                video={videoToUse as HTMLVideoElement}
                            />
                        )}
                        <div
                            ref={videoContainer}
                            className={cx("shaka-media")}
                            data-shaka-player-cast-receiver-id={
                                import.meta.env.VITE_SHAKA_PLAYER_RECEIVER_ID
                            }
                            data-shaka-player-cast-android-receiver-compatible
                        >
                            <ShakaVideoElement
                                videoRef={videoRef}
                                video={videoToUse as HTMLVideoElement}
                                poster={poster ?? ""}
                                shakaPlayer={shakaPlayer}
                                onTouch={onTouch}
                                endTimeSliderInterval={endTimeSliderInterval}
                                beginTimeSliderInterval={
                                    beginTimeSliderInterval
                                }
                                isCasting={isCasting}
                            />
                            <MediaControlBar
                                video={videoToUse as HTMLVideoElement}
                                shakaPlayer={playerToUse}
                                videoContainer={videoContainer}
                                onTouch={onTouch}
                                showControls={showControls}
                                fadeControls={fadeControls}
                                isLitePlayer={isLitePlayer}
                                isFeaturedTrailer={isFeaturedTrailer}
                                castingProvider={castingProvider}
                                castingAvailable={castingAvailable}
                                isCasting={isCasting}
                                handleCast={handleCast}
                                captionsAvailable={captionsAvailable}
                                toggleCaptions={toggleCaptions}
                                captionsVisible={captionsVisible}
                                collectionId={collection?.details?.id}
                            />
                        </div>
                    </>
                )
            )}
        </>
    );
};
export default ShakaPlayer;
