import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/reducers";
import { useCallback } from "react";
import { AppDispatch } from "store/store";
import {
    setIsInitialBuffering,
    setIsInitialPlay,
    setTimeSliderDelay,
    setVideoJsAdapter,
    setVideoSessionVideo
} from "store/VideoSession/slice";
// @ts-expect-error Player JS is an external library that does not provide types
import playerjs from "player.js";
import { useEventTracking } from "hooks/useEventTracking";

export const useShakaCallbacks = ({
    video
}: {
    video: React.RefObject<HTMLVideoElement>;
}) => {
    const { sessionId } = useSelector((s: RootState) => s.videoSession);

    const dispatch = useDispatch<AppDispatch>();
    const { trackEvent } = useEventTracking();

    const onReady = useCallback(() => {
        dispatch(setIsInitialBuffering(false));
        dispatch(setIsInitialPlay(true));
        if (!!video?.current) {
            dispatch(setVideoSessionVideo(video.current));
            dispatch(setVideoJsAdapter(playerjs.HTML5Adapter(video.current)));
            video.current.onenterpictureinpicture = () => {
                trackEvent("Entered PiP", null, true, true);
            };

            video.current.onleavepictureinpicture = () => {
                trackEvent("Exited PiP", null, true, true);
            };
        }
        trackEvent(
            "Video Loaded",
            {
                session_id: sessionId
            },
            true
        );
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [dispatch, sessionId, trackEvent]);

    const beginTimeSliderInterval = useCallback(() => {
        dispatch(setTimeSliderDelay(100));
    }, [dispatch]);

    const endTimeSliderInterval = useCallback(() => {
        dispatch(setTimeSliderDelay(null));
    }, [dispatch]);

    return {
        beginTimeSliderInterval,
        endTimeSliderInterval,
        onReady
    };
};
