import { useCallback, useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import shaka from "shaka-player/dist/shaka-player.compiled";
import {
    setActiveContextMenu,
    setBuffering,
    setIsFullScreen,
    setLevelLoadError,
    setShakaVideoQuality,
    setStreamingProtocol,
    setVolume
} from "store/VideoSession/slice";
import { AppDispatch } from "store/store";
import { useEventTracking } from "hooks/useEventTracking";
import { RootState } from "store/reducers";
import { hasDashSupport, loadShakaPlayer, setCustomProperty } from "../helpers";
import { AdaptationEvent } from "../types";
import { VideoUrl } from "components/EmbeddedVideoPlayer";
import { useShakaCallbacks } from "./useShakaCallbacks";
import { isIOS } from "react-device-detect";
import { useBeforeUnload } from "react-router-dom";
import { eventBus } from "helpers/eventBus";
import { ContextMenus } from "store/VideoSession/types";

export enum CastingProvider {
    ChromeCast = "ChromeCast",
    AirPlay = "AirPlay",
    None = "None"
}

export const useShakaInit = ({ src }: { src: VideoUrl | undefined | null }) => {
    const video = useRef<HTMLVideoElement>(null);
    const videoContainer = useRef<HTMLDivElement>(null);
    const shakaPlayer = useRef<shaka.Player>();
    const dispatch = useDispatch<AppDispatch>();
    const { trackEvent } = useEventTracking();
    const { videoJsAdapter, isVoD } = useSelector(
        (s: RootState) => s.videoSession
    );
    const [castSessionWasActive, setCastSessionWasActive] = useState(false);
    const [captionsAvailable, setCaptionsAvailable] = useState(false);
    const [captionsVisible, setCaptionsVisible] = useState(false);

    const onFullScreenChange = useCallback(() => {
        const _isFullScreen = !!document.fullscreenElement;
        dispatch(setIsFullScreen(_isFullScreen));
        trackEvent(`${_isFullScreen ? "Enter" : "Exit"} Fullscreen`);
    }, [dispatch, trackEvent]);

    const initPlayer = useCallback(async () => {
        // Create a Player instance.
        shakaPlayer.current = new shaka.Player();
        shakaPlayer.current.setVideoContainer(videoContainer.current!);
        shakaPlayer.current.attach(video.current!);

        // Configure instance
        const defaultConfiguration = {
            streaming: {
                bufferBehind: 3600, // keep an hour behind in the buffer
                bufferingGoal: 32, // target 32 seconds target buffer
                rebufferingGoal: 8 // begin playback after 8 seconds available
            }
        };

        // If the video is live, we want to keep the buffer as small as possible
        if (!isVoD) {
            shakaPlayer.current.configure({
                ...defaultConfiguration,
                streaming: {
                    ...defaultConfiguration.streaming,
                    rebufferingGoal: 2 // begin playback after 2 seconds available
                }
            });
        } else {
            shakaPlayer.current.configure(defaultConfiguration);
        }

        try {
            const supportsDash = await hasDashSupport();
            const useDash = supportsDash && !!src?.dash && !isIOS;
            dispatch(setStreamingProtocol(useDash ? "dash" : "hls"));
            await loadShakaPlayer(useDash, shakaPlayer?.current, src);

            const textTracks = shakaPlayer.current.getTextTracks();
            if (textTracks?.length) {
                setCaptionsAvailable(true);
            }

            // dispatch(addNotification({ type: NotificationType.Success, message: "Loaded!", class: "shopping" }))
        } catch (e: any) {
            // dispatch(addNotification({ type: NotificationType.Danger, message: e?.message ?? "Error loading", class: "shopping" }))
        }
    }, [shakaPlayer, video, dispatch, isVoD, src]);

    const { onReady, beginTimeSliderInterval, endTimeSliderInterval } =
        useShakaCallbacks({ video });

    const addEventListeners = useCallback(() => {
        if (!shakaPlayer.current) return;

        // set video duration
        video.current?.addEventListener("durationchange", () => {
            setCustomProperty(
                "--sp-duration",
                video.current?.duration?.toString() ?? "0"
            );
        });

        // set playback rate
        video.current?.addEventListener("ratechange", () => {
            setCustomProperty(
                "--sp-playback-rate",
                video.current?.playbackRate?.toString() ?? "0"
            );
        });

        // set current time position
        video.current?.addEventListener("timeupdate", () => {
            setCustomProperty(
                "--sp-current-time",
                video.current?.currentTime?.toString() ?? "0"
            );
        });

        // set current volume level
        video.current?.addEventListener("volumechange", () => {
            setCustomProperty(
                "--sp-volume-level",
                video.current?.volume?.toString() ?? "0"
            );
        });

        videoContainer.current?.addEventListener(
            "fullscreenchange",
            onFullScreenChange
        );

        shakaPlayer.current.addEventListener("loaded", () => {
            onReady();
            if (!isVoD) {
                // watch for live updates
                beginTimeSliderInterval();
            }
        });

        shakaPlayer.current.addEventListener("buffering", (event: any) => {
            // dispatch(addNotification({ type: NotificationType.Info, message: `Buffering event: ${event?.buffering}`, class: "shopping"}))

            trackEvent("Buffering", event, true);
            if (event.buffering) {
                dispatch(setBuffering(true));
            } else {
                dispatch(setBuffering(false));
            }
        });

        const onAdaptationChange = (event: AdaptationEvent) => {
            dispatch(setShakaVideoQuality(event as AdaptationEvent));
        };

        // This is for bitrate change due to ABR
        shakaPlayer.current.addEventListener("adaptation", (e: any) => {
            trackEvent(
                "Adaptation Change",
                {
                    event: e,
                    isUserChange: false
                },
                true
            );
            onAdaptationChange(e as AdaptationEvent);
        });

        //This is for bitrate change made by user
        shakaPlayer.current.addEventListener("variantchanged", (e: any) => {
            trackEvent(
                "Adaptation Change",
                {
                    event: e,
                    isUserChange: true
                },
                true
            );
            onAdaptationChange(e as AdaptationEvent);
        });

        shakaPlayer.current.addEventListener("error", (e: any) => {
            const shakaError = e?.detail as shaka.util.Error;
            const code = shakaError.code;

            trackEvent("Shaka Error", shakaError, true);

            switch (code) {
                case 1001:
                    dispatch(setLevelLoadError(true));
                    break;
                default:
                    break;
            }
        });
    }, [
        shakaPlayer,
        video,
        videoContainer,
        onFullScreenChange,
        dispatch,
        onReady,
        beginTimeSliderInterval,
        isVoD,
        trackEvent
    ]);

    const initApp = useCallback(() => {
        // Install built-in polyfills to patch browser incompatibilities.
        shaka?.polyfill?.installAll();

        // Check to see if the browser supports the basic APIs Shaka needs.
        if (shaka?.Player?.isBrowserSupported()) {
            // Everything looks good!
            // dispatch(addNotification({ type: NotificationType.Success, message: "Let's roll!", class: "shopping" }))
            initPlayer();
            addEventListeners();
        } else {
            // This browser does not have the minimum set of APIs we need.
            console.error("Browser not supported!");
            // dispatch(addNotification({ type: NotificationType.Danger, message: "Browser not supported", class: "shopping" }))
        }
    }, [initPlayer, addEventListeners]);

    // init shaka player once video container ref and video ref are available
    useEffect(() => {
        if (video.current && videoContainer.current && !shakaPlayer.current) {
            initApp();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // Enable VideoJS adapter
    useEffect(() => {
        if (videoJsAdapter == null) return;
        videoJsAdapter.ready();
    }, [videoJsAdapter]);

    const [shakaCastProxy, setShakaCastProxy] =
        useState<shaka.cast.CastProxy>();
    const [castingProvider, setCastingProvider] = useState<CastingProvider>(
        CastingProvider.None
    );

    useEffect(() => {
        if (!video.current || !shakaPlayer.current) return;
        if (shakaCastProxy) return;

        const localProxy = new shaka.cast.CastProxy(
            video.current,
            shakaPlayer.current,
            import.meta.env.VITE_SHAKA_PLAYER_RECEIVER_ID,
            false
        );

        localProxy.addEventListener("caststatuschanged", (e: any) => {
            const readiedProxy = e.currentTarget as typeof localProxy;
            if (readiedProxy.canCast()) {
                setCastingProvider(CastingProvider.ChromeCast);
            } else if (window.WebKitPlaybackTargetAvailabilityEvent) {
                setCastingProvider(CastingProvider.AirPlay);
            } else {
                setCastingProvider(CastingProvider.None);
            }
        });

        setShakaCastProxy(localProxy);
    }, [video, shakaPlayer, shakaCastProxy]);

    const handleChromeCast = useCallback(async () => {
        if (shakaCastProxy?.isCasting()) {
            shakaCastProxy?.suggestDisconnect();
        } else {
            // Set the volume to 0.25 when casting starts
            dispatch(setVolume(0.25));

            await shakaCastProxy?.cast();
            eventBus.emit("playVideo");
        }
    }, [shakaCastProxy, dispatch]);

    const handleAirPlay = useCallback(() => {
        const video = shakaCastProxy?.getVideo();
        if (video) {
            (video as HTMLVideoElement).webkitShowPlaybackTargetPicker();
        }
    }, [shakaCastProxy]);

    const handleCast = useCallback(async () => {
        setCastSessionWasActive(true);

        switch (castingProvider) {
            case CastingProvider.ChromeCast:
                await handleChromeCast();
                break;
            case CastingProvider.AirPlay:
                console.warn("AirPlay not yet supported", handleAirPlay);
                // handleAirPlay();
                break;
            case CastingProvider.None:
            default:
                break;
        }

        // Track event for starting a cast session
        trackEvent("Casting Started", { castingProvider }, false, true);

        // Close the context menu
        dispatch(setActiveContextMenu(ContextMenus.None));
    }, [
        castingProvider,
        handleAirPlay,
        handleChromeCast,
        dispatch,
        trackEvent
    ]);

    const stopCasting = useCallback(() => {
        if (shakaCastProxy?.isCasting()) {
            shakaCastProxy?.forceDisconnect();
            setShakaCastProxy(undefined);
            eventBus.emit("pauseVideo");
        }
    }, [shakaCastProxy]);

    useBeforeUnload(stopCasting);

    eventBus.on("stopCasting", () => {
        stopCasting();
    });

    useEffect(() => {
        return () => {
            stopCasting();
        };
    }, [stopCasting]);

    const toggleCaptions = useCallback(async () => {
        if (!shakaPlayer.current || !captionsAvailable) return;

        const textTracks = shakaPlayer.current.getTextTracks();
        if (!textTracks || textTracks.length === 0) {
            setCaptionsAvailable(false);
            return;
        }

        shakaPlayer.current.selectTextTrack(textTracks[0]);
        await shakaPlayer.current?.setTextTrackVisibility(!captionsVisible);
        setCaptionsVisible(!captionsVisible);

        trackEvent(
            "Captions Toggled",
            { captionsVisible: !captionsVisible },
            false,
            true
        );
    }, [shakaPlayer, captionsAvailable, captionsVisible, trackEvent]);

    return {
        stopCasting,
        video,
        videoContainer,
        shakaPlayer,
        shakaCastProxy,
        proxyVideo: shakaCastProxy?.getVideo(),
        proxyPlayer: shakaCastProxy?.getPlayer(),
        castingProvider,
        handleCast,
        isCasting: !!shakaCastProxy?.isCasting(),
        hasJustCasted: castSessionWasActive && !shakaCastProxy?.isCasting(),
        setHasJustCasted: setCastSessionWasActive,
        endTimeSliderInterval,
        beginTimeSliderInterval,
        captionsAvailable,
        toggleCaptions,
        captionsVisible,
        castingAvailable:
            castingProvider !== CastingProvider.None &&
            castingProvider !== CastingProvider.AirPlay // AirPlay not yet supported
    };
};
