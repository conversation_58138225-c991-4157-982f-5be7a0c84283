import { useInterval } from "hooks/useInterval";
import { useCallback, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "store/reducers";
import { isMobile } from "react-device-detect";

const FADE_OUT_INTERVAL = 4000;
const REFRESH_INTEVERAL = 100;

export interface UseHideShowControlsOptions {
    /** If true, fade controls will not be set to true */
    preventFade?: boolean;
}

export const useHideShowControls = (
    { preventFade }: UseHideShowControlsOptions = { preventFade: false }
) => {
    const [fadeoutCountdown, setFadeoutCountdown] =
        useState<number>(FADE_OUT_INTERVAL);
    const [refreshInterval, setRefreshInterval] = useState<number | null>(null);
    const [fadeControls, setFadeControls] = useState<boolean>(false);
    const [showControls, setShowControls] = useState<boolean>(true);
    const { isPlaying, userSeeking } = useSelector(
        (s: RootState) => s.videoSession
    );
    const [hideControlsScheduled, setHideControlsScheduled] =
        useState<boolean>(false);

    useInterval(() => {
        setFadeoutCountdown((current) => current - REFRESH_INTEVERAL);
    }, refreshInterval);

    // hide controls when fadeout countdown hits zero
    useEffect(() => {
        if (fadeoutCountdown <= 0) {
            // desktop fade out/pop in controlled by CSS not JS
            if (!showControls || !isMobile || preventFade) return;

            setFadeControls(true);
            setTimeout(() => {
                setHideControlsScheduled(true);
            }, 200);
        }
    }, [fadeoutCountdown, preventFade, showControls]);

    // signal unmount of controls when timeout schedules it
    useEffect(() => {
        if (hideControlsScheduled && fadeControls) {
            setShowControls(false);
            setFadeControls(false);
            setHideControlsScheduled(false);
        }
    }, [fadeControls, hideControlsScheduled]);

    const resetCountdown = useCallback(() => {
        setFadeoutCountdown(FADE_OUT_INTERVAL);
    }, []);

    const resumeCountdown = useCallback(() => {
        resetCountdown();
        setRefreshInterval(REFRESH_INTEVERAL);
    }, [resetCountdown]);

    const stopCountdown = useCallback(() => {
        setRefreshInterval(null);
        resetCountdown();
    }, [resetCountdown]);

    // prevent fade out when video is paused or seeking
    useEffect(() => {
        if ((!isPlaying || userSeeking) && refreshInterval) {
            stopCountdown();
        }
        if (isPlaying && !userSeeking && !refreshInterval) {
            resumeCountdown();
        }
    }, [
        isPlaying,
        refreshInterval,
        resumeCountdown,
        stopCountdown,
        userSeeking
    ]);

    const onTouch = useCallback(() => {
        setFadeControls(false);
        setShowControls(true);
        setHideControlsScheduled(false);
        resumeCountdown();
    }, [resumeCountdown]);

    return {
        showControls,
        fadeControls,
        onTouch
    };
};
