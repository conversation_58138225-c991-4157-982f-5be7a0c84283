import { VideoUrl } from "components/EmbeddedVideoPlayer";
import shaka from "shaka-player/dist/shaka-player.compiled";

export const getFrameRate = (
    totalFrames: number,
    totalSeconds: number
): string | undefined => {
    if (Number.isNaN(totalFrames) || Number.isNaN(totalSeconds))
        return undefined;
    const frameRate = Math.round(totalFrames / (totalSeconds || 0.1));
    return `${frameRate}.000`;
};

export const getQualityString = (
    width: number | undefined,
    height: number | undefined
) => {
    if (!width || !height) return undefined;

    return `${width}x${height}`;
};

export const getPlaybackStats = (player: shaka.Player | undefined | null) => {
    if (!player) return {};
    const stats = player.getStats();
    const bitrate = stats.streamBandwidth;
    const framerate = getFrameRate(stats?.decodedFrames, stats?.playTime);
    const quality = getQualityString(stats?.width, stats?.height);

    return {
        bitrate,
        framerate,
        quality
    };
};

export const setCustomProperty = (
    name: string,
    value: string,
    element?: HTMLElement
) => {
    if (element) {
        element.style.setProperty(name, value);
    } else {
        document.documentElement.style.setProperty(name, value);
    }
};

export const prepareSeekTime = (rawTime: number | undefined | null) => {
    const time = !rawTime || Number.isNaN(rawTime) ? 0 : rawTime;
    const hours = Math.floor(time / 60 / 60);
    const minutes = Math.floor((time - hours * 60 * 60) / 60);
    const seconds = Math.round(time - hours * 60 * 60 - minutes * 60);
    const prepareTimeDigits = (digits: number) =>
        digits < 10 ? `0${digits}` : `${digits}`;
    return hours > 0
        ? `${hours}:${prepareTimeDigits(minutes)}:${prepareTimeDigits(seconds)}`
        : `${minutes}:${prepareTimeDigits(seconds)}`;
};

export const resetTimeSlider = () => {
    setCustomProperty("--sp-fill-percent", "0%");
    setCustomProperty("--sp-fill-rate", "0");
    setCustomProperty("--sp-fill-value", "0");
    setCustomProperty("--sp-pointer-percent", "0%");
    setCustomProperty("--sp-pointer-rate", "0");
    setCustomProperty("--sp-pointer-value", "0");
};

export const getBufferedEndPosition = (
    shakaPlayer: shaka.Player | undefined | null,
    isVod: boolean
): number => {
    const bufferedInfo = shakaPlayer?.getBufferedInfo();
    if (!bufferedInfo) return 0;
    if (isVod) return bufferedInfo.total?.[0]?.end ?? 0;
    return (
        (bufferedInfo.total?.[0]?.end ?? 0) -
        (bufferedInfo.total?.[0]?.start ?? 0)
    );
};

export const isMouseEvent = (
    e: React.TouchEvent | React.MouseEvent
): e is React.MouseEvent => {
    return e && "screenX" in e;
};

export const getSliderPercentFromMousePosition = (
    sliderElement: HTMLDivElement | undefined | null,
    event: React.MouseEvent | React.TouchEvent
) => {
    const clientX = isMouseEvent(event)
        ? event.clientX
        : event.targetTouches?.[0]?.clientX;
    const inputRect = sliderElement?.getBoundingClientRect();
    const mouseX = clientX - inputRect!.left;
    const pointerPercent = (mouseX / inputRect!.width) * 100;
    const pointerRate = pointerPercent / 100;

    return {
        pointerPercent,
        pointerRate
    };
};

export const hasDashSupport = async () => {
    const support = await shaka.Player.probeSupport(false);
    const supportsDash = support.manifest?.["video/vnd.mpeg.dash.mpd"];
    return supportsDash;
};

export const loadShakaPlayer = async (
    useDash: boolean,
    shakaPlayer: shaka.Player | undefined,
    src: VideoUrl | undefined | null,
    startTime: number = 0
): Promise<any> => {
    return Promise.resolve(
        shakaPlayer?.load(
            useDash ? (src?.dash ?? "") : (src?.hls ?? ""),
            startTime
        )
    );
};

export const getSliderPercentFromTime = (
    currentTime: number | null | undefined,
    duration: number | null | undefined
): number => {
    if (!currentTime || !duration) return 0;

    return Number((currentTime / duration) * 100);
};
