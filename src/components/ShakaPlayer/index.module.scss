.shaka-media {
    display: block;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    border-radius: rem(8);
    overflow: hidden;
    background-color: black;
    --neutral-color: rgba(255, 255, 255, 0.2);
    --buffered-fill-color: rgba(238, 238, 238, 0.4);
    --thumb-width: #{rem(16)};
    --gradient-steps: rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.008) 8%,
        rgba(0, 0, 0, 0.027) 15.3%, rgba(0, 0, 0, 0.063) 21.9%,
        rgba(0, 0, 0, 0.106) 28.1%, rgba(0, 0, 0, 0.157) 33.9%,
        rgba(0, 0, 0, 0.21) 39.5%, rgba(0, 0, 0, 0.27) 45%,
        rgba(0, 0, 0, 0.33) 50.5%, rgba(0, 0, 0, 0.39) 56.2%,
        rgba(0, 0, 0, 0.443) 62.1%, rgba(0, 0, 0, 0.494) 68.4%,
        rgba(0, 0, 0, 0.537) 75.2%, rgba(0, 0, 0, 0.573) 82.7%,
        rgba(0, 0, 0, 0.592) 90.9%, rgba(0, 0, 0, 0.6) 100%;
    border-radius: rem(16) rem(16) rem(0) rem(0);
    transition: 0.25s border-radius ease-in-out;

    @include mobile {
        border-radius: 0;
    }

    .media-controls-container {
        position: absolute;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        bottom: 0;
        left: 0;
        width: 100%;
        height: max-content;
        /* prevent blocking lower elements (e.g., gestures). */
        pointer-events: none;
        /** place above poster (optional). */
        z-index: 1;
        //opacity: 0;
        transition: opacity 0.25s ease 0s;
        transform-origin: left bottom;

        @include prep-fade-out(0, 0.5s);

        &.fadeControls {
            @include fade-out();
        }

        &:before {
            content: " ";
            position: absolute;
            bottom: rem(0);
            width: 100%;
            height: 12em;
            background-image: linear-gradient(var(--gradient-steps));
            z-index: -1;
            pointer-events: none;
        }
    }

    video {
        position: relative;
        height: 100%;
        width: 100%;
    }

    :global(.shaka-text-container) {
        bottom: rem(80);
        transition: bottom 0.1s ease;

        span {
            padding: rem(2) rem(8);
        }

        @media screen and (max-width: rem(550)) {
            font-size: rem(14);
        }
    }
}

.media-progress-bar-group,
.media-controls-group {
    display: flex;
    align-items: center;
    width: 100%;
    opacity: 1;
    transition: opacity 200ms ease;
    /** prevent clicks triggering lower elements. */
    pointer-events: auto;
    position: relative;
}

.media-controls-group {
    display: flex;
    justify-content: space-between;
    padding: rem(0) rem(25) rem(12) rem(25);

    .left-controls-container,
    .right-controls-container {
        display: flex;
        flex-flow: row nowrap;
        align-items: center;
        gap: rem(14);

        @include mobile() {
            gap: rem(10);
        }
    }

    .left-controls-container {
        justify-content: flex-start;
        flex-grow: 1;
    }

    .right-controls-container {
        justify-content: flex-end;
        flex-basis: 20%;
        gap: rem(25);

        .captions-button {
            display: grid;
            padding: 0;
        }
    }
}

.media-progress-bar-group {
    height: rem(32);
    padding: 0;
}

@media screen and (max-width: rem(550)) {
    .media-controls-group {
        margin-top: rem(4);

        .left-controls-container {
            flex-basis: unset;
        }
    }

    .mute-button {
        margin-right: 0;
    }

    .volume-slider {
        transform: scale(0.9);
    }
}

/* Styles for specific player states */
:global(.tool-active) .shaka-media {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

:global(.is-embed:not(.is-expanded)) .shaka-media {
    border-radius: 0;
}

// handle hiding controls when playing
:global(.is-playing:not(.is-mobile)) {
    .shaka-media {
        .media-controls-container:not(.preventFade) {
            @include fade-out();
        }

        :global(.shaka-text-container) {
            bottom: rem(20);
        }

        &:hover {
            .media-controls-container {
                @include prep-pop-in();
                @include pop-in();
            }

            :global(.shaka-text-container) {
                bottom: rem(80);
            }
        }
    }
}

.live-text,
.ended-text {
    border-radius: rem(8);
    font-size: rem(14);
    padding: rem(4) rem(12);

    display: flex;
    flex-direction: row;
    gap: rem(1);

    background-color: #da1f0e;
    color: #ffffff;
    text-transform: uppercase;
    font-weight: 700;
    border: none;
}

.ended-text {
    background-color: #363636;
}
