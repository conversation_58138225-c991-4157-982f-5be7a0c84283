import PlayIcon from "assets/icons/play.svg?react";
import PauseIcon from "assets/icons/pause.svg?react";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import { useSelector } from "react-redux";
import { RootState } from "store/reducers";
const cx = classNames.bind(styles);

export const PlayPauseButton = ({
    handleClick
}: {
    handleClick: () => void;
}) => {
    const { isPlaying } = useSelector((s: RootState) => s.videoSession);

    return (
        <button
            className={cx("switcher-play-pause-button")}
            type="button"
            onClick={handleClick}
        >
            {isPlaying ? (
                <PauseIcon className={cx("media-pause-icon")} />
            ) : (
                <PlayIcon className={cx("media-play-icon")} />
            )}
        </button>
    );
};
