import React from "react";
import PlayIcon from "assets/icons/play.svg?react";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
const cx = classNames.bind(styles);

export const PlayEffect = () => {
    return (
        <div className={cx("play-pause-container")}>
            <div className={cx("play-circle")}>
                <PlayIcon />
            </div>
        </div>
    );
};
