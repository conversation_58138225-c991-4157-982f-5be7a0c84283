@keyframes appearDisappear {
    0% {
        transform: scale(1);
        opacity: 1;
    }

    100% {
        transform: scale(0.8);
        opacity: 0;
    }
}

.play-pause-container {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    /* prevent blocking lower elements (e.g., gestures). */
    pointer-events: none;
    /** place above poster (optional). */
    z-index: 1;
}

.play-circle,
.pause-circle {
    height: 7em;
    width: 7em;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 100%;
    z-index: 1;
    display: flex;
    flex-flow: row nowrap;
    justify-content: center;
    align-items: center;
    animation: 0.6s ease 0.2s 1 normal forwards running appearDisappear;

    & > svg {
        color: var(--player-controls-color);
        height: 50%;
        width: 50%;
    }
}

.play-circle {
    svg {
        /* Allow play button to be centered */
        margin-left: 12%;
    }
}
