import React from "react";
import EnterFullscreenIcon from "assets/icons/enter-fullscreen.svg?react";
import ExitFullscreenIcon from "assets/icons/exit-fullscreen.svg?react";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
const cx = classNames.bind(styles);

export const FullscreenButton = ({
    isFullScreen,
    onClick
}: {
    isFullScreen: boolean;
    onClick: () => void;
}) => {
    return (
        <button className={cx("fullscreen-button")} onClick={onClick}>
            {isFullScreen ? (
                <ExitFullscreenIcon className={cx("media-exit-fs-icon")} />
            ) : (
                <EnterFullscreenIcon className={cx("media-enter-fs-icon")} />
            )}
        </button>
    );
};
