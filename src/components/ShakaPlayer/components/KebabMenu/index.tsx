import KebabMenuIcon from "assets/icons/more.svg?react";
import { useDispatch } from "react-redux";
import { AppDispatch } from "store/store";
import { useSelector } from "react-redux";
import { RootState } from "store/reducers";
import { ContextMenus } from "store/VideoSession/types";
import { setActiveContextMenu } from "store/VideoSession/slice";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import { ComponentMap } from "components/utility/ComponentMap";
import { useCallback, useMemo, useRef, useState } from "react";
import { useClickOutside } from "hooks/useClickOutside";
import { KebabMenuOption } from "./KebabMenuOption";
import { KebabMenuSelect, KebabMenuSelectProps } from "./KebabMenuSelect";
import BackIcon from "assets/icons/chevron-left.svg?react";

const cx = classNames.bind(styles);

export enum KebabMenuItemIds {
    PlaybackSpeed = "playbackSpeed",
    PictureInPicture = "pip",
    CastToDevice = "cast",
    FullScreen = "fullscreen",
    Report = "report",
    VideoQuality = "videoQuality",
    Share = "share"
}

export interface KebabMenuItem<T> {
    id: T;
    label: string;
    hidden?: boolean;
}

export interface KebabMenuOptionItem<T> extends KebabMenuItem<T> {
    onClick: (
        id: T,
        event: React.MouseEvent<HTMLButtonElement, MouseEvent>
    ) => void;
}

export interface KebabMenuSelectItemOption<K> {
    id: K;
    label: string;
    selected?: boolean;
    hidden?: boolean;
}

export interface KebabMenuSelectItem<T = string[]>
    extends KebabMenuItem<KebabMenuItemIds> {
    options: KebabMenuSelectItemOption<keyof T>[];
    hideSelected?: boolean;
    selected?: keyof T;
    onOptionSelect: (id: keyof T) => void;
    shouldCloseOnSelect?: boolean;
}

export interface KebabMenuProps<T = string[]> {
    items: Array<
        KebabMenuOptionItem<KebabMenuItemIds | T> | KebabMenuSelectItem<T>
    >;
    onClose: () => void;
    preventMobileStyles?: boolean;
}

/**
 * KebabMenu component displays a dropdown menu with various options
 * for user interaction. It may contain standard options and submenus
 * that can be selected by the user.
 *
 * Props:
 * - `items`: An array of items to be displayed in the dropdown menu.
 *   Each item can be either a regular option or a select item
 *   with its own set of options.
 * - `onSelect`: Optional callback function that is triggered when
 *   an item is selected. It receives the ID of the selected item
 *   and the mouse event as arguments.
 *
 * Functionality:
 * - Opens the menu when the user clicks the "More Options" button,
 *   triggering the `open` method which sets the active context menu
 *   to "MoreOptions".
 * - Allows the user to close the menu and submenus by clicking outside
 *   or selecting a submenu item, triggering the `close` method.
 * - Renders the main menu items and conditionally renders the submenu
 *   items if a submenu is selected.
 * - Uses React hooks for managing state (`useState`), side effects
 *   (`useEffect`), and event handling (`useCallback`).
 *
 * The menu also includes a backdrop that covers other elements
 * when opened, providing a clearer user interaction.
 */
export const KebabMenu = ({
    items,
    onClose,
    preventMobileStyles
}: KebabMenuProps) => {
    const dispatch = useDispatch<AppDispatch>();
    const { activeContextMenu } = useSelector((s: RootState) => s.videoSession);
    const menuRef = useRef<HTMLDivElement>(null);
    const baseMenuRef = useRef<HTMLDivElement>(null);
    const subMenuRef = useRef<HTMLDivElement>(null);
    const buttonRef = useRef<HTMLButtonElement>(null);
    const submenuBackButtonRef = useRef<HTMLButtonElement>(null);
    const menuFirstButtonRef = useRef<HTMLButtonElement>(null);

    const [selectedSubmenuId, setSelectedSubmenuId] = useState<
        KebabMenuItemIds | undefined
    >();

    const selectedSubmenu = useMemo<KebabMenuSelectItem | undefined>(
        () =>
            items.find(
                (i) => i.id === selectedSubmenuId
            ) as KebabMenuSelectItem,
        [items, selectedSubmenuId]
    );

    const transition = useCallback((to: "menu" | "submenu" | "open") => {
        let start: number;
        const duration: number = 100;
        const initialHeight =
            to === "open" ? 0 : menuRef.current?.clientHeight ?? 0;

        const placement = menuRef.current?.getBoundingClientRect();

        if (menuRef.current) {
            const topMargin = 24;
            menuRef.current.style.maxHeight =
                (placement?.bottom ?? 0) - topMargin + "px";
        }

        const step = (timestamp: number) => {
            if (start === undefined) {
                start = timestamp;
            }

            if (menuRef.current && baseMenuRef.current && subMenuRef.current) {
                const elapsed = timestamp - start;
                const amount = Math.min(elapsed / duration, 1);

                let target: number;
                switch (to) {
                    case "open":
                        target = baseMenuRef.current.clientHeight;
                        break;
                    case "menu":
                        target =
                            baseMenuRef.current.clientHeight - initialHeight;
                        break;
                    case "submenu":
                        target =
                            subMenuRef.current.clientHeight - initialHeight;
                        break;
                }

                menuRef.current.style.height =
                    initialHeight + target * amount + "px";

                if (amount < 1) {
                    requestAnimationFrame((timestamp) => step(timestamp));
                }
            }
        };

        requestAnimationFrame((timestamp) => step(timestamp));
    }, []);

    const open = useCallback(() => {
        dispatch(setActiveContextMenu(ContextMenus.MoreOptions));
        transition("open");
    }, [dispatch, transition]);

    const next = useCallback(
        (id: KebabMenuItemIds) => {
            setSelectedSubmenuId(id);
            transition("submenu");
        },
        [transition]
    );

    const back = useCallback(() => {
        setSelectedSubmenuId(undefined);
        transition("menu");
    }, [transition]);

    const close = useCallback(() => {
        onClose?.();
        // delays resetting the menu to avoid conflict with the transition - make sure this is the same as the transition time set in css
        setTimeout(back, 200);
    }, [onClose, back]);

    useClickOutside([menuRef, buttonRef], close, { ignoreInitialClick: false });

    return (
        <div className={cx("more-button-wrapper")}>
            <button
                className={cx("more-button")}
                title="More Options"
                type="button"
                onClick={() =>
                    activeContextMenu === ContextMenus.None ? open() : close()
                }
                ref={buttonRef}
            >
                <KebabMenuIcon />
            </button>

            <div
                id="more-menu"
                aria-haspopup="true"
                className={cx("more-menu-container", {
                    "active": activeContextMenu === ContextMenus.MoreOptions,
                    "prevent-mobile-styles": preventMobileStyles
                })}
                ref={menuRef}
            >
                <div
                    className={cx("more-menu", {
                        "submenu-open": selectedSubmenuId !== undefined
                    })}
                >
                    <div className={cx("base-menu")} ref={baseMenuRef}>
                        <ComponentMap
                            items={items}
                            element={(item, { isFirst }) => {
                                if (item.hidden) return <></>;

                                const firstButton =
                                    isFirst && !item.hidden
                                        ? menuFirstButtonRef
                                        : undefined;

                                if (
                                    (item as KebabMenuSelectItem<string[]>)
                                        ?.options
                                ) {
                                    return (
                                        <KebabMenuSelect
                                            {...(item as KebabMenuSelectProps)}
                                            onClick={next}
                                            ref={firstButton}
                                        />
                                    );
                                } else {
                                    return (
                                        <KebabMenuOption
                                            ref={firstButton}
                                            {...(item as KebabMenuOptionItem<KebabMenuItemIds>)}
                                        />
                                    );
                                }
                            }}
                        />
                    </div>

                    <div className={cx("more-submenu")} ref={subMenuRef}>
                        <button
                            type="button"
                            className={cx("submenu-header", "more-menu-item")}
                            onClick={() => {
                                back();
                            }}
                            tabIndex={!selectedSubmenu ? -1 : 0}
                            ref={submenuBackButtonRef}
                        >
                            <BackIcon />
                            <strong>{selectedSubmenu?.label}</strong>
                        </button>
                        <ComponentMap
                            className={cx("submenu-options")}
                            items={selectedSubmenu?.options ?? []}
                            element={(item) => (
                                <KebabMenuOption
                                    {...item}
                                    onClick={(id) => {
                                        if (
                                            selectedSubmenu?.shouldCloseOnSelect
                                        ) {
                                            close();
                                        } else {
                                            back();
                                        }

                                        selectedSubmenu?.onOptionSelect(id);
                                    }}
                                    variant="submenu"
                                />
                            )}
                        />
                    </div>
                </div>
            </div>
            <div
                className={cx("more-menu-backdrop", {
                    "active": activeContextMenu === ContextMenus.MoreOptions,
                    "prevent-mobile-styles": preventMobileStyles
                })}
            />
        </div>
    );
};
