import React, { forwardRef } from "react";
import { KebabMenuItemIds } from ".";
import MoreIcon from "assets/icons/chevron-right.svg?react";
import styles from "./index.module.scss";
import classNames from "classnames/bind";

const cx = classNames.bind(styles);

export interface KebabMenuSelectProps {
    id: KebabMenuItemIds;
    label: string;
    selected: string;
    onClick: (id: KebabMenuItemIds) => void;
    hideSelected: boolean;
}

export const KebabMenuSelect = forwardRef<
    HTMLButtonElement,
    KebabMenuSelectProps
>(({ id, label, selected, onClick, hideSelected }, ref) => {
    return (
        <button
            ref={ref}
            className={cx(
                "more-menu-item",
                "more-menu-select-item",
                hideSelected && "hide-select-item"
            )}
            type="button"
            title={label}
            onClick={() => {
                onClick(id);
            }}
        >
            <span>{label}</span>
            {!hideSelected && <small>{selected}</small>} <MoreIcon />
        </button>
    );
});

KebabMenuSelect.displayName = "KebabMenuSelect";
