$transition-speed: 0.2s;

@mixin scrollable-menu {
    max-height: 60vh;
    overflow-y: auto;

    scrollbar-width: thin;
    scrollbar-color: slategrey var(--player-controls-color);

    &::-webkit-scrollbar {
        width: 0.25rem;
    }

    &::-webkit-scrollbar-thumb {
        background: slategrey;
        border: 0.125rem solid slategrey;
        border-radius: 10rem;
    }

    &::-webkit-scrollbar-corner {
        background: var(--player-controls-color);
    }
}

.more-button-wrapper {
    position: relative;
    display: flex;
    align-items: center;

    .more-button {
        position: relative;
        width: rem(4);
        height: rem(16);
        cursor: pointer;
        background: transparent;
        border: none;
        color: var(--player-controls-color);

        @include glow-shadow(var(--player-controls-color));

        & > svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
    }

    .more-menu-container {
        position: absolute;
        bottom: rem(20);
        right: 0;
        pointer-events: none;
        overflow: hidden;
        border-radius: rem(8);
        opacity: 0;
        width: rem(240);
        background: rgba(28, 28, 28, 0.9);
        box-shadow: rem(0) rem(12) rem(16) rem(-4) rgba(16, 24, 40, 0.08),
            rem(0) rem(4) rem(6) rem(-2) rgba(16, 24, 40, 0.03);
        transition: $transition-speed opacity ease-in-out,
            $transition-speed transform ease-in-out;
        z-index: 1010;

        &.active {
            transform: translateY(rem(-20));
            opacity: 1;
            pointer-events: auto;
        }

        @include mobile-portrait {
            &.prevent-mobile-styles {
                overflow-y: auto;
            }

            &:not(.prevent-mobile-styles) {
                $margin: rem(10);
                position: fixed;
                left: $margin;
                right: $margin;
                bottom: $margin;
                width: calc(100% - calc(2 * $margin));

                .more-menu {
                    width: 200%;
                }
            }
        }

        .more-menu {
            width: 200%;

            display: grid;
            place-items: start;

            grid-template-rows: 1fr;
            grid-template-columns: 1fr 1fr;

            position: absolute;
            top: 0;
            left: 0;

            &.submenu-open {
                left: -100%;
            }

            .base-menu {
                width: 100%;
                transition: opacity $transition-speed ease-out;
                @include scrollable-menu();
            }

            .more-submenu {
                width: 100%;
                opacity: 0;
                transition: opacity $transition-speed ease-out;

                pointer-events: none;

                .submenu-header {
                    padding: rem(12) rem(15);

                    .svg {
                        width: rem(12);
                        width: rem(12);
                    }
                }

                .submenu-options {
                    @include scrollable-menu();
                }
            }

            &.submenu-open {
                .base-menu {
                    opacity: 0;
                }

                .more-submenu {
                    opacity: 1;
                    pointer-events: auto;
                    overflow: hidden;
                }
            }

            .more-menu-item {
                display: flex;
                align-items: center;
                gap: rem(15);
                width: 100%;
                cursor: pointer;

                padding: rem(13);
                font-weight: 400;
                font-size: rem(14);
                line-height: rem(20);
                text-wrap: nowrap;

                border: none;
                border-top: rem(1) solid rgba(121, 121, 121, 0.16);
                transition: $transition-speed background-color ease-in-out;
                background-color: transparent;
                color: #fff;

                &:first-child {
                    border: none;
                }

                &:hover {
                    background-color: rgba(48, 48, 48, 0.6);
                }

                &.submenu-item {
                    border-top: none;
                }
            }

            .more-menu-select-item {
                display: grid;
                grid-template-columns: 1fr;
                grid-template-rows: 1fr;
                row-gap: 0;

                span {
                    justify-self: start;
                    font-size: rem(13);
                    font-weight: 700;
                }

                small {
                    font-size: rem(13);
                    font-weight: 400;
                    justify-self: start;
                    grid-column: 1;
                    grid-row: 2;
                }

                svg {
                    justify-self: end;
                    grid-column: end;
                    grid-row: 1;
                    width: rem(12);
                    height: rem(12);
                }
            }
        }

        .hide-select-item {
            span {
                font-weight: 400;
                font-size: rem(14);
                line-height: rem(20);
            }
        }
    }

    .more-menu-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1000;
        opacity: 0;
        pointer-events: none;

        @include mobile-portrait {
            &:not(.prevent-mobile-styles) {
                background: rgba(0, 0, 0, 0.5);
            }
        }

        &.active {
            pointer-events: all;
            opacity: 1;
        }
    }
}
