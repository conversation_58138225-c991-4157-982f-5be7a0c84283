import React, { forwardRef } from "react";
import { KebabMenuOptionItem } from ".";
import CheckIcon from "assets/icons/checkmark-simple-black.svg?react";
import styles from "./index.module.scss";
import classNames from "classnames/bind";

const cx = classNames.bind(styles);

export interface KebabMenuOptionProps<T> extends KebabMenuOptionItem<T> {
    variant?: "main" | "submenu";
    selected?: boolean;
}

// Use forwardRef to create the component
export const KebabMenuOption = forwardRef<
    HTMLButtonElement,
    KebabMenuOptionProps<any>
>(({ id, label, onClick, variant = "main", selected = false }, ref) => (
    <button
        className={cx("more-menu-item", {
            "submenu-item": variant === "submenu"
        })}
        type="button"
        title={label}
        onClick={(event) => {
            onClick(id, event);
        }}
        ref={ref}
    >
        {label}
        {selected && <CheckIcon />}
    </button>
));

// Set a display name for debugging
KebabMenuOption.displayName = "KebabMenuOption";
