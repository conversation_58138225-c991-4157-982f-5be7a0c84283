.more-button-wrapper {
    position: relative;
    display: flex;
    align-items: center;

    .more-button {
        position: relative;
        width: rem(4);
        height: rem(16);
        cursor: pointer;
        background: transparent;
        border: none;
        color: var(--player-controls-color);

        @include glow-shadow(var(--player-controls-color));

        & > svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
    }

    .more-menu {
        position: absolute;
        right: 0;
        bottom: rem(40);
        background: #ffffff;
        box-shadow: rem(0) rem(12) rem(16) rem(-4) rgba(16, 24, 40, 0.08),
            rem(0) rem(4) rem(6) rem(-2) rgba(16, 24, 40, 0.03);
        border-radius: rem(8);
        opacity: 0;
        pointer-events: none;
        transition: 0.25s opacity ease-in-out;
        overflow: hidden;
        width: rem(240);
        z-index: 110;

        &.active {
            opacity: 1;
            pointer-events: all;
        }

        .more-menu-item {
            display: flex;
            align-items: center;
            gap: rem(15);
            width: 100%;
            cursor: pointer;

            padding: rem(13) rem(19);
            font-weight: 400;
            font-size: rem(14);
            line-height: rem(20);

            border: none;
            border-top: rem(1) solid rgba(121, 121, 121, 0.16);
            transition: 0.25s background-color ease-in-out;
            background-color: #ffffff;

            &:first-child {
                border: none;
            }

            &:hover {
                background-color: rgba(121, 121, 121, 0.08);
            }

            svg {
                width: rem(20);
                height: rem(20);
            }
        }
    }
}
