import MoreIcon from "assets/icons/more.svg?react";
import FlagIcon from "assets/icons/flag.svg?react";
import CopyrightIcon from "assets/icons/copyright.svg?react";
import { useDispatch } from "react-redux";
import { AppDispatch } from "store/store";
import { useSelector } from "react-redux";
import { RootState } from "store/reducers";
import { ContextMenus } from "store/VideoSession/types";
import { setActiveContextMenu } from "store/VideoSession/slice";
import { Modals } from "store/Modals/types";
import { setActiveModal } from "store/Modals/slice";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
const cx = classNames.bind(styles);

export const MoreButton = () => {
    const dispatch = useDispatch<AppDispatch>();
    const { activeContextMenu } = useSelector((s: RootState) => s.videoSession);

    return (
        <>
            <div className={cx("more-button-wrapper")}>
                <button
                    className={cx("more-button")}
                    onClick={() => {
                        dispatch(
                            setActiveContextMenu(
                                activeContextMenu === ContextMenus.None
                                    ? ContextMenus.MoreOptions
                                    : ContextMenus.None
                            )
                        );
                    }}
                >
                    <MoreIcon />
                </button>
                <div
                    className={cx("more-menu", {
                        "active": activeContextMenu === ContextMenus.MoreOptions
                    })}
                >
                    <button
                        className={cx("more-menu-item")}
                        onClick={() => {
                            dispatch(setActiveContextMenu(ContextMenus.None));
                            dispatch(setActiveModal(Modals.Report));
                        }}
                    >
                        <FlagIcon /> Report Video
                    </button>
                    <button
                        className={cx("more-menu-item")}
                        onClick={() => {
                            dispatch(setActiveContextMenu(ContextMenus.None));
                            dispatch(setActiveModal(Modals.DMCA));
                        }}
                    >
                        <CopyrightIcon /> DMCA Notice
                    </button>
                </div>
            </div>
        </>
    );
};
