import React from "react";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import { prepareSeekTime } from "components/ShakaPlayer/helpers";
const cx = classNames.bind(styles);

export const VideoPlayerTime = ({
    currentPosition = 0,
    duration = 0
}: {
    currentPosition: number;
    duration: number;
}) => (
    <div className={cx("video-time-container")}>
        <p>
            {prepareSeekTime(currentPosition)}
            {" / "}
            {prepareSeekTime(duration)}
        </p>
    </div>
);
