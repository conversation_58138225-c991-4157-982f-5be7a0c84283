.volume-control-container {
    height: 100%;
}

.volume-control-group {
    width: 100%;
    position: relative;
}

/**
   * -----------------------------------------------------------
   * Mute <PERSON>ton
   * -----------------------------------------------------------
   */

.mute-button {
    all: unset;
    position: relative;
    flex-basis: rem(18);
    min-width: rem(18);
    cursor: pointer;
    color: var(--player-controls-color);
    height: rem(18);
    width: rem(18);
    display: grid;

    @include glow-shadow(var(--player-controls-color));
}

.mute-button > svg {
    /** `absolute` so icons are placed on top of each other. */
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 1;
    transition: opacity ease 150ms;
}

/**
   * -----------------------------------------------------------
   * Volume Slider
   * -----------------------------------------------------------
   */

.volume-slider {
    position: absolute;
    top: rem(-144);
    left: rem(-8);
    padding: rem(12) rem(12) rem(8);
    background-color: rgba(28, 28, 28, 0.9);
    border-radius: rem(8);
    z-index: 120; // higher than #more-menu

    --track-height: #{rem(8)};
    -webkit-appearance: none;
    appearance: none;
    outline: none;
    writing-mode: vertical-lr;
    direction: rtl;
    vertical-align: bottom;
    pointer-events: none;

    width: max-content;
    max-height: rem(142);
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.25s ease 0s;

    // Webkit (Chrome, Safari, Opera)
    &::-webkit-slider-runnable-track {
        background-color: var(--buffered-fill-color, green);
        width: rem(8);
        height: var(--track-height);
        border-radius: rem(20);
    }
    &::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;

        width: var(--thumb-width);
        height: var(--thumb-width);
        border-radius: rem(9999);
        background-color: #fff;
        transform: translateX(-25%);
        cursor: pointer;
    }

    // Firefox
    @-moz-document url-prefix() {
        padding: rem(12) rem(8) rem(8);
        left: rem(-12);
    }

    &::-moz-range-track {
        background-color: var(--buffered-fill-color, green);
        height: 100%;
        width: var(--track-height);
        border-radius: rem(20);
    }

    &::-moz-range-thumb {
        appearance: none;
        width: var(--thumb-width);
        height: var(--thumb-width);
        border: none;
        border-radius: 50%;
        background-color: #fff;
        cursor: pointer;
    }
}

.mute-button:hover + .volume-slider,
.volume-slider:hover,
.volume-slider:focus {
    opacity: 1;
    pointer-events: auto;

    body:has(&) {
        :global {
            #more-menu {
                opacity: 0;
            }
        }
    }

    @include mobile {
        opacity: 0;
    }
}

.volume-slider {
    @include mobile {
        opacity: 0;
    }
}
