import MuteIcon from "assets/icons/mute.svg?react";
import UnmuteIcon from "assets/icons/unmute.svg?react";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import { useSelector } from "react-redux";
import { RootState } from "store/reducers";
import React, { useRef, useCallback, useEffect } from "react";
import { useDispatch } from "react-redux";
import { AppDispatch } from "store/store";
import { setVolume, setVolumeMuted } from "store/VideoSession/slice";
import { isMobile } from "react-device-detect";
const cx = classNames.bind(styles);

export const VolumeControl = ({ video }: { video: HTMLVideoElement }) => {
    const volumeSlider = useRef<HTMLInputElement>(null);
    const { isMuted, volume, volumeSliderVisible } = useSelector(
        (s: RootState) => s.videoSession
    );
    const dispatch = useDispatch<AppDispatch>();

    // Set initial volume from state when loading a video
    useEffect(() => {
        if (!!video) {
            video!.volume = volume ?? 1;
            if (volume === 0 || isMuted) {
                video!.muted = true;
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [video]);

    useEffect(() => {
        if (!volumeSlider.current) return;
        // allow volume slider to be made visible with redux state
        if (volumeSliderVisible) {
            volumeSlider.current.focus();
        } else {
            volumeSlider.current.blur();
        }
    }, [volumeSliderVisible]);

    const handlePointerChange = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            const eventPercentRate = Number(e.currentTarget.value);
            video!.volume = eventPercentRate;

            if (eventPercentRate === 0 && !isMuted) {
                dispatch(setVolumeMuted(true));
            }

            if (eventPercentRate > 0 && isMuted) {
                dispatch(setVolumeMuted(false));
                video!.muted = false;
            }
        },
        [dispatch, video, isMuted]
    );
    const handlePointerChangeEnd = useCallback(() => {
        if (video!.volume > 0) {
            const eventPercentRate = Number(video!.volume);
            dispatch(setVolume(eventPercentRate));
        }
    }, [video, dispatch]);

    const handleToggleMute = useCallback(() => {
        const _isMuted = !(isMuted || video!.volume === 0);
        dispatch(setVolumeMuted(_isMuted));
        video!.muted = _isMuted;

        const newVolume = Number(_isMuted ? 0 : volume === 0 ? 1 : volume);
        video!.volume = newVolume;
        if (!!volumeSlider.current)
            volumeSlider.current!.value = newVolume.toString();
    }, [dispatch, isMuted, video, volume]);

    return (
        <div className={cx("volume-control-container")}>
            <div className={cx("volume-control-group")}>
                <button
                    className={cx("mute-button")}
                    onClick={handleToggleMute}
                >
                    {isMuted ? (
                        <MuteIcon className={cx("media-mute-icon")} />
                    ) : (
                        <UnmuteIcon className={cx("media-unmute-icon")} />
                    )}
                </button>
                {!isMobile && (
                    <input
                        className={cx("volume-slider")}
                        type="range"
                        step={0.01}
                        min={0}
                        max={1}
                        defaultValue={isMuted ? 0 : volume ?? 1}
                        aria-label="Seek"
                        onChange={handlePointerChange}
                        onMouseUp={handlePointerChangeEnd}
                        onTouchEnd={handlePointerChangeEnd}
                        onKeyUp={handlePointerChangeEnd}
                        ref={volumeSlider}
                    />
                )}
            </div>
        </div>
    );
};
