import React, { useEffect, useMemo, useCallback } from "react";
import styles from "./index.module.scss";
import SettingsIcon from "assets/icons/settings.svg?react";
import classNames from "classnames/bind";
import { ContextMenus } from "store/VideoSession/types";
import { useSelector } from "react-redux";
import { RootState } from "store/reducers";
import { useDispatch } from "react-redux";
import {
    setActiveContextMenu,
    setUserSelectedQualityLevel,
    setShakaVideoQualityIsAuto
} from "store/VideoSession/slice";
import CheckmarkIcon from "assets/icons/checkmark-simple-black.svg?react";
const cx = classNames.bind(styles);

export const QualitySelector = ({
    shakaPlayer
}: {
    shakaPlayer: shaka.Player;
}) => {
    const {
        activeContextMenu,
        userSelectedQualityLevel,
        shakaVideoQuality: {
            levelInfo: { newTrack },
            isAuto
        }
    } = useSelector((s: RootState) => s.videoSession);
    const levels = useMemo(
        () =>
            shakaPlayer
                .getVariantTracks()
                ?.sort((a, b) => b.bandwidth - a.bandwidth),
        [shakaPlayer]
    );
    const levelNumbers = useMemo(() => levels?.map((l) => l.id), [levels]);
    const dispatch = useDispatch();

    const changeLevel = useCallback(
        (level: shaka.extern.Track | "auto", fromUser: boolean = false) => {
            if (fromUser) {
                dispatch(
                    setUserSelectedQualityLevel(
                        level === "auto" ? level : level?.id
                    )
                );
            }

            if (level === "auto") {
                shakaPlayer.configure({ abr: { enabled: true } });
                dispatch(setShakaVideoQualityIsAuto(true));
                return;
            }

            shakaPlayer.configure({ abr: { enabled: false } });
            shakaPlayer.selectVariantTrack(level, true, 5); // clears buffer and switches to new quality after 2 seconds
            dispatch(setShakaVideoQualityIsAuto(false));
            dispatch(setActiveContextMenu(ContextMenus.None));
        },
        [dispatch, shakaPlayer]
    );

    /** Handle quality level bump down when previous level no longer available on current video */
    useEffect(() => {
        const bumpDownLevel = () => {
            const nextHighestLevel = levels[0];
            changeLevel(nextHighestLevel);
        };

        const resetLevelToUserSelection = () => {
            const userSelectedLevelObject = levels.find(
                (l) => l.id === userSelectedQualityLevel
            );
            if (userSelectedLevelObject) {
                changeLevel(userSelectedLevelObject);
            }
        };

        if (userSelectedQualityLevel === "auto" || !levels?.length) return;

        if (!levelNumbers.includes(userSelectedQualityLevel)) {
            // user's selected quality level no longer available, need to bump down to next highest level
            bumpDownLevel();
        } else if (newTrack?.id !== userSelectedQualityLevel) {
            // user's selected quality level is available, but is different from current constrained level
            // need to reset constrained level to match previous selection
            resetLevelToUserSelection();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [levels, dispatch, userSelectedQualityLevel]);

    return (
        <>
            <div className={cx("quality-selector-button-wrapper")}>
                <button
                    className={cx("quality-selector-button")}
                    onClick={() =>
                        dispatch(
                            setActiveContextMenu(
                                activeContextMenu ===
                                    ContextMenus.QualityOptions
                                    ? ContextMenus.None
                                    : ContextMenus.QualityOptions
                            )
                        )
                    }
                >
                    <SettingsIcon />
                </button>
                <div
                    className={cx("quality-selector-menu", {
                        active:
                            activeContextMenu === ContextMenus.QualityOptions
                    })}
                >
                    {levels?.map((level) => (
                        <button
                            key={level?.height}
                            className={cx("quality-selector-menu-item")}
                            onClick={() => {
                                changeLevel(level, true);
                                dispatch(
                                    setActiveContextMenu(ContextMenus.None)
                                );
                            }}
                        >
                            {`${level?.height}p`}
                            {!isAuto && newTrack?.id === level.id && (
                                <CheckmarkIcon />
                            )}
                        </button>
                    ))}

                    <button
                        key={"auto"}
                        className={cx("quality-selector-menu-item")}
                        onClick={() => {
                            changeLevel("auto", true);
                            dispatch(setActiveContextMenu(ContextMenus.None));
                        }}
                    >
                        Auto
                        {isAuto && <CheckmarkIcon />}
                    </button>
                </div>
            </div>
        </>
    );
};
