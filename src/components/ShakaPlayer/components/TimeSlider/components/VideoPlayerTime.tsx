import React from "react";
import styles from "../index.module.scss";
import classNames from "classnames/bind";
import { prepareSeekTime } from "components/ShakaPlayer/helpers";
const cx = classNames.bind(styles);

export const VideoPlayerTime = ({
    video
}: {
    video: HTMLVideoElement | undefined | null;
}) => {
    const currentPosition = video?.currentTime ?? 0;
    const duration = video?.duration ?? 0;
    return (
        <div className={cx("video-time-container")}>
            {prepareSeekTime(currentPosition)}
            {" / "}
            {prepareSeekTime(duration)}
        </div>
    );
};
