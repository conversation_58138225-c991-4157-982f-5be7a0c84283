import React, { useMemo } from "react";
import styles from "../index.module.scss";
import classNames from "classnames/bind";
import { getSliderPercentFromTime, prepareSeekTime } from "../../../helpers";
import { isMobile } from "react-device-detect";
import { useSelector } from "react-redux";
import { RootState } from "store/reducers";
const cx = classNames.bind(styles);

const preFillColor = "rgb(98, 97, 97)";
const postFillColor = "rgb(225, 225, 225)";

export const MediaPreviewContainer = ({
    hoverTime,
    duration
}: {
    hoverTime: number;
    duration: number | undefined;
}) => {
    const { video, isVoD, currentCollectionVideo } = useSelector(
        (s: RootState) => s.videoSession
    );

    const isLiveEnded = useMemo(() => {
        return (
            currentCollectionVideo?.broadcast?.details?.broadcastStatus ===
                "Ended" &&
            currentCollectionVideo?.broadcast?.videos?.[0]?.details?.status ===
                "live-inprogress"
        );
    }, [currentCollectionVideo]);
    const isLive = useMemo(() => !isVoD || isLiveEnded, [isVoD, isLiveEnded]);

    const isValidValue = useMemo(
        () => hoverTime !== undefined && duration !== undefined,
        [hoverTime, duration]
    );

    const value = useMemo(() => {
        if (
            duration === undefined ||
            hoverTime === undefined ||
            (!isLive && hoverTime < 0)
        )
            return 0;
        if (isLive) return duration - hoverTime;
        if (hoverTime > duration) return duration;
        return hoverTime;
    }, [duration, hoverTime, isLive]);

    const pointerPercent = useMemo(() => {
        const percent = getSliderPercentFromTime(hoverTime, duration);

        // because live videos count backwards from current live position,
        // if pointer percent gets to zero, it needs to actually be 100 so it doesn't wrap around
        return isLive && percent === 0 ? 100 : percent;
    }, [duration, hoverTime, isLive]);
    const fillPercent = useMemo(
        () => getSliderPercentFromTime(video?.currentTime, duration),
        [duration, video?.currentTime]
    );

    const timeIndicatorColor = useMemo(() => {
        if (!pointerPercent || !fillPercent || pointerPercent > fillPercent) {
            return postFillColor;
        } else {
            return preFillColor;
        }
    }, [fillPercent, pointerPercent]);

    const seekTime = useMemo(() => {
        const time = prepareSeekTime(value);

        return !isLive ? time : `${time !== "0:00" ? "-" : ""}${time}`;
    }, [isLive, value]);

    return (
        isValidValue && (
            <>
                <div
                    className={cx("media-time-container")}
                    style={{
                        left: `calc(${pointerPercent}%)`
                    }}
                >
                    <div className={cx("switcher-slider-value-text")}>
                        {seekTime}
                    </div>
                </div>

                <div
                    className={cx("time-indicator", { isMobile })}
                    style={{
                        left: `${pointerPercent}%`,
                        backgroundColor: timeIndicatorColor
                    }}
                ></div>
            </>
        )
    );
};
