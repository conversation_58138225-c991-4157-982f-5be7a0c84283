/**
* -----------------------------------------------------------
* Switcher Time Slider
* -----------------------------------------------------------
*/

.switcher-time-slider-group {
    width: 100%;
    margin: 0;
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
    padding: 0 calc(rem(16));
    align-items: center;
    --track-height: #{rem(4)};
    --track-height-hover: #{rem(7)};
    pointer-events: none;

    .switcher-time-slider-container {
        width: 100%;
        height: 100%;
        flex-grow: 2;
        /** Prevent thumb flowing out of slider. */
        margin: 0 calc(var(--thumb-width, 0) / 2);
        position: relative;
        cursor: pointer;
        pointer-events: none;
        display: flex;

        &:hover {
            :global {
                .MuiSlider-root,
                .MuiSlider-rail,
                .MuiSlider-track {
                    height: var(--track-height-hover) !important;
                }
            }

            &.is-mobile,
            &.is-disabled {
                :global {
                    .MuiSlider-root,
                    .MuiSlider-rail,
                    .MuiSlider-track {
                        height: var(--track-height) !important;
                    }
                }
            }

            .time-indicator {
                opacity: 1;
            }
        }

        /**
       * -----------------------------------------------------------
       * Media Preview Container
       * -----------------------------------------------------------
       */

        .media-time-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: absolute;
            top: rem(-18);
            transform: translateX(-50%);
            will-change: left;
            border-radius: rem(2);
            user-select: none;

            .switcher-slider-value-text {
                font-size: rem(14);
                font-weight: 700;
                color: var(--player-controls-color);
            }
        }

        .time-indicator {
            position: absolute;
            bottom: 0;
            top: 50%;
            width: rem(3);
            height: var(--track-height-hover);
            z-index: 100;
            transform: translate(-50%, -50%);
            will-change: transform;
            opacity: 0;
            transition: opacity 0.25s ease;
            background-color: var(
                --sp-time-indicator-bg-color,
                --sp-time-indicator-bg-post-fill
            );

            &.is-mobile {
                display: none;
            }
        }
    }
}

// Overrides

:global {
    .MuiSlider-root,
    .MuiSlider-rail,
    .MuiSlider-track {
        height: var(--track-height) !important;
        pointer-events: all;
    }

    .MuiSlider-track,
    .MuiSlider-thumb {
        border: rem(1) solid var(--player-controls-color) !important;
    }

    // instead of using a separate track overlay for buffered amount, we can just use a linear gradient based on buffered percent
    // that has the buffered color on the left and the neutral color on the right of the current bufferent percent location
    .MuiSlider-rail {
        opacity: 1 !important;
        background: linear-gradient(
            to right,
            var(--buffered-fill-color) 0%,
            var(--buffered-fill-color)
                calc(
                    min(
                            calc(
                                var(--sp-buffered-amount, 0) /
                                    var(--sp-duration, 0.001) * 100
                            ),
                            100
                        ) * 1%
                ),
            var(--neutral-color)
                calc(
                    min(
                            calc(
                                var(--sp-buffered-amount, 0) /
                                    var(--sp-duration, 0.001) * 100
                            ),
                            100
                        ) * 1%
                ),
            var(--neutral-color) 100%
        ) !important;
    }

    .MuiSlider-track {
        background-color: var(--player-controls-color) !important;
    }

    .MuiSlider-thumb {
        background-color: var(--player-controls-color) !important;
        height: var(--thumb-width) !important;
        width: var(--thumb-width) !important;
        z-index: 101;

        &:hover,
        &.Mui-focusVisible {
            box-shadow: 0 0 0 rem(6) rgba(var(--player-controls-color-rgb), 0.1) !important;
        }

        &.Mui-active {
            box-shadow: 0 0 0 rem(6)
                rgba(var(--player-controls-color-rgb), 0.26) !important;
        }
    }
}
