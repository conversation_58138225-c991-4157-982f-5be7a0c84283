import React, {
    SyntheticEvent,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState
} from "react";
import Slider from "@mui/material/Slider";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import { useInterval } from "hooks/useInterval";
import { getBufferedEndPosition, setCustomProperty } from "../../helpers";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/reducers";
import { isMobile } from "react-device-detect";
import { usePlayPause } from "hooks/usePlayPause";
import { MediaPreviewContainer } from "./components/MediaPreviewContainer";
import { AppDispatch } from "store/store";
import { setUserSeeking } from "store/VideoSession/slice";
import { eventBus } from "helpers/eventBus";

const cx = classNames.bind(styles);

interface CustomTimeSliderProps {
    shakaPlayer: shaka.Player | undefined;
    video: HTMLVideoElement;
}

const CustomTimeSlider: React.FC<CustomTimeSliderProps> = ({
    shakaPlayer,
    video
}) => {
    const [currentTime, setCurrentTime] = useState<number>(0);
    const [duration, setDuration] = useState<number>(0);
    const [isInitialized, setIsInitialized] = useState<boolean>(false);
    const [hoverTime, setHoverTime] = useState<number | null>(null);
    /**
     * The duration and hover time fed to the media preview container on live videos.
     * It is "frozen" for 5 seconds to avoid the preview changing as live video duration grows
     */
    const [frozenLivePreview, setFrozenLivePreview] = useState<{
        duration: number;
        hoverTime: number;
    } | null>(null);
    const sliderRef = useRef<HTMLInputElement>(null);

    const {
        isVoD,
        userSeeking,
        timeSliderDelay,
        playHasStarted,
        currentCollectionVideo
    } = useSelector((s: RootState) => s.videoSession);
    const { playVideo, pauseVideo } = usePlayPause(video);
    const dispatch = useDispatch<AppDispatch>();

    const isLiveEnded = useMemo(() => {
        return (
            currentCollectionVideo?.broadcast?.details?.broadcastStatus ===
                "Ended" &&
            currentCollectionVideo?.broadcast?.videos?.[0]?.details?.status ===
                "live-inprogress"
        );
    }, [currentCollectionVideo]);
    const isLive = useMemo(() => !isVoD || isLiveEnded, [isVoD, isLiveEnded]);

    // Sets the actual seek time on the video element when the user sets a value with the slider
    const assignVideoPosition = useCallback(
        (currentTimeInSeconds: number) => {
            if (isLive) {
                currentTimeInSeconds =
                    currentTimeInSeconds +
                    (shakaPlayer?.seekRange().start ?? 0);
            }

            if (isMobile) setHoverTime(null);
            if (video?.paused) {
                if (isMobile) {
                    // this fixes a bug where some mobile browsers won't accept new seeked position while video paused
                    playVideo();
                    video!.currentTime = currentTimeInSeconds;
                    pauseVideo();
                } else {
                    video!.currentTime = currentTimeInSeconds;
                }
            } else {
                video!.currentTime = currentTimeInSeconds;
            }
        },
        [pauseVideo, playVideo, video, isLive, shakaPlayer]
    );

    // Updates the slider duration based on the current state of the player
    const updateSliderValues = useCallback(() => {
        const currentTime = Math.max(
            video.currentTime - (shakaPlayer?.seekRange()?.start ?? 0),
            0
        );
        let duration = 0;
        if (isLive) {
            duration =
                (shakaPlayer?.seekRange().end ?? 0) -
                (shakaPlayer?.seekRange().start ?? 0);
            //- 24; // We have an 24 second buffer offset ahead of where the user can seek (this allows for accurate presentation while maintaining a consistent functionality)
            // Update 9/25/24: We removed the 24 second buffer because it was causing the live preview to delay in moving the seek bar to the end.
        } else {
            duration = video.duration;
        }
        setDuration(duration);

        if (!userSeeking) {
            setCurrentTime(currentTime);
            if (sliderRef.current) {
                sliderRef.current.value = currentTime.toString();
            }
        }
        // Set the thumb to the end for a live stream as that's where playback will begin
        if (isLive && !playHasStarted) {
            setCurrentTime(duration);
        }

        if (currentTime === duration) {
            eventBus.emit("endVideo");
        }
    }, [isLive, userSeeking, video, shakaPlayer, playHasStarted]);

    // useEffect hook that initializes the time slider when the video duration is available and not yet initialized.
    // It sets the isInitialized state to true and updates the slider values.
    useEffect(() => {
        if (!!video?.duration && video.duration >= 0 && !isInitialized) {
            setIsInitialized(true);
            updateSliderValues();
        }
    }, [updateSliderValues, video?.duration, isInitialized]);

    // Monitors the video duration and updates the duration state when/if it changes.
    useEffect(() => {
        if (!isLive) setDuration(video?.duration ?? 0);
    }, [video?.duration, isLive]);

    const isValidEvent = useCallback(
        (
            value: number | number[],
            handler: "onChange" | "onChangeCommitted",
            eventType: string
        ) => {
            if (typeof value !== "number") return false;

            // on mobile, the triggers for the onChange and onChangeCommitted event are duplicated using mouse and touch events
            // there is a bug where an extra change happens and is committed on mobile coming from mouse events using incorrect position
            // after a genuine change happens and is committed at the correct location using the touch events

            // more info here: ==> https://github.com/mui/material-ui/issues/31869
            if (isMobile) {
                return handler === "onChange"
                    ? eventType !== "mousedown"
                    : eventType !== "mouseup";
            }

            return true;
        },
        []
    );

    // Handles the intermittent value of MUI, before the change is committed
    const handleSliderChange = useCallback(
        (event: Event, value: number | number[]) => {
            if (isValidEvent(value, "onChange", event.type)) {
                setCurrentTime(value as number);
                if (userSeeking || isMobile) {
                    setHoverTime(value as number);
                    if (isLive) {
                        setFrozenLivePreview({
                            hoverTime: value as number,
                            duration
                        });
                    }
                }
            }
        },
        [isValidEvent, userSeeking, duration, isLive]
    );

    // Handles when the final change is sent from the MUI slider
    // Additionally, this handles cleanup the state when dragging started (instead of onDragend)
    const handleSliderChangeCommitted = useCallback(
        (
            event: Event | SyntheticEvent<Element, Event>,
            value: number | number[]
        ) => {
            if (isValidEvent(value, "onChangeCommitted", event.type)) {
                //handle drag end
                if (isMobile) {
                    setHoverTime(null);
                    setFrozenLivePreview(null);
                }
                setTimeout(() => {
                    dispatch(setUserSeeking(false));
                    eventBus.emit("seekVideo");
                }, 100);

                assignVideoPosition(value as number);
            }
        },
        [dispatch, assignVideoPosition, isValidEvent]
    );

    // Handles the start of dragging on the MUI slider
    const handleSliderDragStart = useCallback(() => {
        dispatch(setUserSeeking(true));
    }, [dispatch]);

    const [currentMouseX, setCurrentMouseX] = useState<number>();
    const [currentSliderWidth, setCurrentSliderWidth] = useState<number>();

    const handleSetHoverTime = useCallback(() => {
        if (!isInitialized) return;
        const hoverTime = Math.min(
            Math.max((currentMouseX! / currentSliderWidth!) * duration, 0),
            duration
        );
        setHoverTime(isMobile ? currentTime : hoverTime);

        if (isLive) {
            setFrozenLivePreview({ hoverTime, duration });
        }
    }, [
        isInitialized,
        currentMouseX,
        currentSliderWidth,
        duration,
        currentTime,
        isLive
    ]);

    // This handles the hover effect and displays the tooltip time
    const handleMouseMove = useCallback(
        (event: React.MouseEvent) => {
            const rect = sliderRef.current!.getBoundingClientRect();
            const x = event.clientX - rect.left; // x position within the element.
            const width = rect.right - rect.left;

            setCurrentMouseX(x);
            setCurrentSliderWidth(width);
            handleSetHoverTime();
        },
        [handleSetHoverTime]
    );

    // Ends hovering and hides the tooltip
    const handleMouseLeave = useCallback(() => {
        if (!userSeeking) {
            setHoverTime(null);
            setFrozenLivePreview(null);
        }
    }, [userSeeking]);

    // update shaka player data on interval
    useInterval(() => {
        if (isVoD) {
            const bufferedAmount = getBufferedEndPosition(
                shakaPlayer,
                isVoD
            )?.toString();
            setCustomProperty("--sp-buffered-amount", bufferedAmount);
        }
    }, 100);

    // Update the slider values to match the latest state of the player
    useInterval(() => {
        if (!userSeeking) {
            updateSliderValues();
        }
    }, timeSliderDelay);

    // Update the frozen preview data for live streams
    useInterval(() => {
        if (!userSeeking && isLive && currentMouseX !== undefined) {
            handleSetHoverTime();
        }
    }, 5000);

    // check if controls disabled until live playback has begun
    const isDisabled = useMemo(
        () => isLive && !playHasStarted,
        [isLive, playHasStarted]
    );

    const calculatedHoverTime = useMemo(
        () => (isLive ? frozenLivePreview?.hoverTime ?? null : hoverTime),
        [isLive, frozenLivePreview?.hoverTime, hoverTime]
    );

    // Bail out if duration is not available yet
    if (duration === null || isNaN(duration)) {
        return null;
    }

    return (
        <div className={cx("switcher-time-slider-group")}>
            <div
                className={cx("switcher-time-slider-container", {
                    pointing: Boolean(hoverTime),
                    "is-mobile": isMobile,
                    "is-live": isLive,
                    "is-disabled": isDisabled
                })}
                onMouseLeave={handleMouseLeave}
            >
                <Slider
                    ref={sliderRef}
                    value={currentTime}
                    min={0}
                    max={duration}
                    disabled={isDisabled}
                    step={0.0001}
                    onChange={handleSliderChange}
                    onChangeCommitted={handleSliderChangeCommitted}
                    onMouseMove={handleMouseMove}
                    onMouseLeave={() => setCurrentMouseX(undefined)}
                    onMouseDown={handleSliderDragStart}
                    onTouchStart={handleSliderDragStart}
                    valueLabelDisplay="off"
                />

                {calculatedHoverTime !== null &&
                    !Number.isNaN(calculatedHoverTime) &&
                    !isDisabled && (
                        <MediaPreviewContainer
                            hoverTime={calculatedHoverTime}
                            duration={
                                isLive ? frozenLivePreview?.duration : duration
                            }
                        />
                    )}
            </div>
        </div>
    );
};

export default CustomTimeSlider;
