import React, { useCallback, useMemo } from "react";
import { PlayPauseButton } from "./PlayPauseButton";
import { PlayEffect } from "./PlayPauseEffect/PlayEffect";
import { PauseEffect } from "./PlayPauseEffect/PauseEffect";
import { StaticPlayPauseButton } from "./StaticPlayPauseButton";
import { VolumeControl } from "./VolumeControl";
import { isIOS, isMobile } from "react-device-detect";
import { RootState } from "store/reducers";
import { useDispatch, useSelector } from "react-redux";
import styles from "../index.module.scss";
import classNames from "classnames/bind";
import { useInterval } from "hooks/useInterval";
import { AppDispatch } from "store/store";
import {
    setActiveContextMenu,
    setHeartbeatDelay,
    setSeekingStart
} from "store/VideoSession/slice";
import CustomTimeSlider from "./TimeSlider";
import { usePlayPause } from "hooks/usePlayPause";
import Loading from "components/loading/Loading";
import {
    KebabMenu,
    KebabMenuItemIds,
    KebabMenuSelectItem,
    KebabMenuSelectItemOption
} from "./KebabMenu";
import { useTranslation } from "react-i18next";
import { VideoPlayerTime } from "./VideoPlayerTime";
import { ContextMenus, VideoSpeed } from "store/VideoSession/types";
import { QualitySelector } from "./QualitySelector";
import { PiPButton } from "./PiPButton";
import { FullscreenButton } from "./FullscreenButton";
import { MoreButton } from "./MoreButton";
import { useSelectQuality } from "hooks/useSelectQuality";
import { setActiveModal } from "store/Modals/slice";
import { Modals } from "store/Modals/types";
import { setUserSelectedSpeed } from "store/VideoSession/slice";
import { CastingProvider } from "../hooks/useShakaInit";
import shaka from "shaka-player/dist/shaka-player.compiled";
import { IconButton } from "components/Buttons/IconButton";
import ClosedCaptionsIcon from "assets/icons/closed-captions.svg?react";
import { useRedirectUrl } from "hooks/useRedirectUrl";
import { addNotification } from "store/notification/slice";
import {
    AddNotificationPayload,
    NotificationType
} from "store/notification/types";
import { useEventTracking } from "hooks/useEventTracking";

const cx = classNames.bind(styles);

const enableControlRedesign =
    import.meta.env.VITE_ENABLE_CONTROL_REDESIGN === "true";

export const MediaControlBar = ({
    video,
    videoContainer,
    shakaPlayer,
    onTouch,
    showControls,
    fadeControls,
    isFeaturedTrailer,
    isLitePlayer,
    castingProvider,
    castingAvailable,
    isCasting,
    handleCast,
    captionsAvailable = false,
    toggleCaptions,
    captionsVisible = false,
    collectionId
}: {
    video: HTMLVideoElement;
    videoContainer: React.RefObject<HTMLDivElement>;
    shakaPlayer: shaka.Player | undefined;
    onTouch: () => void;
    showControls: boolean;
    fadeControls: boolean;
    isFeaturedTrailer?: boolean;
    isLitePlayer?: boolean;
    castingProvider: CastingProvider;
    castingAvailable: boolean;
    isCasting: boolean;
    handleCast: () => void;
    captionsAvailable?: boolean;
    toggleCaptions?: () => void;
    captionsVisible?: boolean;
    collectionId?: string;
}) => {
    const { trackEvent } = useEventTracking();
    const { t } = useTranslation();
    const { togglePlayPause } = usePlayPause(video);
    const {
        isPlaying,
        isInitialBuffering,
        isInitialPlay,
        buffering,
        isVoD,
        streamingProtocol,
        isFullScreen,
        userSelectedSpeed,
        activeContextMenu,
        currentCollectionVideo
    } = useSelector((s: RootState) => s.videoSession);

    const catalogId = useSelector(
        (s: RootState) => s.catalogState?.catalog?.details?.id
    );

    const redirectUrl = useRedirectUrl();

    const {
        qualityOptions,
        onQualityOptionsSelected,
        userSelectedQualityLevel,
        getLevelLabel,
        isAuto
    } = useSelectQuality({
        shakaPlayer
    });

    const reportOptions = useMemo<KebabMenuSelectItemOption<string>[]>(
        () => [
            {
                id: "Report",
                label: t("controls:report")
            },
            {
                id: "DMCA",
                label: t("controls:dmca-notice")
            }
        ],
        [t]
    );

    const isLive = useMemo(() => !isVoD, [isVoD]);

    const isLiveEnded = useMemo(() => {
        return (
            currentCollectionVideo?.broadcast?.details?.broadcastStatus ===
                "Ended" &&
            currentCollectionVideo?.broadcast?.videos?.[0]?.details?.status ===
                "live-inprogress"
        );
    }, [currentCollectionVideo]);

    const dispatch = useDispatch<AppDispatch>();

    const castToDeviceLabel = useMemo(() => {
        if (isCasting) return t("controls:is-casting");

        switch (castingProvider) {
            case CastingProvider.ChromeCast:
                return t("controls:cast-to-device");
            case CastingProvider.AirPlay:
                return t("controls:cast-to-airplay");
            default:
                return t("controls:cast-to-device");
        }
    }, [castingProvider, isCasting, t]);

    const handleStaticPlayPauseClick = useCallback(() => {
        if (isMobile) {
            onTouch();
            togglePlayPause();
        } else {
            togglePlayPause();
        }
    }, [onTouch, togglePlayPause]);

    const closeMenu = useCallback(
        () => dispatch(setActiveContextMenu(ContextMenus.None)),
        [dispatch]
    );

    const onPiPClick = useCallback(() => {
        video?.requestPictureInPicture();
        closeMenu();
    }, [closeMenu, video]);

    const handleReportOptionSelected = useCallback(
        (selectedId: string) => {
            if (selectedId === "Report") {
                dispatch(setActiveModal(Modals.Report));
            } else if (selectedId === "DMCA") {
                dispatch(setActiveModal(Modals.DMCA));
            }
            closeMenu();
        },
        [closeMenu, dispatch]
    );

    const handleToggleFullScreen = useCallback(() => {
        if (!isFullScreen) {
            if (isIOS) {
                try {
                    // @ts-expect-error - webkitEnterFullscreen is not a standard method
                    video!.webkitEnterFullscreen?.();
                } catch (e) {}
            } else {
                try {
                    videoContainer!.current!.requestFullscreen();
                } catch (e) {
                    video!.requestFullscreen();
                }
            }
        } else {
            document?.exitFullscreen();
        }
        closeMenu();
    }, [closeMenu, isFullScreen, video, videoContainer]);

    // Update seeking time
    useInterval(() => {
        if (!!video && !video?.seeking) {
            dispatch(setSeekingStart(video.currentTime));
        }
    }, 1000);

    const speeds = useMemo<VideoSpeed[]>(
        () =>
            isLive
                ? [0.25, 0.5, 0.75, "Normal"]
                : [0.25, 0.5, 0.75, "Normal", 1.25, 1.5, 1.75, 2],
        [isLive]
    );

    const speedOptions = useMemo<
        KebabMenuSelectItemOption<number | "Normal">[]
    >(() => {
        return [
            ...(speeds?.map((speed) => ({
                id: speed === 1 ? "Normal" : speed,
                label: speed.toString(),
                selected: speed === userSelectedSpeed
            })) ?? [])
        ];
    }, [speeds, userSelectedSpeed]);

    /* adjust the tracking so that the user's watched seconds are appropriately updated */
    const adjustTracking = useCallback(
        (speed: number) => {
            const interval = 1 / speed;

            dispatch(setHeartbeatDelay(4000 * interval));
        },
        [dispatch]
    );

    const dispatchShareLinkNotification = useCallback(
        (success: boolean, message: string) => {
            const notificationParams: AddNotificationPayload = {
                type: success
                    ? NotificationType.Success
                    : NotificationType.Danger,
                message,
                class: "interactives"
            };

            // Auto-fade the notification if it was successful
            if (success) {
                notificationParams.fadeMilliseconds = 5000;
            }

            dispatch(addNotification(notificationParams));
        },
        [dispatch]
    );

    const copyShareLink = useCallback(() => {
        const broadcastId = currentCollectionVideo?.details?.broadcastId;

        if (!broadcastId || !collectionId) {
            dispatchShareLinkNotification(false, t("errors:share-link-error"));
            return;
        }

        const url = new URL(redirectUrl.href);
        url.searchParams.set("switcher-pbid", broadcastId);
        url.searchParams.set("switcher-pcid", collectionId);
        const shareUrl = url.toString();
        const errorMessageWithLink = t("errors:share-link-copy-error", {
            link: shareUrl
        });

        // Copy the share URL to the clipboard
        if (!navigator.clipboard) {
            dispatchShareLinkNotification(false, errorMessageWithLink);
            return;
        }

        navigator.clipboard
            .writeText(shareUrl)
            .then(() => {
                trackEvent("Share Link Copied", {
                    shareUrl,
                    catalogId,
                    collectionId,
                    broadcastId
                });
                dispatchShareLinkNotification(
                    true,
                    t("controls:share-link-copied")
                );
            })
            .catch(() => {
                dispatchShareLinkNotification(false, errorMessageWithLink);
            });
    }, [
        currentCollectionVideo,
        catalogId,
        collectionId,
        redirectUrl,
        dispatchShareLinkNotification,
        t,
        trackEvent
    ]);

    /** if the video is paused, this prevents resuming playback when changing the speed
     * must be called both before AND after changing the speed
     * we're actually just making the player play, and then pausing it again
     */
    const preventResumePlaying = () => {
        if (!isPlaying) {
            togglePlayPause();
        }
    };

    const changeSpeed = (speed: VideoSpeed) => {
        if (!!shakaPlayer) {
            preventResumePlaying();
            const _speed: number =
                speed === "Normal"
                    ? 1
                    : Number(speeds.find((s) => s === speed));
            dispatch(setUserSelectedSpeed(speed));
            adjustTracking(_speed);
            shakaPlayer?.trickPlay(_speed ?? 1);
            preventResumePlaying();
        }
    };

    return (
        <>
            {buffering && (!isMobile || !isInitialPlay) && (
                <Loading variant="no-overlay" />
            )}
            {!isInitialBuffering && showControls && (
                <div
                    className={cx("media-controls-container", {
                        fadeControls,
                        preventFade: activeContextMenu !== ContextMenus.None
                    })}
                >
                    <div className={cx("media-progress-bar-group")}>
                        <CustomTimeSlider
                            shakaPlayer={shakaPlayer}
                            video={video}
                        />
                    </div>
                    <div className={cx("media-controls-group")}>
                        <div className={cx("left-controls-container")}>
                            <PlayPauseButton handleClick={togglePlayPause} />

                            {isLive ? (
                                <div className={cx("live-text")}>LIVE</div>
                            ) : isLiveEnded ? (
                                <div className={cx("ended-text")}>ENDED</div>
                            ) : (
                                <VideoPlayerTime
                                    currentPosition={video?.currentTime ?? 0}
                                    duration={video?.duration ?? 0}
                                />
                            )}

                            {!enableControlRedesign && (
                                <VolumeControl video={video} />
                            )}
                        </div>
                        <div className={cx("right-controls-container")}>
                            {!enableControlRedesign ? (
                                <>
                                    {/** Quality selection not available on HLS (i.e. iOS devices) */}
                                    {shakaPlayer &&
                                        streamingProtocol !== "hls" && (
                                            <QualitySelector
                                                shakaPlayer={shakaPlayer}
                                            />
                                        )}
                                    <PiPButton onClick={onPiPClick} />
                                    <FullscreenButton
                                        isFullScreen={isFullScreen}
                                        onClick={handleToggleFullScreen}
                                    />
                                    <MoreButton />
                                </>
                            ) : (
                                <>
                                    <VolumeControl video={video} />
                                    {captionsAvailable && (
                                        <IconButton
                                            icon={ClosedCaptionsIcon}
                                            onClick={toggleCaptions}
                                            aria-pressed={captionsVisible}
                                            aria-label={
                                                captionsVisible
                                                    ? t(
                                                          "controls:disable-captions"
                                                      )
                                                    : t(
                                                          "controls:enable-captions"
                                                      )
                                            }
                                            additionalClasses={[
                                                styles["captions-button"],
                                                captionsVisible ? "active" : ""
                                            ]}
                                        />
                                    )}
                                    <KebabMenu
                                        items={[
                                            {
                                                id: KebabMenuItemIds.PlaybackSpeed,
                                                label: t(
                                                    "controls:playback-speed"
                                                ),
                                                options: speedOptions,
                                                hidden: false,
                                                selected:
                                                    userSelectedSpeed === 1
                                                        ? "Normal"
                                                        : userSelectedSpeed,
                                                onOptionSelect: (
                                                    s: VideoSpeed
                                                ) => changeSpeed(s)
                                            } as KebabMenuSelectItem,
                                            {
                                                id: KebabMenuItemIds.VideoQuality,
                                                label: t(
                                                    "controls:video-quality"
                                                ),
                                                options: qualityOptions,
                                                hidden:
                                                    streamingProtocol === "hls",
                                                selected: getLevelLabel(
                                                    isAuto
                                                        ? "auto"
                                                        : userSelectedQualityLevel
                                                ),
                                                onOptionSelect:
                                                    onQualityOptionsSelected,
                                                shouldCloseOnSelect: true
                                            } as KebabMenuSelectItem,
                                            {
                                                id: KebabMenuItemIds.Share,
                                                label: t("controls:share"),
                                                onClick: copyShareLink,
                                                hidden: isFeaturedTrailer
                                            },
                                            {
                                                id: KebabMenuItemIds.CastToDevice,
                                                label: castToDeviceLabel,
                                                onClick: handleCast,
                                                hidden: !castingAvailable
                                            },
                                            {
                                                id: KebabMenuItemIds.FullScreen,
                                                label: t("controls:fullscreen"),
                                                onClick: handleToggleFullScreen,
                                                hidden: isCasting
                                            },
                                            {
                                                id: KebabMenuItemIds.PictureInPicture,
                                                label: t(
                                                    "controls:picture-in-picture"
                                                ),
                                                onClick: onPiPClick,
                                                hidden:
                                                    !document.pictureInPictureEnabled ||
                                                    isCasting
                                            },
                                            {
                                                id: KebabMenuItemIds.Report,
                                                label: t("controls:report"),
                                                options: reportOptions,
                                                onOptionSelect:
                                                    handleReportOptionSelected,
                                                hideSelected: true
                                            } as KebabMenuSelectItem
                                        ]}
                                        preventMobileStyles={
                                            isFeaturedTrailer || isLitePlayer
                                        }
                                        onClose={closeMenu}
                                    />
                                </>
                            )}
                        </div>
                    </div>
                </div>
            )}

            {(isInitialPlay || isMobile) &&
                (!buffering || (isMobile && isInitialPlay)) &&
                !isInitialBuffering &&
                showControls && (
                    <StaticPlayPauseButton
                        handleClick={handleStaticPlayPauseClick}
                        fadeControls={fadeControls}
                    />
                )}
            {!isMobile && isPlaying && <PlayEffect />}
            {!isMobile && !isPlaying && !isInitialPlay && <PauseEffect />}
        </>
    );
};
