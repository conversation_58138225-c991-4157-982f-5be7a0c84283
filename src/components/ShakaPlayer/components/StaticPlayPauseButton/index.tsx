import PlayIcon from "assets/icons/play.svg?react";
import PauseIcon from "assets/icons/pause.svg?react";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import { useSelector } from "react-redux";
import { RootState } from "store/reducers";
const cx = classNames.bind(styles);

export const StaticPlayPauseButton = ({
    handleClick,
    fadeControls
}: {
    handleClick: () => void;
    fadeControls: boolean;
}) => {
    const { isPlaying } = useSelector((s: RootState) => s.videoSession);

    return (
        <div className={cx("initial-play-button-container", { fadeControls })}>
            <div className={cx("initial-play-button")} onClick={handleClick}>
                {isPlaying ? (
                    <PauseIcon className={cx("media-pause-icon")} />
                ) : (
                    <PlayIcon className={cx("media-play-icon")} />
                )}
            </div>
        </div>
    );
};
