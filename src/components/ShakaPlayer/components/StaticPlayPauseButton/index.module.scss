.initial-play-button-container {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    pointer-events: none;
    @include prep-fade-out(0, 0.5s);

    &.fadeControls {
        @include fade-out();
    }

    .initial-play-button {
        width: 20%;
        height: auto;
        aspect-ratio: 1 / 1;
        background-color: rgba(0, 0, 0, 0.5);
        border-radius: 100%;
        display: flex;
        flex-flow: row nowrap;
        justify-content: center;
        align-items: center;
        animation: 0.75s ease easeInElement;
        cursor: pointer;
        pointer-events: all;

        @include mobile {
            margin-top: rem(-20);
        }

        @media screen and (max-width: rem(400)) {
            width: 15%;
        }

        &:hover {
            svg {
                @include hover-glow-shadow(var(--player-controls-color));
            }
        }

        svg {
            display: block;
            color: var(--player-controls-color);
            height: 75%;
            width: 75%;

            @include prep-glow-shadow();
        }
    }
}

/* Styles for specific player states */
:global(.is-paused) .initial-play-button svg {
    /* center play button */
    margin-left: 13%;
}

:global(.is-playing) .initial-play-button svg {
    width: 65%;
    height: 65%;
}
