import React, { useCallback, useEffect, useMemo, useState } from "react";
import { isIOS, isMobile } from "react-device-detect";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/reducers";
import { AppDispatch } from "store/store";
import {
    setEndedState,
    setHeartbeatDelay,
    setIsAutoPlay,
    setIsInitialPlay,
    setPlayHasStarted,
    setPlayingState,
    setSeekingStart
} from "store/VideoSession/slice";
import { getPlaybackStats, prepareSeekTime } from "../helpers";
import { useEventTracking } from "hooks/useEventTracking";
import { useInterval } from "hooks/useInterval";
import { usePlayPause } from "hooks/usePlayPause";
import { BroadcastResponse } from "@switcherstudio/player-api-client";
import { eventBus } from "helpers/eventBus";

export const ShakaVideoElement = ({
    video,
    videoRef,
    shakaPlayer,
    poster,
    onTouch,
    beginTimeSliderInterval,
    endTimeSliderInterval,
    isCasting = false
}: {
    video: HTMLVideoElement;
    videoRef: React.RefObject<HTMLVideoElement>;
    shakaPlayer: React.MutableRefObject<shaka.Player | undefined>;
    poster: string;
    onTouch: () => void;
    beginTimeSliderInterval: () => void;
    endTimeSliderInterval: () => void;
    isCasting?: boolean;
}) => {
    const dispatch = useDispatch<AppDispatch>();

    const {
        currentCollectionVideo,
        isInitialPlay,
        sessionId,
        isFullScreen,
        isVoD,
        seekingStart,
        heartbeatDelay,
        isAutoPlay
    } = useSelector((s: RootState) => s.videoSession);

    const { togglePlayPause } = usePlayPause(video);
    const { trackEvent } = useEventTracking();

    const broadcast = useMemo<BroadcastResponse | undefined>(
        () => currentCollectionVideo?.broadcast,
        [currentCollectionVideo]
    );

    const [hbInterval, setHbInterval] = useState<number | null>(null);

    const beginHearbeatInterval = useCallback(() => {
        /* set the interval to the inverse of the speed the user has selected, or default to 4000 */
        dispatch(setHeartbeatDelay(hbInterval ?? 4000));
    }, [dispatch, hbInterval]);

    const clearHearbeatInterval = useCallback(() => {
        /* in case a user has changed the speed and pauses the video, we need to preserve the tracking changes */
        setHbInterval(heartbeatDelay);
        dispatch(setHeartbeatDelay(null));
    }, [dispatch, heartbeatDelay]);

    const onPlay = useCallback(() => {
        // dispatch(addNotification({ type: NotificationType.Info, message: "On Play", class: "shopping"}));
        dispatch(setPlayingState(true));
        beginHearbeatInterval();
        if (isVoD) beginTimeSliderInterval();
        const { bitrate, quality, framerate } = getPlaybackStats(
            shakaPlayer.current
        );

        if (isInitialPlay) {
            // If playing state is false, we can update this state to reflect autoplay success

            trackEvent(
                "Video Playback Started",
                {
                    session_id: sessionId,
                    content_asset_ids: [broadcast?.details?.id],
                    content_pod_ids: [`${broadcast?.details?.id}-pod`],
                    ad_asset_ids: [],
                    ad_pod_ids: [],
                    ad_types: [],
                    position: video?.currentTime,
                    total_length:
                        broadcast?.details?.broadcastStatus === "Active"
                            ? null
                            : video?.duration,
                    video_player: "switcher",
                    sound: (video?.volume ?? 0) * 100,
                    full_screen: isFullScreen,
                    ad_enabled: false,
                    bitrate,
                    framerate,
                    quality
                },
                true
            );

            trackEvent(
                "Video Content Started",
                {
                    session_id: sessionId,
                    asset_id: broadcast?.details?.id,
                    pod_id: `${broadcast?.details?.id}-pod`,
                    title: broadcast?.details?.title,
                    description: broadcast?.details?.description,
                    airdate: broadcast?.details?.endedAt,
                    position: video?.currentTime,
                    total_length:
                        broadcast?.details?.broadcastStatus === "Active"
                            ? null
                            : video?.duration
                },
                true
            );
            if (!isVoD && !!shakaPlayer?.current) {
                shakaPlayer.current.goToLive();
            }
            // These need to be here as setting them in the "usePlayPause" hook causes
            // the initial play to be false before getting to this point.  It's more reliable
            // in this case to use the video element events.
            dispatch(setIsInitialPlay(false));
            dispatch(setPlayHasStarted(true));
        } else {
            trackEvent("Video Playback Resumed", {
                session_id: sessionId,
                content_asset_id: broadcast?.details?.id,
                content_pod_id: `${broadcast?.details?.id}-pod`,
                ad_asset_id: null,
                ad_pod_id: null,
                ad_type: null,
                position: video?.currentTime,
                total_length:
                    broadcast?.details?.broadcastStatus === "Active"
                        ? null
                        : video?.duration,
                video_player: "switcher",
                sound: (video?.volume ?? 0) * 100,
                full_screen: isFullScreen,
                ad_enabled: false,
                bitrate,
                framerate,
                quality
            });
        }
    }, [
        dispatch,
        beginHearbeatInterval,
        beginTimeSliderInterval,
        broadcast?.details?.broadcastStatus,
        broadcast?.details?.description,
        broadcast?.details?.endedAt,
        broadcast?.details?.id,
        broadcast?.details?.title,
        isFullScreen,
        isInitialPlay,
        isVoD,
        sessionId,
        shakaPlayer,
        trackEvent,
        video?.currentTime,
        video?.duration,
        video?.volume
    ]);

    const onPause = useCallback(() => {
        dispatch(setPlayingState(false));
        clearHearbeatInterval();
        if (isVoD) endTimeSliderInterval();
        const { bitrate, quality, framerate } = getPlaybackStats(
            shakaPlayer.current
        );

        if (!video?.ended)
            trackEvent("Video Playback Paused", {
                session_id: sessionId,
                content_asset_id: broadcast?.details?.id,
                content_pod_id: `${broadcast?.details?.id}-pod`,
                ad_asset_id: null,
                ad_pod_id: null,
                ad_type: null,
                position: video?.currentTime,
                total_length:
                    broadcast?.details?.broadcastStatus === "Active"
                        ? null
                        : video?.duration,
                video_player: "switcher",
                sound: (video?.volume ?? 0) * 100,
                full_screen: isFullScreen,
                ad_enabled: false,
                bitrate,
                framerate,
                quality
            });
    }, [
        isVoD,
        broadcast?.details?.broadcastStatus,
        broadcast?.details?.id,
        clearHearbeatInterval,
        dispatch,
        endTimeSliderInterval,
        isFullScreen,
        sessionId,
        shakaPlayer,
        trackEvent,
        video?.currentTime,
        video?.duration,
        video?.ended,
        video?.volume
    ]);

    const onEnd = useCallback(() => {
        clearHearbeatInterval();
        endTimeSliderInterval();

        trackEvent(
            "Video Content Completed",
            {
                session_id: sessionId,
                asset_id: broadcast?.details?.id,
                pod_id: `${broadcast?.details?.id}-pod`,
                title: broadcast?.details?.title,
                description: broadcast?.details?.description,
                airdate: broadcast?.details?.endedAt,
                position: video?.currentTime,
                total_length:
                    broadcast?.details?.broadcastStatus === "Active"
                        ? null
                        : video?.duration
            },
            true
        );

        dispatch(setPlayingState(false));
        dispatch(setEndedState(true));
    }, [
        broadcast?.details?.broadcastStatus,
        broadcast?.details?.description,
        broadcast?.details?.endedAt,
        broadcast?.details?.id,
        broadcast?.details?.title,
        clearHearbeatInterval,
        dispatch,
        endTimeSliderInterval,
        sessionId,
        trackEvent,
        video?.currentTime,
        video?.duration
    ]);

    const onSeeked = useCallback(() => {
        if (!video) return;

        const currentPosition = video.currentTime;

        if (seekingStart !== null && seekingStart !== undefined) {
            trackEvent(
                "Seek Event",
                {
                    seekBegin: prepareSeekTime(seekingStart),
                    seekEnd: prepareSeekTime(currentPosition),
                    rawSeekBegin: seekingStart,
                    rawSeekEnd: currentPosition
                },
                false,
                true
            );
        }

        dispatch(setSeekingStart(null));
    }, [dispatch, seekingStart, trackEvent, video]);

    const handleVideoClick = useCallback(() => {
        if (isMobile) {
            onTouch();
        } else {
            togglePlayPause();
        }
    }, [onTouch, togglePlayPause]);

    // HEARTBEAT INTERVAL
    useInterval(() => {
        trackEvent("Video Content Playing", {
            session_id: sessionId,
            asset_id: broadcast?.details?.id,
            pod_id: `${broadcast?.details?.id}-pod`,
            title: broadcast?.details?.title,
            description: broadcast?.details?.description,
            airdate: broadcast?.details?.endedAt,
            position: video?.currentTime,
            total_length:
                broadcast?.details?.broadcastStatus === "Active"
                    ? null
                    : video?.duration
        });
    }, heartbeatDelay);

    //clear tracking interval when video unloads
    useEffect(() => {
        return () => {
            clearHearbeatInterval();
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const [autoPlay] = useState<boolean>(isAutoPlay);
    useEffect(() => {
        if (isAutoPlay) {
            dispatch(setIsAutoPlay(false));
            // Wait for onPlay to set playing state to true - in case autoplay fails
            dispatch(setPlayingState(false));
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [dispatch]);

    // Apply custom event listeners for casting as the player/video proxy does NOT fire the video events during a casting session
    // NOTE: Based on review of the sourcecode, this looks to be a bug and may work in future versions.
    useEffect(() => {
        if (isCasting) {
            eventBus.on("playVideo", onPlay);
            eventBus.on("pauseVideo", onPause);
            eventBus.on("seekVideo", onSeeked);
            eventBus.on("endVideo", onEnd);
        }

        return () => {
            eventBus.off("playVideo", onPlay);
            eventBus.off("pauseVideo", onPause);
            eventBus.off("seekVideo", onSeeked);
            eventBus.off("endVideo", onEnd);
        };
    }, [onPlay, onPause, onSeeked, onEnd, isCasting]);

    return (
        <video
            ref={videoRef}
            poster={poster}
            onEnded={onEnd}
            onSeeked={onSeeked}
            onPlay={onPlay}
            onPause={onPause}
            onClick={handleVideoClick}
            playsInline={isIOS}
            autoPlay={autoPlay}
            tabIndex={0}
        />
    );
};
