import PipIcon from "assets/icons/pip.svg?react";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
const cx = classNames.bind(styles);

export const PiPButton = ({ onClick }: { onClick: () => void }) => {
    if (!document.pictureInPictureEnabled) {
        return <></>;
    }

    return (
        <button className={cx("pip-button")} onClick={() => onClick()}>
            <PipIcon aria-hidden />
        </button>
    );
};
