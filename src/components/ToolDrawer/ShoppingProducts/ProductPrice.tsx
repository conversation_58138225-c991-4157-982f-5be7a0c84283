interface ProductPriceProps {
    amount: number;
    currencyCode: string;
    quantity?: number;
}
export const ProductPrice = ({
    amount,
    currencyCode,
    quantity
}: ProductPriceProps) => {
    const isUSD = currencyCode === "USD";
    const hasQuantity = !!quantity;
    const totalAmt = hasQuantity ? quantity * amount : amount;
    const totalPrice = totalAmt.toFixed(2);
    return <>{isUSD ? "$" + totalPrice : totalPrice + " " + currencyCode}</>;
};
