import { useDispatch, useSelector } from "react-redux";
import { updateSelectedProduct } from "../../../../store/shopping/slice";
import { RootState } from "../../../../store/reducers";
import { ProductListItem } from "./ProductListItem";
import CartNoneIcon from "assets/icons/cart-none.svg?react";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import { useMemo } from "react";
import { useGatedContent } from "../../../../hooks/useGatedContent";
import { useTranslation } from "react-i18next";
const cx = classNames.bind(styles);

export const ProductListModal = () => {
    const dispatch = useDispatch();
    const { products } = useSelector((s: RootState) => s.shopping);
    const { playlistBroadcastIsEntitled, playlistBroadcastIsGated } =
        useGatedContent();
    const { t } = useTranslation();

    const _productMsg = useMemo<string>(
        () =>
            !playlistBroadcastIsEntitled && playlistBroadcastIsGated
                ? t("shopping:entitled-msg")
                : t("shopping:no-products"),
        [playlistBroadcastIsGated, playlistBroadcastIsEntitled, t]
    );

    return (
        <>
            {" "}
            {products.length === 0 ? (
                <div className={cx("products-empty-wrapper")}>
                    <div className={cx("products-empty")}>
                        <CartNoneIcon />
                        {_productMsg}
                    </div>
                </div>
            ) : (
                <>
                    <div className={cx("shopping-header")}>
                        <h3>Featured Products</h3>
                    </div>
                    <div className={cx("product-wrapper")}>
                        {products.map((p, i) => {
                            return (
                                <ProductListItem
                                    key={p.productId}
                                    product={p}
                                    index={i}
                                    onClick={() =>
                                        dispatch(updateSelectedProduct(p))
                                    }
                                />
                            );
                        })}
                    </div>
                </>
            )}
        </>
    );
};
