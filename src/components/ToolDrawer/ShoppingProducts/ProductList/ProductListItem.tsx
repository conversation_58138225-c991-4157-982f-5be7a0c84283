import NoImageIcon from "assets/icons/no-image-available.png";
import ChevronRight from "assets/icons/chevron-right.svg?react";
import { ProductPrice } from "../ProductPrice";
import { ShopifyProductWithOptions } from "api/liveCommentsReceiver/types";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import { Image } from "components/Image";
const cx = classNames.bind(styles);

export const ProductListItem = ({
    product: {
        productId,
        title,
        image,
        minPrice,
        maxPrice,
        currencyCode,
        availableForSale
    },
    onClick
}: {
    product: ShopifyProductWithOptions;
    index: number;
    onClick: () => void;
}) => {
    return (
        <div
            className={cx("product-card", {
                "out-of-stock": !availableForSale
            })}
            key={productId}
            onClick={
                availableForSale
                    ? onClick
                    : () => {
                          return;
                      }
            }
        >
            <div className={cx("product-info")}>
                <div className={cx("product-image")}>
                    {image ? (
                        <Image
                            className={cx("thumbnail")}
                            src={image}
                            alt={title}
                        />
                    ) : (
                        <img
                            className={cx("thumbnail")}
                            src={NoImageIcon}
                            alt=""
                        />
                    )}
                </div>
                <div className={cx("product-info-text")}>
                    <div className={cx("product-title")}>{title}</div>
                    <div className={cx("product-price")}>
                        {!!minPrice && (
                            <ProductPrice
                                amount={Number(minPrice)}
                                currencyCode={currencyCode}
                            />
                        )}
                        {!!minPrice && !!maxPrice && minPrice !== maxPrice && (
                            <span>
                                <span> - </span>
                                <ProductPrice
                                    amount={Number(maxPrice)}
                                    currencyCode={currencyCode}
                                />
                            </span>
                        )}
                    </div>
                </div>
            </div>
            {availableForSale ? (
                <div className={cx("product-chevron")}>
                    <ChevronRight className={cx("chevron")} />
                </div>
            ) : (
                <div className={`badge badge-out-of-stock`}>Out of Stock</div>
            )}
        </div>
    );
};
