@import "../index.module.scss";

.products-empty-wrapper {
    border: rem(1) dashed rgba(var(--interactive-panel-background-color-rgb));
    padding: rem(12);
    margin-left: rem(16);
    margin-right: rem(16);
    width: calc(100% - rem(32));

    .products-empty {
        padding: rem(25);
        background: rgba(
            var(--interactive-panel-background-contrast-color-rgb),
            0.1
        );
        color: rgba(var(--interactive-panel-text-color-rgb), 0.6);
        box-shadow: 0 rem(1) rem(4) rgba(0, 0, 0, 0.08);
        border-radius: rem(8);
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;

        svg {
            width: rem(34);
            height: rem(36);
            margin-bottom: rem(11);
            color: rgba(var(--interactive-panel-text-color-rgb), 0.6);
        }

        button {
            margin-top: rem(24);
            border: none;
            background: none;
            font-weight: 600;
            font-size: rem(14);
            line-height: rem(20);
            text-decoration: underline;
            color: rgba(var(--brand-color-primary-rgb)) !important;
            cursor: pointer;

            transition: 0.25s color ease-in-out;

            &:hover {
                color: rgba(var(--brand-color-primary-rgb), 0.2);
            }
        }
    }
}

.shopping-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-bottom: rem(8);

    h3 {
        margin: 0;
        font-style: normal;
        font-weight: 400;
        font-size: rem(14);
        line-height: rem(20);
        text-transform: uppercase;
        padding: 0 rem(16);
        color: rgba(var(--interactive-panel-text-color-rgb));
    }
}

.product-wrapper {
    display: flex;
    align-items: center;
    flex-direction: column;

    .product-card {
        display: flex;
        flex-direction: row;
        align-content: center;
        justify-content: space-between;
        align-items: center;
        gap: rem(15);
        width: 100%;
        padding: rem(16);
        margin-top: rem(1);
        cursor: pointer;
        position: relative;
        border-radius: rem(12);
        transition: 0.25s ease-in-out background-color;

        &:before {
            content: "";
            border-top: rem(0.5) solid #e3e3e3;
            position: absolute;
            top: rem(-1);
            left: rem(16);
            right: rem(16);
        }

        &:first-child {
            margin-top: 0;

            &:before {
                display: none;
            }
        }

        &:hover {
            background-color: rgba(
                var(--interactive-panel-background-contrast-color-rgb),
                0.05
            );
        }

        &.out-of-stock {
            cursor: default;

            .product-info {
                filter: opacity(0.3) grayscale(1);
            }

            &:hover {
                background-color: unset;
            }
        }

        .product-info {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;

            .product-image {
                .thumbnail {
                    height: rem(56);
                    width: rem(56);
                    border: rem(1) solid rgba(105, 112, 119, 0.16);
                    border-radius: rem(4);
                    object-fit: contain;
                }
            }

            .product-info-text {
                display: flex;
                flex-direction: column;
                padding-left: rem(10);

                .product-title {
                    font-size: rem(16);
                    line-height: rem(22);
                    color: rgba(var(--interactive-panel-text-color-rgb));
                }

                .product-price {
                    color: rgba(12, 12, 14, 0.56);
                    font-size: rem(14);
                    line-height: rem(20);
                    color: rgba(var(--interactive-panel-text-color-rgb));
                }
            }
        }

        .product-chevron {
            flex-shrink: 0;

            svg {
                width: 100%;
                width: rem(9);
                height: rem(16);
                color: rgba(var(--interactive-panel-text-color-rgb));
            }
        }

        .out-of-stock-tag {
            width: max-content;
            padding: rem(2) rem(10) rem(4) rem(10);
            height: rem(28);
            background-color: rgb(232, 230, 230);
            display: flex;
            flex-flow: row nowrap;
            justify-content: center;
            align-items: center;
            text-align: center;
            border-radius: rem(10);
            position: relative;

            p {
                font-size: rem(14);
                padding: 0;
            }
        }
    }
}
