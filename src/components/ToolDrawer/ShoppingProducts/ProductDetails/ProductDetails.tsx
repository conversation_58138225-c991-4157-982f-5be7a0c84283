import { useCallback, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "../../../../store/reducers";
import { useShopping } from "../../../../hooks/useShopping";
import { QuantitySelector } from "components/inputs/QuantitySelector";
import { ButtonGroup } from "components/Buttons/ButtonGroup";
import NoImageIcon from "assets/icons/no-image-available.png";
import { ProductPrice } from "../ProductPrice";
import { useEventTracking } from "hooks/useEventTracking";
import { useDispatch } from "react-redux";
import { addNotification } from "store/notification/slice";
import { NotificationType } from "store/notification/types";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import { Image } from "components/Image";
const cx = classNames.bind(styles);

export const ProductDetailsModal = ({
    broadcastId
}: {
    broadcastId?: string | null | undefined;
}) => {
    const dispatch = useDispatch();
    const { selectedProduct } = useSelector((s: RootState) => s.shopping);
    const {
        onOptionChange,
        availableOptions,
        availableQty,
        answers,
        response,
        addToCart
    } = useShopping(broadcastId);
    const [qty, setQty] = useState<number>(1);

  const { trackEvent } = useEventTracking();

    const trackAddToCart = () => {
        trackEvent(
            "Item Added To Cart",
            {
                productTitle: selectedProduct?.title,
                productOptions: response?.finalVariant,
                productQuantity: qty
            },
            false,
            true
        );
    };

    const handleQuantityReduced = useCallback(() => {
        const qtySelectorDiv = document.querySelector(
            `.${cx("product-details-quantity")}`
        );
        qtySelectorDiv?.scrollIntoView({ behavior: "smooth", block: "start" });
        dispatch(
            addNotification({
                class: "shopping",
                message: "alerts:cart-quantity-reduced",
                type: NotificationType.Info
            })
        );
    }, [dispatch]);

    return (
        <div className={cx("product-details-wrapper")}>
            {/* Header */}
            <div className={cx("product-details-header")}>
                {/* Image */}
                <div className={cx("product-details-image")}>
                    <div className={cx("product-image")}>
                        {response?.finalVariant?.image?.url ? (
                            <Image
                                className={cx("thumbnail")}
                                src={response?.finalVariant?.image?.url}
                                alt={""}
                            />
                        ) : (
                            <Image
                                className={cx("thumbnail")}
                                src={NoImageIcon}
                                alt=""
                            />
                        )}
                    </div>
                </div>

                <div className={cx("product-details-info")}>
                    {/* Title */}
                    <div className={cx("product-details-title")}>
                        <h3>{selectedProduct?.title}</h3>
                    </div>

                    {/* Price */}
                    <div className={cx("product-details-price")}>
                        <ProductPrice
                            amount={Number(
                                response?.finalVariant?.priceV2?.amount || 0
                            )}
                            currencyCode={
                                response?.finalVariant?.priceV2?.currencyCode ||
                                "USD"
                            }
                        />
                    </div>
                </div>
            </div>

            {/* Variants */}
            {availableOptions.length > 0 && (
                <div className={cx("product-details-variants")}>
                    {/* Variant Section */}
                    <div
                        className={cx(
                            "product-details-variant-section-wrapper"
                        )}
                    >
                        {response?.options.map((opt, i) => {
                            return (
                                <div
                                    key={`divKey-${i}`}
                                    className={cx(
                                        "product-details-variant-section"
                                    )}
                                >
                                    <div
                                        className={cx(
                                            "product-details-variant-section-label"
                                        )}
                                    >
                                        <label>{opt.name} </label>
                                    </div>
                                    <div
                                        className={cx(
                                            "product-details-variant-section-options-wrapper"
                                        )}
                                    >
                                        <ButtonGroup
                                            buttons={opt.values}
                                            onClick={(value: string) =>
                                                onOptionChange(value, opt.name)
                                            }
                                            activeValue={answers[i].value}
                                        />
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                </div>
            )}

            {!response?.returnMsg && (
                <div className={cx("product-details-manage")}>
                    <div className={cx("product-details-quantity")}>
                        <QuantitySelector
                            maxQuantity={availableQty}
                            quantity={qty}
                            setQuantity={setQty}
                            onQuantityReduced={handleQuantityReduced}
                        />
                    </div>
                    <div className={cx("product-actions-wrapper")}>
                        <button
                            className={cx("btn", "cart-action")}
                            onClick={() => {
                                addToCart({
                                    finalVariant: response?.finalVariant,
                                    qty: qty,
                                    title: selectedProduct?.title
                                });
                                trackAddToCart();
                            }}
                        >
                            Add To Cart
                        </button>
                    </div>
                </div>
            )}

            {response?.returnMsg && <div>{response?.returnMsg}</div>}
        </div>
    );
};
