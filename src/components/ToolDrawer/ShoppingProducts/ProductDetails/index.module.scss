.product-details-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: left;
    padding: 0 rem(16);
    gap: rem(24);

    .product-details-header {
        display: flex;
        gap: rem(16);
        align-items: center;

        .product-details-image {
            .product-image {
                .thumbnail {
                    height: rem(100);
                    width: rem(100);
                    border: rem(1) solid rgba(121, 121, 121, 0.16);
                    border-radius: rem(8);
                    object-fit: contain;

                    @include mobile {
                        height: rem(88);
                        width: rem(88);
                    }
                }
            }
        }

        .product-details-info {
            display: flex;
            flex-direction: column;
            gap: rem(8);

            .product-details-title {
                h3 {
                    font-weight: 400;
                    font-size: rem(20);
                    line-height: rem(26);
                    margin: 0;
                    color: rgba(var(--interactive-panel-text-color-rgb));

                    @include mobile {
                        font-size: rem(18);
                        line-height: rem(24);
                    }
                }
            }

            .product-details-price {
                font-weight: 400;
                font-size: rem(16);
                line-height: rem(22);
                color: rgba(var(--interactive-panel-text-color-rgb), 0.8);

                @include mobile {
                    font-size: rem(14);
                    line-height: rem(20);
                }
            }
        }
    }

    .product-details-variants {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;

        .product-details-variant-section-wrapper {
            display: flex;
            flex-direction: column;
            justify-content: space-evenly;
            width: 100%;
            gap: rem(24);

            .product-details-variant-section {
                display: flex;
                flex-direction: column;

                .product-details-variant-section-label {
                    font-weight: 400;
                    font-size: rem(14);
                    line-height: rem(20);
                    text-transform: uppercase;
                    margin-bottom: rem(8);
                }

                .product-details-variant-section-options-wrapper {
                }
            }
        }
    }

    .product-details-manage {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .product-actions-wrapper {
            display: flex;
            flex-direction: row;
            justify-content: flex-end;

            .cart-action {
                background-color: var(--brand-color-primary);
                color: var(--brand-color-secondary);

                &:hover {
                    background-color: var(--brand-color-primary-dark);
                }
            }
        }

        .product-quantity-selector-wrapper {
            display: flex;
            flex-flow: column nowrap;
            justify-content: space-evenly;
            align-items: flex-start;
            gap: 0.25rem;
            margin-bottom: 0.5rem;
        }
    }
}
