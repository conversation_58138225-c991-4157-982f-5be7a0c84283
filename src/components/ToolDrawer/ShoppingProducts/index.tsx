import { useEffect } from "react";
import { ShoppingViews } from "../../../store/shopping/types";
import { AppDispatch } from "store/store";
import { useDispatch, useSelector } from "react-redux";
import {
    changeScreen,
    updateProducts,
    setShoppingLoading
} from "../../../store/shopping/slice";
import { ProductDetailsModal } from "./ProductDetails/ProductDetails";
import { RootState } from "../../../store/reducers";
import { ProductListModal } from "./ProductList/index";
import { NotificationContainer } from "components/notification/NotificationContainer";
import { liveCommentsReceiver } from "api/liveCommentsReceiver/lcr-client";
import { useTwilioEventHandler } from "hooks/useTwilioEventHandler";
import Loading from "components/loading/Loading";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import { useGatedContent } from "../../../hooks/useGatedContent";
const cx = classNames.bind(styles);

const ShoppingProducts = ({
    setChildNavState
}: {
    setChildNavState: (...args: any) => any;
}) => {
    const dispatch = useDispatch<AppDispatch>();
    const { currentCollectionVideo } = useSelector(
        (s: RootState) => s.videoSession
    );
    const { currentView, loading } = useSelector((s: RootState) => s.shopping);
    const { playlistBroadcastIsEntitled, playlistBroadcastIsGated } =
        useGatedContent();

    useTwilioEventHandler(currentCollectionVideo?.broadcast);

    useEffect(() => {
        async function fetch() {
            const broadcastId = currentCollectionVideo?.broadcast?.details?.id;
            const shoppingEntitled =
                currentCollectionVideo?.broadcast?.details?.enableLiveShopping;
            if (broadcastId && shoppingEntitled) {
                if (!playlistBroadcastIsEntitled && playlistBroadcastIsGated) {
                    return;
                }
                try {
                    dispatch(setShoppingLoading(true));
                    const res = await liveCommentsReceiver.getBroadcastProducts(
                        { broadcastId: broadcastId }
                    );
                    dispatch(updateProducts(res));
                    dispatch(setShoppingLoading(false));
                } catch (e) {
                    dispatch(setShoppingLoading(false));
                    throw e;
                }
            }
        }
        fetch();
    }, [
        currentCollectionVideo,
        dispatch,
        playlistBroadcastIsEntitled,
        playlistBroadcastIsGated
    ]);

    const {
        shoppingNotifications: { notifications }
    } = useSelector((s: RootState) => s.notifications);

    useEffect(() => {
        if (currentView !== ShoppingViews.Products) {
            setChildNavState({
                label: "Back to Products",
                callback: () => {
                    dispatch(changeScreen(ShoppingViews.Products));
                }
            });
        } else {
            setChildNavState(undefined);
        }
    }, [currentView, setChildNavState, dispatch]);

    if (
        currentCollectionVideo == null ||
        !currentCollectionVideo?.broadcast?.details?.enableLiveShopping
    )
        return null;
    return (
        <div className={cx("shopping-container")}>
            {loading && <Loading variant="no-radius" />}
            <div className={cx("shopping-wrapper")}>
                <NotificationContainer
                    visible={notifications.length > 0}
                    notifications={notifications}
                />
                <div className={cx("shopping-body")}>
                    {currentView === ShoppingViews.ProductDetails && (
                        <ProductDetailsModal
                            broadcastId={
                                currentCollectionVideo?.broadcast?.details?.id
                            }
                        />
                    )}
                    {currentView === ShoppingViews.Products && (
                        <ProductListModal />
                    )}
                </div>
            </div>
        </div>
    );
};

export default ShoppingProducts;
