.tool-drawer {
    background-color: rgba(var(--interactive-panel-background-color-rgb));
    width: 0;
    transition: 0.3s width ease-in-out;
    overflow: hidden;
    position: absolute;
    top: rem(16);
    right: rem(16);
    bottom: rem(16);
    left: auto;
    //inset: rem(16) rem(16) rem(16) auto;
    border-top-right-radius: rem(16);
    border-top: rem(1) solid
        rgba(var(--interactive-panel-background-contrast-color-rgb), 0.1);

    @include mobile-landscape {
        top: 0;
        right: 0;
        bottom: 0;
        left: auto;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }

    @include mobile-portrait {
        top: auto;
        right: 0;
        bottom: 0;
        left: 0;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        width: 100%;
        height: 0;
        transition: 0.3s height ease-in-out;
    }

    .tool-drawer-buttons {
        gap: rem(24);
        display: flex;
        flex-direction: column;
        width: 90%;
        margin: rem(22) rem(22) rem(8);

        .unlock-option-link {
            color: var(--brand-color-primary);
            font-weight: 700;
            font-size: 14px;
            line-height: 20px;
            text-align: center;
        }
    }

    .tool-child-nav {
        width: 100%;
        min-height: 0;
        border-bottom: rem(1) solid
            rgba(var(--interactive-panel-background-contrast-color-rgb), 0.1);
        background-color: rgba(var(--interactive-panel-background-color-rgb));
        padding: rem(16) rem(24);
        display: flex;
        align-items: center;
        overflow: hidden;

        button {
            display: flex;
            align-items: center;
            gap: rem(16);
            font-weight: 600;
            font-size: rem(14);
            line-height: rem(20);
            padding: 0;
            border: none;
            cursor: pointer;
            background-color: transparent;
            transition: 0.25s gap ease-in-out;
            color: rgba(var(--interactive-panel-text-color-rgb));

            svg {
                width: rem(10);
                height: rem(16);

                * {
                    fill: rgba(var(--interactive-panel-text-color-rgb));
                }
            }

            &:hover {
                gap: rem(20);
            }
        }
    }

    .tools-available {
        display: flex;
        min-width: rem(480);
        height: rem(68);
        min-height: 0;
        border-bottom: rem(1) solid
            rgba(var(--interactive-panel-background-contrast-color-rgb), 0.1);
        overflow-x: auto;
        padding-bottom: rem(1);

        @include desktop {
            &::-webkit-scrollbar {
                height: rem(10);
                padding-top: rem(10);
                border-top: rem(1) solid #e3e3e3;
            }

            &::-webkit-scrollbar-thumb {
                background: #dde1e6;
                border: 0.125rem solid #dde1e6;
                border-radius: 10rem;
                height: rem(5);
            }
        }

        @include mobile-portrait {
            min-width: 0;
        }

        .tool-item-tab {
            flex-basis: 100%;
            background-color: rgba(
                var(--interactive-panel-background-color-rgb)
            );
            border: none;
            padding: rem(16);
            cursor: pointer;
            position: relative;
            color: #000;

            display: flex;
            flex-flow: column nowrap;
            justify-content: space-evenly;
            align-items: center;

            &.mobile-only {
                display: none;

                @include mobile {
                    display: block;
                }
            }

            &:after {
                content: "";
                display: block;
                position: absolute;
                bottom: rem(-1);
                left: 50%;
                width: rem(15);
                height: rem(3);
                transform: translateX(-50%);
                opacity: 0;
                transition: 0.25s opacity ease-in-out, 0.25s width ease-in-out;
                background-color: rgba(var(--interactive-panel-text-color-rgb));
            }

            svg {
                color: rgba(var(--interactive-panel-text-color-rgb));
                width: rem(21);
                height: rem(21);
                flex-shrink: 0;

                * {
                    fill: rgba(var(--interactive-panel-text-color-rgb));
                }
            }

            small {
                color: rgba(var(--interactive-panel-text-color-rgb));
                margin-top: 0.5rem;
                text-transform: uppercase;
            }

            svg,
            small {
                opacity: 0.47;
                transition: 0.25s opacity ease-in-out;
            }

            &:hover {
                svg,
                small {
                    opacity: 0.6;
                }

                &:after {
                    width: rem(25);
                    opacity: 0.6;
                }
            }

            &.active {
                svg,
                small {
                    opacity: 1;
                }

                small {
                    font-weight: 600;
                }

                &:after {
                    width: rem(32);
                    opacity: 1;
                }
            }

            .tool-item-tab-badge {
                background-color: rgba(var(--interactive-panel-text-color-rgb));
                color: rgba(var(--interactive-panel-background-color-rgb));
                position: absolute;
                width: rem(16);
                height: rem(16);
                top: rem(10);
                left: 50%;
                transform: translateX(rem(1));
                z-index: 2;
                font-weight: 700;
                font-size: rem(10);
                line-height: rem(16);
                border-radius: 50%;
            }
        }
    }

    $tab-height: rem(68);
    $fixed-child-height: rem(72);
    $primary-button-height: rem(72);
    $primary-and-secondary-button-height: rem(118);

    .tool-container {
        position: relative;
        height: calc(100% - $tab-height);
        min-width: rem(480);
        min-height: 0;

        @include mobile-portrait {
            min-width: 0;
        }

        .tool-item {
            overflow-y: auto;
            width: 100%;
            height: 100%;
            display: none;

            &.active {
                display: block;
            }

            &.mobile-only {
                display: none;

                @include mobile {
                    display: block;
                }
            }
        }
    }

    &.has-child-fixed {
        .tool-container {
            height: calc(100% - $tab-height - $fixed-child-height);
        }
    }
    &.has-primary-button {
        .tool-container {
            height: calc(100% - $tab-height - $primary-button-height);
        }
    }
    &.has-secondary-button {
        .tool-container {
            height: calc(
                100% - $tab-height - $primary-and-secondary-button-height
            );
        }
    }

    &.has-child-fixed.has-primary-button {
        .tool-container {
            height: calc(
                100% - $tab-height - $primary-button-height -
                    $fixed-child-height
            );
        }
    }
    &.has-child-fixed.has-secondary-button {
        .tool-container {
            height: calc(
                100% - $tab-height - $primary-and-secondary-button-height -
                    $fixed-child-height
            );
        }
    }

    .tool-child-fixed {
        width: 100%;
        min-height: 0;
        position: sticky;
        bottom: 0;
    }
}

/* Styles for specific player states */
:global(.is-embed:not(.is-expanded)) {
    .tool-drawer {
        top: 0;
        right: 0;
        bottom: 0;
        left: auto;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;

        &.active {
            display: none;
        }
    }

    @include mobile-in-global {
        .tool-drawer {
            &.active {
                width: 0;
            }
        }
    }
    @include mobile-portrait-in-global {
        .tool-drawer {
            &.active {
                height: 0;
            }
        }
    }
}

:global(.tool-active) {
    .tool-drawer {
        width: rem(480);
        border-right: rem(1) solid
            rgba(var(--interactive-panel-background-contrast-color-rgb), 0.1);
        border-left: rem(1) solid
            rgba(var(--interactive-panel-background-contrast-color-rgb), 0.1);
    }

    @include mobile-portrait-in-global {
        .tool-drawer {
            width: 100%;
            height: calc(100% - 56.25vw);
            max-height: calc(100% - 56.25vw);

            &.is-vertical {
                height: 50%;
                max-height: 50%;
            }
        }
    }
}
