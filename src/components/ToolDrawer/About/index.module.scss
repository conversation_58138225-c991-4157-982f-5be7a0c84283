.video-details-container {
    padding: rem(12) rem(22) rem(12) rem(22);
    min-height: 100%;
    position: relative;

    .video-details-grouping {
        &:first-child {
            margin-top: 0;
        }

        &.only-player-pricing {
            position: absolute;
            bottom: rem(40);
            left: rem(26);
            right: rem(26);
        }

        hr {
            border-radius: rem(12);
            border: none;
            border-bottom: solid rem(1)
                rgba(
                    var(--interactive-panel-background-contrast-color-rgb),
                    0.1
                );
            margin: 0;
        }
    }

    .video-details-top {
        text-align: left;
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        margin-bottom: rem(16);
        gap: rem(15);

        .video-details-main {
            color: rgba(var(--interactive-panel-text-color-rgb));
            h3 {
                margin: 0;
                font-size: rem(16);
                @include wrapTextOverflow();
            }

            .video-details-meta {
                font-size: rem(14);
                line-height: rem(20);
                color: rgba(var(--interactive-panel-text-color-rgb), 0.56);
            }
        }
    }

    .purchase-alternative {
        margin: rem(18) 0;
        height: rem(24);
        position: relative;
        display: flex;
        justify-content: center;

        &:before {
            content: "";
            display: block;
            position: absolute;
            z-index: 1;
            width: 100%;
            height: rem(1);
            background-color: rgba(
                var(--interactive-panel-background-contrast-color-rgb),
                0.1
            );
            top: 50%;
            transform: translateY(-50%);
        }

        span {
            position: relative;
            z-index: 2;
            color: #666666;
            font-size: rem(18);
            line-height: rem(24);
            background-color: rgba(
                var(--interactive-panel-background-color-rgb)
            );
            padding: 0 rem(13);
        }
    }

    .video-details-purchase-info {
        display: flex;
        justify-content: center;
        align-items: center;
        color: rgba(18, 22, 25, 0.64);
        gap: rem(5.5);
        font-size: rem(14);
        line-height: rem(20);
        border-bottom: rem(0.5) solid #dde1e6;
        padding: rem(16) 0 rem(16) 0;
    }

    .video-details-description {
        margin: rem(16) 0 0 0;
        white-space: pre-wrap;
        font-size: rem(14);
        line-height: rem(20);
    }

    .links-button-container {
        color: unset;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        background: #ffffff;
        border: none;
        margin-top: rem(16);
        // border-bottom: rem(1) solid rgba(121, 121, 121, 0.16);
        gap: rem(12);
        cursor: pointer;

        &:hover {
            .links-button-icon-container {
                background-color: var(--brand-color-primary-darker);
            }

            .links-button-title {
                color: #000;
            }

            .links-button-arrow {
                color: #000;
            }
        }

        .links-button-title {
            font-weight: 400;
            display: flex;
            align-items: flex-start;
            flex-basis: 80%;
            flex-grow: 2;
            line-height: rem(22);
            color: rgba(12, 12, 14, 0.56);
            transition: color 0.2s ease-in-out;
        }

        .links-button-icon-container {
            background-color: var(--brand-color-primary);
            border-radius: 50%;
            padding: rem(15);
            transition: background-color 0.2s ease-in-out;
        }

        .links-button-icon {
            color: rgb(var(--primary-contrast-color-rgb));
        }

        .links-button-arrow {
            color: rgba(12, 12, 14, 0.56);
            transition: color 0.2s ease-in-out;
        }
    }
}

.empty-wrapper {
    border: rem(1) dashed
        rgba(var(--interactive-panel-background-contrast-color-rgb), 0.1);
    padding: rem(12);
    margin: rem(24);
    width: calc(100% - rem(48));

    .empty {
        padding: rem(25);
        color: rgb(var(--interactive-panel-text-color-rgb));
        background: rgb(
            var(--interactive-panel-background-contrast-color-rgb),
            0.1
        );
        box-shadow: 0 rem(1) rem(4) rgba(0, 0, 0, 0.08);
        border-radius: rem(8);
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;

        svg {
            width: rem(34);
            height: rem(36);
            margin-bottom: rem(11);
        }

        button {
            margin-top: rem(24);
            border: none;
            background: none;
            font-weight: 600;
            font-size: rem(14);
            line-height: rem(20);
            text-decoration: underline;
            color: #0c0c0e;
            cursor: pointer;

            transition: 0.25s color ease-in-out;

            &:hover {
                color: lighten(#0c0c0e, 20);
            }
        }
    }
}
