.container {
    color: rgb(var(--interactive-panel-text-color-rgb));

    &.card {
        background-color: rgb(
            var(--interactive-panel-background-contrast-color-rgb),
            0.1
        ) !important;
    }

    .header {
        display: flex;
        flex-direction: row;
        align-items: center;
        height: rem(62);
        margin-bottom: rem(12);

        .brand-logo {
            margin-right: rem(16);
        }

        .info {
            @include wrapTextOverflow();

            h3 {
                margin: 0;
                font-size: rem(16);
            }

            .subtitle {
                font-size: rem(14);
                line-height: rem(20);
                color: rgba(var(--interactive-panel-text-color-rgb) 0.56);
            }
        }
    }

    hr {
        display: none;
        border: solid rem(1)
            rgba(var(--interactive-panel-background-contrast-color-rgb), 0.1);
        border-radius: rem(12);
        margin: rem(18) 0;
    }
    .body {
        .description {
            font-size: rem(14);
            white-space: pre-wrap;
            font-size: rem(14);
            line-height: rem(20);
        }
    }
}

.broadcast-slider {
    position: relative;
    margin-bottom: rem(16);

    .broadcast-slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: 11;
        background: none;
        border: none;
        width: rem(34);
        height: rem(34);
        background-color: rgba(33, 37, 41, 0.3);
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        &.disabled {
            opacity: 0.5;
            cursor: default;
        }

        > svg {
            color: #fff;
        }

        &.broadcast-slider-nav-prev {
            left: rem(4);
            transform: rotateY(180deg) translateY(-50%);
        }

        &.broadcast-slider-nav-next {
            right: rem(4);
        }
    }

    .broadcast-slider-swiper {
        position: relative;
        z-index: 10;
        padding: rem(3);

        .broadcast-slider-slide {
            @include sixteenByNine();
            border-radius: rem(6);
            overflow: hidden;
            cursor: pointer;
            transition: 0.1s ease-in-out border;
            border-radius: rem(8);

            &:hover {
                outline: rem(3) solid rgba(var(--brand-color-primary-rgb), 0.5);
            }

            &.active {
                outline: rem(3) solid var(--brand-color-primary);
            }

            .broadcast-slider-slide-image {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: #0c0c0e;
                border-radius: rem(6);
                object-fit: contain;
            }
        }
    }
}

.is-vertical {
    .broadcast-slider {
        .broadcast-slider-swiper {
            .broadcast-slider-slide {
                @include nineBySixteen();
            }
        }
    }
}

.card {
    background: #f2f2f2;
    border-radius: rem(12);
    padding: rem(16);
    margin-top: rem(16);

    .info {
        .subtitle {
            color: rgba(0, 0, 0, 0.56);
        }
    }

    hr {
        display: block;
    }
}
