import { useMemo } from "react";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import timeSince from "helpers/timeFrom";
import { TruncatedElement } from "components/Text/TruncatedElement";
import { WebLinkGroup } from "components/Groups/WebLinkGroup";
import { useGatedContent } from "hooks/useGatedContent";
import useCollectionWithVideos from "hooks/useCollectionWithVideos";

const cx = classNames.bind(styles);

export interface VideoPlayerInfoPanelProps {
    variant?: "default" | "card";
    promptSubscribe?: boolean;
    setLink: (link: string) => void;
}

export const VideoPlayerInfoPanel = ({
    variant = "default",
    setLink,
    promptSubscribe = false
}: VideoPlayerInfoPanelProps) => {
    const { collection, allVideos } = useCollectionWithVideos();

    const { playerIsEntitled } = useGatedContent();

    const playlistLength = useMemo(() => {
        return allVideos?.length ?? 0;
    }, [allVideos]);

    const lastUpdated = useMemo(() => {
        if (!allVideos?.length) return null;
        return timeSince(
            allVideos?.reduce((prev, current) => {
                return (prev?.broadcast?.details?.createdAt ??
                    new Date("01/01/1970")) >
                    (current?.broadcast?.details?.createdAt ??
                        new Date("01/01/1970"))
                    ? prev
                    : current;
            })?.broadcast?.details?.createdAt
        );
    }, [allVideos]);

    return (
        <div className={cx("container", variant)}>
            <div className={styles["header"]}>
                <div className={styles["info"]}>
                    <div>
                        {promptSubscribe ? (
                            <h3>{`Subscribe to "${
                                collection?.details?.name ?? "Example Player"
                            }"`}</h3>
                        ) : (
                            <h3>{collection?.details?.name}</h3>
                        )}
                        <div className={styles["subtitle"]}>
                            {`${playlistLength} Video${
                                playlistLength === 1 ? "" : "s"
                            } • Last updated ${lastUpdated}`}
                        </div>
                    </div>
                </div>
            </div>

            {collection?.details?.description && <hr />}

            <div className={styles["body"]}>
                <TruncatedElement className={styles["description"]}>
                    {collection?.details?.description}
                </TruncatedElement>
                <WebLinkGroup
                    level="player"
                    setLink={setLink}
                    purchased={playerIsEntitled}
                />
            </div>
        </div>
    );
};
