import { useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "store/reducers";
import timeSince from "helpers/timeFrom";
import { Linkify } from "components/Text/Linkify";
import NoVideoIcon from "assets/icons/no-video.svg?react";
import timeUntil from "helpers/timeUntil";
import { useGatedContent } from "hooks/useGatedContent";
import "swiper/css";
import "swiper/css/navigation";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import { TruncatedElement } from "components/Text/TruncatedElement";
import { WebLinkGroup } from "components/Groups/WebLinkGroup";
import { QRCodeModal } from "components/Modals/QRCodeModal";
import { isInFuture, isInPast, formatVideoDuration } from "helpers/time";
import useCollectionWithVideos from "hooks/useCollectionWithVideos";
import { VideoPlayerInfoPanel } from "./VideoPlayerInfoPanel";
const cx = classNames.bind(styles);

export interface AboutToolProps {}

const AboutTool = () => {
    const { collection } = useCollectionWithVideos();

    const { currentCollectionVideo, isVoD, hasSetDefault } = useSelector(
        (s: RootState) => s.videoSession
    );

    const { playlistBroadcastIsEntitled } = useGatedContent();

    const [link, setLink] = useState<string | null>(null);

    const currentCollectionVideoDuration = useMemo(() => {
        if (isVoD) {
            return formatVideoDuration(
                currentCollectionVideo?.broadcast?.videos?.[0]?.details
                    ?.duration ?? 0
            );
        }
        return formatVideoDuration(
            currentCollectionVideo?.broadcast?.videos?.[0]?.details?.duration ??
                0
        );
    }, [currentCollectionVideo, isVoD]);

    const currentCollectionVideoMeta = useMemo(() => {
        if (
            currentCollectionVideo?.broadcast?.details?.broadcastStatus ===
            "Active"
        )
            return "LIVE";
        if (
            isInFuture(currentCollectionVideo?.broadcast?.details?.endedAt) &&
            isInPast(currentCollectionVideo?.broadcast?.details?.startsAt)
        )
            return "Premiering";
        if (
            currentCollectionVideo?.broadcast?.details?.endedAt &&
            !isInFuture(currentCollectionVideo?.broadcast?.details?.endedAt)
        )
            return `${timeSince(
                currentCollectionVideo?.broadcast?.details?.endedAt
            )} • ${currentCollectionVideoDuration}`;
        return `${timeUntil(
            currentCollectionVideo?.broadcast?.details?.startsAt
        )}`;
    }, [currentCollectionVideo, currentCollectionVideoDuration]);

    const isVertical = useMemo(
        () => collection?.details?.aspectRatio === "NineBySixteen",
        [collection?.details?.aspectRatio]
    );

    return (
        <>
            {currentCollectionVideo && (
                <div
                    className={cx("video-details-container", {
                        "is-vertical": isVertical
                    })}
                >
                    <QRCodeModal onClose={() => setLink(null)} link={link} />

                    <div className={cx("video-details-grouping")}>
                        <div className={cx("video-details-top")}>
                            <div className={cx("video-details-main")}>
                                <h3>
                                    {
                                        currentCollectionVideo?.broadcast
                                            ?.details?.title
                                    }
                                </h3>
                                <div className={cx("video-details-meta")}>
                                    {currentCollectionVideoMeta}
                                </div>
                            </div>
                        </div>

                        {currentCollectionVideo?.broadcast?.details
                            ?.description &&
                            currentCollectionVideo?.broadcast?.details
                                ?.description !== "" && (
                                <>
                                    <hr />
                                    <TruncatedElement
                                        key={
                                            currentCollectionVideo?.details?.id
                                        }
                                        className={cx(
                                            "video-details-description"
                                        )}
                                    >
                                        <Linkify
                                            input={
                                                currentCollectionVideo
                                                    ?.broadcast?.details
                                                    ?.description
                                            }
                                        />
                                    </TruncatedElement>
                                </>
                            )}

                        <WebLinkGroup
                            level="broadcast"
                            setLink={setLink}
                            purchased={playlistBroadcastIsEntitled}
                        />
                    </div>

                    <VideoPlayerInfoPanel setLink={setLink} variant="card" />
                </div>
            )}

            {hasSetDefault && !currentCollectionVideo && (
                <div className={cx("empty-wrapper")}>
                    <div className={cx("empty")}>
                        <NoVideoIcon />
                        {
                            "When we schedule a video, you'll find info here. Check back soon."
                        }
                    </div>
                </div>
            )}
        </>
    );
};

export default AboutTool;
