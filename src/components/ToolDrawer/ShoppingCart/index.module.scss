.cart-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 rem(24);
    gap: rem(24);
    background-color: rgba(var(--interactive-panel-background-color-rgb));
    color: rgba(var(--interactive-panel-text-color-rgb));

    .cart-empty-wrapper {
        border: rem(1) dashed
            rgba(var(--interactive-panel-background-contrast-color-rgb), 0.1);
        padding: rem(12);
        margin-top: rem(24);
        width: 100%;

        .cart-empty {
            padding: rem(25);
            background: rgba(
                var(--interactive-panel-background-contrast-color-rgb),
                0.1
            );
            color: rgba(var(--interactive-panel-text-color-rgb), 0.6);
            box-shadow: 0 rem(1) rem(4) rgba(0, 0, 0, 0.08);
            border-radius: rem(8);
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;

            svg {
                width: rem(34);
                height: rem(36);
                margin-bottom: rem(11);
                color: rgba(var(--interactive-panel-text-color-rgb), 0.6);
            }

            button {
                margin-top: rem(24);
                border: none;
                background: none;
                font-weight: 600;
                font-size: rem(14);
                line-height: rem(20);
                text-decoration: underline;
                color: rgba(var(--brand-color-primary-rgb)) !important;
                cursor: pointer;

                transition: 0.25s color ease-in-out;

                &:hover {
                    color: lighten(#0c0c0e, 20);
                }
            }
        }
    }

    .cart-product-wrapper {
        width: 100%;
        border-top: rem(1) solid rgba(121, 121, 121, 0.24);

        &:first-child {
            border: none;
        }

        .product-card {
            display: flex;
            flex-direction: row;
            align-content: center;
            justify-content: space-between;
            align-items: center;
            gap: rem(15);
            width: 100%;
            padding: rem(16) 0;
            position: relative; //

            img {
                object-fit: contain;
            }

            .product-info {
                display: flex;
                flex-direction: row;
                justify-content: flex-start;
                align-items: center;

                .product-image {
                    img {
                        height: rem(56);
                        width: rem(56);
                        border: rem(1) solid rgba(105, 112, 119, 0.16);
                        border-radius: rem(4);
                    }
                }

                .product-info-text {
                    display: flex;
                    flex-direction: column;
                    padding-left: rem(10);

                    .product-title {
                        font-size: rem(16);
                        line-height: rem(22);
                    }

                    .product-desc {
                        font-size: rem(14);
                        line-height: rem(20);
                        color: rgba(12, 12, 14, 0.56);
                    }
                }
            }

            .cart-product-remove {
                flex-shrink: 0;
                color: rgba(var(--interactive-panel-text-color-rgb), 0.6);
                transition: 0.25s color ease-in-out;
                cursor: pointer;

                svg {
                    width: rem(24);
                    height: rem(24);
                }

                &:hover {
                    color: rgba(var(--interactive-panel-text-color-rgb), 1);
                }
            }
        }

        .cart-product-actions {
            display: flex;
            flex-direction: row;
            align-items: center;

            justify-content: space-between;
            border-top: rem(0.5) solid rgba(121, 121, 121, 0.16);
            padding: rem(16) 0;

            .cart-product-quantity {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
            }

            .product-price {
                font-weight: 590;
                font-size: rem(14);
                line-height: rem(20);
            }
        }
    }
}

.shopping-cart-management {
    width: 100%;
    background-color: #fff;
    z-index: 2;

    .cart-management-wrapper {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        background-color: rgba(var(--interactive-panel-background-color-rgb));
        padding: rem(16) rem(24);
        border-top: rem(0.5) solid rgba(121, 121, 121, 0.16);

        .cart-subtotal {
            font-weight: bolder;
            color: rgba(var(--interactive-panel-text-color-rgb));
        }

        .cart-actions {
            background-color: rgba(var(--brand-color-primary-rgb));
            color: var(--brand-color-secondary);

            &:disabled {
                background-color: rgba(
                    var(--brand-color-primary-rgb),
                    0.15
                ) !important;
                color: rgba(var(--brand-color-secondary-rgb), 0.4);
                cursor: no-drop;
            }

            &:not([disabled]) {
                background-color: var(--brand-color-primary);
                color: var(--brand-color-secondary);

                &:hover {
                    background-color: var(--brand-color-primary-dark);
                }
            }

            .cart-icon {
                display: flex;
                flex-direction: row;
                justify-content: center;
                align-items: center;
            }
        }

        .action-with-icon {
            padding: rem(8) rem(24);
        }
    }
}
