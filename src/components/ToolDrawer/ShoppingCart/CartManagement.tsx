import { useSelector } from "react-redux";
import { RootState } from "../../../store/reducers";
import { useShopping } from "../../../hooks/useShopping";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
const cx = classNames.bind(styles);

export const CartManagement = ({
    broadcastId
}: {
    broadcastId: string | null | undefined;
}) => {
    const { cart } = useSelector((s: RootState) => s.shopping);
    const { checkoutConfirmation, subTotal } = useShopping(broadcastId);

    const firstCurrencyCode =
        cart.length > 0 ? cart[0].finalVariant?.priceV2.currencyCode || "" : "";
    const isUSD = cart.length > 0 && firstCurrencyCode === "USD" ? true : false;

    return (
        <>
            <div className={cx("cart-management-wrapper")}>
                <div className={cx("cart-subtotal")}>
                    {"Subtotal: " +
                        (cart.length > 0
                            ? isUSD
                                ? "$" + subTotal
                                : subTotal + " " + firstCurrencyCode
                            : "-")}
                </div>

                <button
                    className={cx("btn", "cart-actions")}
                    onClick={() => checkoutConfirmation()}
                    disabled={cart.length === 0}
                >
                    Checkout
                </button>
            </div>
        </>
    );
};
