import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store/reducers";
import {
    updateCartItemQty,
    removeCartItem,
    clearCart
} from "../../../store/shopping/slice";
import NoImageIcon from "assets/icons/no-image-available.png";
import TrashIcon from "assets/icons/trash.svg?react";
import CartPlusIcon from "assets/icons/cart-plus.svg?react";
import { QuantitySelector } from "components/inputs/QuantitySelector";
import { ProductPrice } from "../ShoppingProducts/ProductPrice";
import { useEventTracking } from "hooks/useEventTracking";
import { FinalVariant } from "store/shopping/types";
import { CartManagement } from "./CartManagement";
import { setActiveTool } from "store/VideoSession/slice";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import { Image } from "components/Image";
const cx = classNames.bind(styles);

const ShoppingCart = ({
    setChildActionState
}: {
    setChildActionState: (...args: any) => any;
}) => {
    const dispatch = useDispatch();
    const { cart } = useSelector((s: RootState) => s.shopping);
  const { trackEvent } = useEventTracking();
    const { activeTool, currentCollectionVideo } = useSelector(
        (s: RootState) => s.videoSession
    );

    const trackRemoveFromCart = (item: FinalVariant) => {
        trackEvent(
            "Item Removed From Cart",
            {
                productTitle: item.title,
                productOptions: item.finalVariant,
                productQuantity: item.qty
            },
            false,
            true
        );
    };

    // Clear cart on new broadcast
    useEffect(() => {
        dispatch(clearCart());
    }, [dispatch, currentCollectionVideo]);

    useEffect(() => {
        if (activeTool === "Cart") {
            setChildActionState({
                components: (
                    <div className={cx("shopping-cart-management")}>
                        <CartManagement
                            broadcastId={
                                currentCollectionVideo?.broadcast?.details?.id
                            }
                        />
                    </div>
                )
            });
        }
    }, [currentCollectionVideo, activeTool, setChildActionState]);

    return (
        <>
            <div className={cx("cart-wrapper")}>
                {cart.map((p) => {
                    return (
                        <div
                            className={cx("cart-product-wrapper")}
                            key={p.finalVariant?.id}
                        >
                            <div className={cx("product-card")}>
                                <div className={cx("product-info")}>
                                    <div className={cx("product-image")}>
                                        {p.finalVariant?.image?.url ? (
                                            <Image
                                                className={cx("thumbnail")}
                                                src={p.finalVariant?.image?.url}
                                                alt={p.finalVariant?.title}
                                            />
                                        ) : (
                                            <Image
                                                className={cx("thumbnail")}
                                                src={NoImageIcon}
                                                alt=""
                                            />
                                        )}
                                    </div>
                                    <div className={cx("product-info-text")}>
                                        <div className={cx("product-title")}>
                                            {p.title}
                                        </div>
                                        <div className={cx("product-desc")}>
                                            {p.finalVariant?.title !==
                                            "Default Title"
                                                ? p.finalVariant?.title
                                                : ""}
                                        </div>
                                    </div>
                                </div>
                                <div className={cx("cart-product-remove")}>
                                    <TrashIcon
                                        className={cx("trash-action")}
                                        onClick={() => {
                                            dispatch(removeCartItem(p));
                                            trackRemoveFromCart(p);
                                        }}
                                    />
                                </div>
                            </div>
                            <div className={cx("cart-product-actions")}>
                                <div className={cx("cart-product-quantity")}>
                                    <QuantitySelector
                                        maxQuantity={
                                            p.finalVariant?.quantityAvailable
                                        }
                                        quantity={p.qty}
                                        setQuantity={(qty) =>
                                            dispatch(
                                                updateCartItemQty({
                                                    id: p.finalVariant?.id,
                                                    qty: qty
                                                })
                                            )
                                        }
                                        onTrash={() => {
                                            dispatch(removeCartItem(p));
                                            trackRemoveFromCart(p);
                                        }}
                                    />
                                </div>
                                <div className={cx("product-price")}>
                                    <ProductPrice
                                        amount={Number(
                                            p?.finalVariant?.priceV2.amount
                                        )}
                                        currencyCode={
                                            p?.finalVariant?.priceV2
                                                ?.currencyCode || "USD"
                                        }
                                        quantity={p.qty}
                                    />
                                </div>
                            </div>
                        </div>
                    );
                })}
                {cart.length === 0 && (
                    <div key="empty" className={cx("cart-empty-wrapper")}>
                        <div className={cx("cart-empty")}>
                            <CartPlusIcon />
                            Looks like your cart is empty.
                            <button
                                onClick={() => {
                                    dispatch(setActiveTool("Products"));
                                }}
                            >
                                Shop Products
                            </button>
                        </div>
                    </div>
                )}
            </div>
        </>
    );
};

export default ShoppingCart;
