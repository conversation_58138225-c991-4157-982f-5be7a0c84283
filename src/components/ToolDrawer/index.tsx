import {
    ReactNode,
    useEffect,
    useMemo,
    useState,
    lazy,
    Suspense,
    useCallback
} from "react";
import { useSelector } from "react-redux";
import { RootState } from "store/reducers";
import InfoIcon from "assets/icons/info.svg?react";
import ProductTagIcon from "assets/icons/product-tag.svg?react";
import CartIcon from "assets/icons/cart.svg?react";
import VoDIcon from "assets/icons/vod.svg?react";
import CardIcon from "assets/icons/card.svg?react";
import ChevronLeftIcon from "assets/icons/chevron-left.svg?react";
import ChatIcon from "assets/icons/chat.svg?react";
import { setActiveTool } from "store/VideoSession/slice";
import { useDispatch } from "react-redux";
import { AppDispatch } from "store/store";
import { PlaylistList } from "components/VideoOnDemand/PlaylistList";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import { useGatedContent } from "hooks/useGatedContent";
// import { MobileDebugLogger } from "components/MobileDebugLogger";
import { useTranslation } from "react-i18next";
import { DefaultInteractiveTab } from "@switcherstudio/player-api-client";
import { ExtendedInteractiveTab } from "store/VideoSession/types";
import useCollectionWithVideos from "hooks/useCollectionWithVideos";
import { Button } from "components/Buttons/Button";
import { setActiveModal } from "store/Modals/slice";
import { Modals } from "store/Modals/types";
import { displayAmount } from "helpers/currency";
import { ChatPanel } from "components/Chat";
const cx = classNames.bind(styles);

export interface ChildNavState {
    label: string;
    callback: (...args: any) => any;
}
export interface ChildActionState {
    components: ReactNode;
}

const AboutTool = lazy(() => import("./About"));
const ShoppingProducts = lazy(() => import("./ShoppingProducts"));
const ShoppingCart = lazy(() => import("./ShoppingCart"));
const Subscriptions = lazy(() => import("./Subscription/Subscriptions"));

export const ToolDrawer = () => {
    const { activeTool, currentCollectionVideo } = useSelector(
        (s: RootState) => s.videoSession
    );

    const { collection } = useCollectionWithVideos();
    const { configuredCatalogId, catalog } = useSelector(
        (s: RootState) => s.catalogState
    );
    const { cart } = useSelector((s: RootState) => s.shopping);
    const [childNavState, setChildNavState] = useState<ChildNavState>();
    const [childActionState, setChildActionState] =
        useState<ChildActionState>();
    const dispatch = useDispatch<AppDispatch>();
    const { t } = useTranslation();

    const embed = useMemo(() => {
        return configuredCatalogId ? catalog : collection;
    }, [catalog, collection, configuredCatalogId]);

    const {
        playlistBroadcastIsGated,
        playlistBroadcastIsEntitled,
        userHasSubscription,
        availablePurchaseEntitlements,
        availableEmailEntitlements,
        availablePasswordEntitlements
    } = useGatedContent();

    const showShopping = useMemo(
        () => currentCollectionVideo?.broadcast?.details?.enableLiveShopping,
        [currentCollectionVideo]
    );

    const onTabClick = (newTool: ExtendedInteractiveTab) => {
        if (newTool === activeTool) return;

        setChildActionState(undefined);
        dispatch(setActiveTool(newTool));
    };

    // set default tool when new broadcast is set
    useEffect(() => {
        setChildActionState(undefined);
        // if gated, always set to about if gated gated
        if (playlistBroadcastIsGated && !playlistBroadcastIsEntitled) {
            dispatch(setActiveTool("About"));
            return;
        }

        // set the active tool to the defined default tab (falling back to about)
        dispatch(
            setActiveTool(embed?.details?.defaultInteractiveTab ?? "About")
        );
        return;
    }, [
        embed?.details?.defaultInteractiveTab,
        playlistBroadcastIsGated,
        playlistBroadcastIsEntitled,
        dispatch
    ]);

    //detect changes to cart and update cart quantity
    const cartQty = useMemo(() => {
        if (!cart?.length) {
            return null;
        }
        let sum = 0;
        cart.map((x) => (sum += x.qty));

        return sum;
    }, [cart]);

    const purchaseButtonText = useMemo<string | null>(() => {
        if (availablePurchaseEntitlements?.length === 0) return null;

        const genericPurchaseCta = t("tool-drawer:purchase-options");

        const entitlementsSummary = availablePurchaseEntitlements.map(
            (entitlement) => {
                return {
                    id: entitlement.details.id ?? "",
                    type: entitlement.details.type ?? "",
                    formattedAmount: displayAmount(
                        entitlement.prices?.[0]?.details?.amount ?? 0,
                        { signed: true }
                    ),
                    interval:
                        entitlement.prices?.[0]?.details?.purchaseInterval ??
                        "",
                    isTimeLimitedAccess:
                        entitlement.prices?.[0]?.details?.isTimeLimitedAccess,
                    prices: entitlement.prices
                };
            }
        );

        // Determine appropriate CTA
        if (entitlementsSummary?.length === 1) {
            const {
                type,
                formattedAmount,
                interval,
                isTimeLimitedAccess,
                prices
            } = entitlementsSummary[0];

            if (prices?.length === 1) {
                // If rental (regardless of entity) return rental CTA
                if (interval === "One_Time" && isTimeLimitedAccess) {
                    return t("tool-drawer:rent-one-time", {
                        amount: formattedAmount
                    });
                }

                // If the entity is a catalog or collection and the interval is monthly or yearly, return subscription CTA
                if (
                    ["Catalog", "Collection"].includes(type) &&
                    ["Monthly", "Yearly"].includes(interval)
                ) {
                    return t("tool-drawer:subscription-options");
                }

                // If not a rental or subscription, return one-time purchase CTA
                if (["Video", "Collection", "Catalog"].includes(type)) {
                    return t("tool-drawer:purchase-one-time", {
                        amount: formattedAmount
                    });
                }
            }

            return genericPurchaseCta;
        } else {
            // If more than one entitlement, show generic purchase CTA unless all are recurring
            const allRecurring = entitlementsSummary.every(
                (entitlement) =>
                    entitlement.interval && entitlement.interval.endsWith("ly")
            );

            return allRecurring
                ? t("tool-drawer:subscription-options")
                : genericPurchaseCta;
        }
    }, [availablePurchaseEntitlements, t]);

    const emailButtonText = useMemo<string | null>(
        () =>
            availableEmailEntitlements?.length > 0
                ? t("purchase-options:email-unlock")
                : null,
        [availableEmailEntitlements, t]
    );

    const passwordButtonText = useMemo<string | null>(
        () =>
            availablePasswordEntitlements?.length > 0
                ? t("purchase-options:password-unlock")
                : null,
        [availablePasswordEntitlements?.length, t]
    );

    const openPurchaseOptionsModal = useCallback(() => {
        dispatch(setActiveModal(Modals.PurchaseOptions));
    }, [dispatch]);

    const openEmailAccessModal = useCallback(() => {
        dispatch(setActiveModal(Modals.EmailAccess));
    }, [dispatch]);

    const openPasswordAccessModal = useCallback(() => {
        dispatch(setActiveModal(Modals.PasswordAccess));
    }, [dispatch]);

    const buttons = useMemo<
        Array<{ text: string; onClick: () => void }>
    >(() => {
        const buttons = [];

        if (purchaseButtonText) {
            buttons.push({
                text: purchaseButtonText,
                onClick: openPurchaseOptionsModal
            });

            if (emailButtonText && passwordButtonText) {
                buttons.push({
                    text: t("purchase-options:email-or-password-unlock"),
                    onClick: openEmailAccessModal
                });
            } else if (emailButtonText) {
                buttons.push({
                    text: emailButtonText,
                    onClick: openEmailAccessModal
                });
            } else if (passwordButtonText) {
                buttons.push({
                    text: passwordButtonText,
                    onClick: openPasswordAccessModal
                });
            }
        } else {
            if (emailButtonText) {
                buttons.push({
                    text: emailButtonText,
                    onClick: openEmailAccessModal
                });
            }
            if (passwordButtonText) {
                buttons.push({
                    text: passwordButtonText,
                    onClick: openPasswordAccessModal
                });
            }
        }

        return buttons;
    }, [
        emailButtonText,
        openEmailAccessModal,
        openPasswordAccessModal,
        openPurchaseOptionsModal,
        passwordButtonText,
        purchaseButtonText,
        t
    ]);

    return (
        <div
            className={cx("tool-drawer", {
                "is-vertical":
                    collection?.details?.aspectRatio === "NineBySixteen",
                "is-horizontal":
                    collection?.details?.aspectRatio === "SixteenByNine",
                "has-primary-button": buttons.length > 0,
                "has-secondary-button": buttons.length > 1,
                "has-child-fixed": !!childActionState
            })}
        >
            {buttons?.length > 0 && (
                <div className={cx("tool-drawer-buttons")}>
                    {buttons.map((button, index) => (
                        <Button
                            key={index}
                            text={button.text}
                            onClick={button.onClick}
                            type={index === 0 ? "button" : "link"}
                            className={cx({
                                "unlock-option-link": index !== 0
                            })}
                        />
                    ))}
                </div>
            )}
            {childNavState ? (
                <div className={cx("tool-child-nav")}>
                    <button
                        onClick={() => {
                            childNavState.callback();
                            setChildNavState(undefined);
                        }}
                    >
                        <ChevronLeftIcon /> {childNavState.label}
                    </button>
                </div>
            ) : (
                <div className={cx("tools-available")}>
                    <button
                        className={cx("tool-item-tab", {
                            active: activeTool === "About"
                        })}
                        onClick={() => onTabClick("About")}
                    >
                        <InfoIcon />
                        <small>{t("tool-drawer:about")}</small>
                    </button>
                    {showShopping && (
                        <>
                            <button
                                className={cx("tool-item-tab", {
                                    active: activeTool === "Products"
                                })}
                                onClick={() => onTabClick("Products")}
                            >
                                <ProductTagIcon />
                                <small>{t("tool-drawer:products")}</small>
                            </button>
                            <button
                                className={cx("tool-item-tab", {
                                    active: activeTool === "Cart"
                                })}
                                onClick={() => onTabClick("Cart")}
                            >
                                <CartIcon />
                                <small>{t("tool-drawer:cart")}</small>
                                {cart?.length > 0 && (
                                    <span className={cx("tool-item-tab-badge")}>
                                        {cartQty}
                                    </span>
                                )}
                            </button>
                        </>
                    )}

                    <button
                        className={cx("tool-item-tab", {
                            active: activeTool === "Chat"
                        })}
                        onClick={() => onTabClick("Chat")}
                    >
                        <ChatIcon />
                        <small>Chat</small>
                    </button>

                    <button
                        className={cx("tool-item-tab", {
                            active: activeTool === "VoD"
                        })}
                        onClick={() => onTabClick("VoD")}
                    >
                        <VoDIcon />
                        <small>{t("tool-drawer:collection")}</small>
                    </button>

                    {userHasSubscription && (
                        <button
                            className={cx("tool-item-tab", {
                                active: activeTool === "Subscription"
                            })}
                            onClick={() => onTabClick("Subscription")}
                        >
                            <CardIcon />
                            <small>{t("tool-drawer:purchases")}</small>
                        </button>
                    )}
                </div>
            )}

            <div className={cx("tool-container")}>
                {/** Uncomment debug logger to display logs */}
                {/* <MobileDebugLogger blockUnderlyingContent /> */}
                <div
                    className={cx("tool-item", {
                        active: activeTool === "About"
                    })}
                >
                    <Suspense fallback={<></>}>
                        <AboutTool />
                    </Suspense>
                </div>
                <div
                    className={cx("tool-item", {
                        active: activeTool === "Chat"
                    })}
                >
                    <Suspense fallback={<></>}>
                        <ChatPanel roomId={currentCollectionVideo?.details?.id || 'default'} />
                    </Suspense>
                </div>
                {showShopping && (
                    <>
                        <div
                            className={cx("tool-item", {
                                active: activeTool === "Products"
                            })}
                        >
                            <ShoppingProducts
                                setChildNavState={setChildNavState}
                            ></ShoppingProducts>
                        </div>
                        <div
                            className={cx("tool-item", {
                                active: activeTool === "Cart"
                            })}
                        >
                            <ShoppingCart
                                setChildActionState={setChildActionState}
                            ></ShoppingCart>
                        </div>
                    </>
                )}
                {currentCollectionVideo !== null && (
                    <div
                        className={cx("tool-item", {
                            active: activeTool === "VoD"
                        })}
                    >
                        <PlaylistList />
                    </div>
                )}
                <div
                    className={cx("tool-item", {
                        active: activeTool === "Subscription"
                    })}
                >
                    <Subscriptions />
                </div>
            </div>

            {childActionState && (
                <div className={cx("tool-child-fixed")}>
                    {childActionState.components}
                </div>
            )}
        </div>
    );
};
