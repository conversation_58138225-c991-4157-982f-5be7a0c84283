import { useMemo } from "react";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import { Divider } from "components/Divider";
import { SubscriptionParagraph } from "./SubscriptionParagraph";
import { SubscriptionButtonGroup } from "./SubscriptionButtonGroup";
import {
    EntitlementPriceResponse,
    PurchaseResponse
} from "@switcherstudio/player-api-client";
import { CreatorCustomerSubscriptionPortalResponse } from "api/payment/client";

const cx = classNames.bind(styles);

export interface SubscriptionToolProps {
    price: EntitlementPriceResponse | undefined;
    subscription: PurchaseResponse | undefined;
    response?: CreatorCustomerSubscriptionPortalResponse;
}
export const SubscriptionTool = ({
    price,
    subscription,
    response
}: SubscriptionToolProps) => {
    const isCancelled = useMemo<boolean>(
        () => !!subscription?.details?.canceledAt,
        [subscription]
    );

    if (!subscription) return null;
    const manageSubscriptionLink = response?.UpdateUrl;
    const renewSubscriptionLink = response?.RenewUrl;
    const cancelSubscriptionLink = response?.CancelUrl;

    return (
        <div className={cx("subscriptions-container")}>
            <Divider variant="section" />
            <SubscriptionParagraph
                subscription={subscription}
                price={price}
                isCancelled={isCancelled}
            />
            <SubscriptionButtonGroup
                price={subscription.prices?.[0]}
                manageSubscriptionLink={manageSubscriptionLink}
                renewSubscriptionLink={renewSubscriptionLink}
                cancelSubscriptionLink={cancelSubscriptionLink}
                isCancelled={isCancelled}
            />
        </div>
    );
};

export default SubscriptionTool;
