import { useTranslation } from "react-i18next";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import InlineNotification from "components/notification/InlineNotification";
import { SubscriptionTool } from ".";
import { useManageSubscriptionLinks } from "hooks/useManageSubscriptionLinks";
const cx = classNames.bind(styles);

const Subscriptions = () => {
    const { t } = useTranslation();
    const { subscriptionPurchaseResponseMap, oneTimePassPurchaseResponses } =
        useManageSubscriptionLinks();

    return (
        <div className={cx("container")}>
            <h3>{t("subscription-tool:header")}</h3>
            {subscriptionPurchaseResponseMap.size > 1 && (
                <InlineNotification
                    text="errors:multiple-active-subscriptions"
                    type="error"
                />
            )}
            {/* subscription passes info */}
            {subscriptionPurchaseResponseMap.size > 0 &&
                Array.from(subscriptionPurchaseResponseMap).map(
                    ([response, invoice], index) => (
                        <SubscriptionTool
                            key={index}
                            price={invoice?.prices?.[0]}
                            subscription={invoice}
                            response={response}
                        />
                    )
                )}

            {/* one time passes info */}
            {oneTimePassPurchaseResponses.map((purchase, index) => (
                <SubscriptionTool
                    key={index}
                    price={purchase?.prices?.[0]}
                    subscription={purchase}
                />
            ))}
        </div>
    );
};

export default Subscriptions;
