import classNames from "classnames";
import styles from "./index.module.scss";
import { useCallback, useMemo } from "react";
import { Button } from "components/Buttons/Button";
import { useTranslation } from "react-i18next";
import { displayAmount } from "helpers/currency";
import { EntitlementPriceResponse } from "@switcherstudio/player-api-client";
const cx = classNames.bind(styles);

export interface SubscriptionButtonGroupProps {
    price: EntitlementPriceResponse | undefined;
    manageSubscriptionLink: string | undefined;
    renewSubscriptionLink: string | undefined;
    cancelSubscriptionLink: string | undefined;
    isCancelled: boolean;
}

export const SubscriptionButtonGroup = ({
    price,
    manageSubscriptionLink,
    renewSubscriptionLink,
    cancelSubscriptionLink,
    isCancelled
}: SubscriptionButtonGroupProps) => {
    const { t } = useTranslation();

    const navigate = useCallback((url: string | undefined) => {
        if (!url) return;
        window.open(url, "_blank");
    }, []);

    const formattedPaymentAmount = useMemo<string | null>(
        () =>
            price?.details?.amount
                ? `${displayAmount(price?.details?.amount, { signed: true })}/${
                      price?.details?.purchaseInterval === "Yearly"
                          ? t("subscription-tool:year")
                          : t("subscription-tool:month")
                  }`
                : null,
        [price, t]
    );

    const isRentalOrOTP = useMemo(() => {
        return (
            price?.details?.isTimeLimitedAccess ||
            price?.details?.isRecurring === false
        );
    }, [price]);

    if (isCancelled) {
        return (
            <div className={cx("button-group")}>
                <Button
                    text={t("subscription-tool:renew", {
                        amount: formattedPaymentAmount
                    })}
                    onClick={() => navigate(renewSubscriptionLink)}
                    type="button"
                />
            </div>
        );
    }

    if (!isRentalOrOTP) {
        return (
            <div className={cx("button-group")}>
                <Button
                    text={t("subscription-tool:manage")}
                    onClick={() => navigate(manageSubscriptionLink)}
                    type="outline"
                />
                <Button
                    text={t("subscription-tool:cancel")}
                    onClick={() => navigate(cancelSubscriptionLink)}
                    type="floating"
                    className={styles["cancel-button"]}
                />
            </div>
        );
    }
};
