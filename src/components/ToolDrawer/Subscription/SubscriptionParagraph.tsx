import { useMemo } from "react";
import { displayAmount } from "helpers/currency";
import { useTranslation } from "react-i18next";
import { SubscriptionToolProps } from ".";

const dateDisplayOptions: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "long",
    day: "numeric"
};

const dateTimeDisplayOptions: Intl.DateTimeFormatOptions = {
    ...dateDisplayOptions,
    hour: "numeric",
    minute: "numeric",
    hour12: true
};

interface SubscriptionParagraphProps extends SubscriptionToolProps {
    isCancelled: boolean;
}

export const SubscriptionParagraph = ({
    subscription,
    price,
    isCancelled
}: SubscriptionParagraphProps) => {
    const { t, i18n } = useTranslation();

    const isSubscription = price?.details?.isRecurring === true;
    const isRental = price?.details?.isTimeLimitedAccess;

    const formattedStartDate = useMemo<string>(
        () =>
            subscription?.details?.invoiceDate
                ? new Intl.DateTimeFormat(
                      i18n.language,
                      dateDisplayOptions
                  ).format(subscription?.details?.invoiceDate)
                : "",
        [subscription, i18n]
    );

    const formattedPaymentAmount = useMemo<string | null>(
        () =>
            price?.details?.amount
                ? `${displayAmount(price?.details?.amount, { signed: true })} ${
                      price?.details.purchaseInterval === "Yearly"
                          ? t("subscription-tool:yearly")
                          : t("subscription-tool:monthly")
                  }`
                : null,
        [price, t]
    );

    const formattedCurrentPeriodEndDate = useMemo<string>(
        () =>
            subscription?.details?.currentPeriodEnd
                ? new Intl.DateTimeFormat(
                      i18n.language,
                      isRental ? dateTimeDisplayOptions : dateDisplayOptions
                  ).format(subscription?.details?.currentPeriodEnd)
                : "",
        [subscription?.details?.currentPeriodEnd, i18n.language, isRental]
    );

    /*
        Note: The leading spaces in the following <strong> tags are intentional.
    */
    if (isCancelled) {
        return (
            <p>
                {t("subscription-tool:paragraph-line-3-cancelled")}
                <strong>{` ${formattedCurrentPeriodEndDate}`}</strong>.
            </p>
        );
    }

    /* subscription only */
    if (isSubscription) {
        return (
            <p>
                {t("subscription-tool:paragraph-line-1")}
                <strong>{` ${formattedStartDate}`}</strong>.
                <br />
                {t("subscription-tool:paragraph-line-2")}
                <strong>{` ${formattedPaymentAmount}`}</strong>.
                <br />
                {t("subscription-tool:paragraph-line-3")}
                <strong>{` ${formattedCurrentPeriodEndDate}`}</strong>.
            </p>
        );
    }

    /* otp/rental only */
    return (
        <p>
            {t("subscription-tool:paragraph-line-1")}
            <strong>{` ${formattedStartDate}`}</strong>.
            {!!formattedCurrentPeriodEndDate && (
                <>
                    <br />
                    {t("subscription-tool:paragraph-line-3-rental")}
                    <strong>{` ${formattedCurrentPeriodEndDate}`}</strong>.
                </>
            )}
        </p>
    );
};
