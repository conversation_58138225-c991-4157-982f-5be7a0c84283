import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setHasSetDefault } from "store/VideoSession/slice";
import { RootState } from "store/reducers";
import {
    BroadcastResponse,
    CollectionVideoResponse
} from "@switcherstudio/player-api-client";
import { useSetNewCollectionVideo } from "hooks/useSetNewCollectionVideo";
import { setHasLoadedSingleBroadcastResponse } from "store/Catalog/slice";

export const useHandleDefaultState = (
    preloadedBroadcast?: BroadcastResponse | null
) => {
    const { singleBroadcastResponse } = useSelector(
        (s: RootState) => s.catalogState
    );
    const { hasSetDefault } = useSelector((s: RootState) => s.videoSession);
    const dispatch = useDispatch();

    const setNewCollectionVideo = useSetNewCollectionVideo();

    const localBroadcast = preloadedBroadcast ?? singleBroadcastResponse;

    // handle setting currentVideo
    useEffect(() => {
        if (!localBroadcast || hasSetDefault) return;

        setNewCollectionVideo({
            broadcast: localBroadcast
        } as CollectionVideoResponse);
        dispatch(setHasSetDefault(true));
        // set loaded to true in case we are using a preloaded broadcast
        dispatch(setHasLoadedSingleBroadcastResponse(true));
    }, [dispatch, setNewCollectionVideo, localBroadcast, hasSetDefault]);
};
