import { useEffect, useState } from "react";
import { useIsTabVisible } from "hooks/useIsTabVisible";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/reducers";
import { useInterval } from "hooks/useInterval";
import { AppDispatch } from "store/store";
import { getSingleBroadcastResponse } from "store/Catalog/thunks";
import { setVoDState } from "store/VideoSession/slice";
import { CollectionVideoResponse } from "@switcherstudio/player-api-client";
import { useSetNewCollectionVideo } from "hooks/useSetNewCollectionVideo";

const DEFAULT_POLLING_INTERVAL =
    parseInt(import.meta.env.VITE_DEFAULT_POLLING_INTERVAL || "") || 15000;

const useHandleLivePolling = () => {
    const isTabVisible = useIsTabVisible();
    const [delay, setDelay] = useState<number | null>(DEFAULT_POLLING_INTERVAL);
    const dispatch = useDispatch<AppDispatch>();
    const setNewCollectionVideo = useSetNewCollectionVideo();

    const { currentCollectionVideo } = useSelector(
        (s: RootState) => s.videoSession
    );
    const { configuredBroadcastId, singleBroadcastResponse } = useSelector(
        (s: RootState) => s.catalogState
    );

    // Enable/disable polling when moving away from tab
    useEffect(() => {
        if (!currentCollectionVideo) return;

        const shouldPoll =
            currentCollectionVideo?.broadcast?.details?.broadcastStatus !==
            "Ended";

        if (shouldPoll && isTabVisible)
            return setDelay(DEFAULT_POLLING_INTERVAL);

        setDelay(null);
    }, [isTabVisible, currentCollectionVideo]);

    // Handle transition from countdown to live
    useEffect(() => {
        if (!currentCollectionVideo) return;

        if (
            currentCollectionVideo?.broadcast?.details?.broadcastStatus ===
                "Ready" &&
            singleBroadcastResponse?.details?.broadcastStatus === "Active"
        ) {
            setNewCollectionVideo({
                broadcast: singleBroadcastResponse
            } as CollectionVideoResponse);
        }
    }, [
        dispatch,
        setNewCollectionVideo,
        currentCollectionVideo,
        singleBroadcastResponse
    ]);

    // Handle transition from live to non-live
    useEffect(() => {
        if (!currentCollectionVideo) return;

        if (
            currentCollectionVideo?.broadcast?.details?.broadcastStatus ===
                "Active" &&
            (singleBroadcastResponse?.details?.broadcastStatus === "Ended" ||
                singleBroadcastResponse?.details?.broadcastStatus ===
                    "Archived") // Archived is used for practice mode broadcasts as they are immediately archived
        ) {
            dispatch(setVoDState(true));
        }
    }, [dispatch, currentCollectionVideo, singleBroadcastResponse]);

    // Execute polling to get latest video
    useInterval(async () => {
        if (!configuredBroadcastId) return;

        dispatch(
            getSingleBroadcastResponse({
                broadcastId: configuredBroadcastId
            })
        );
    }, delay);
};

export default useHandleLivePolling;
