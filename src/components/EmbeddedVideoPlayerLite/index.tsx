import { useMemo, Suspense } from "react";
import Loading from "../loading/Loading";
import { VideoPlayerPlaceholderBackground } from "../Placeholder";
import styles from "components/EmbeddedVideoPlayer/index.module.scss";
import classNames from "classnames/bind";
import React from "react";
import { useHandleDefaultState } from "components/EmbeddedVideoPlayerLite/hooks/useHandleDefaultState";
import { getCorrectedAspectRatioThumbnailURL } from "helpers/imageHelpers";
import { BroadcastResponse } from "@switcherstudio/player-api-client";
import { useCollectionVideoThumbnail } from "hooks/useCollectionVideoThumbnail";
import useHandleLivePolling from "components/EmbeddedVideoPlayerLite/hooks/useHandleLivePolling";
import { useSelector } from "react-redux";
import { RootState } from "store/reducers";
import { useMediaQuery } from "hooks/useMediaQuery";
import { Image } from "components/Image";

const cx = classNames.bind(styles);

const ShakaPlayer = React.lazy(() => import("../ShakaPlayer"));

export interface VideoUrl {
    dash: string | undefined;
    hls: string | undefined;
}

interface LiteEmbeddedVideoPlayerProps {
    preloadedBroadcast?: BroadcastResponse | null;
}

export const EmbeddedVideoPlayerLite = ({
    preloadedBroadcast = null
}: LiteEmbeddedVideoPlayerProps) => {
    const { currentCollectionVideo, hasSetDefault } = useSelector(
        (s: RootState) => s.videoSession
    );
    const { hasLoadedSingleBroadcastResponse, hasLoadingError } = useSelector(
        (s: RootState) => s.catalogState
    );
    const isTablet = useMediaQuery({ maxWidth: 915, minWidth: 720 });
    const isSmallTablet = useMediaQuery({ maxWidth: 720, minWidth: 600 });

    // handle the default state initial load
    useHandleDefaultState(preloadedBroadcast);

    // begin polling for latest live data
    useHandleLivePolling();

    const playerKey = useMemo(
        () => currentCollectionVideo?.broadcast?.details?.broadcastStatus,
        [currentCollectionVideo]
    );

    const videoUrl = useMemo<VideoUrl>(
        () => ({
            hls: currentCollectionVideo?.broadcast?.videos?.[0]?.details
                ?.playback?.hls,
            dash: currentCollectionVideo?.broadcast?.videos?.[0]?.details
                ?.playback?.dash
        }),
        [currentCollectionVideo]
    );

    const videoPoster = useMemo(
        () =>
            currentCollectionVideo?.broadcast?.thumbnail?.details?.url ??
            currentCollectionVideo?.broadcast?.videos?.[0]?.details?.thumbnail,
        [currentCollectionVideo]
    );

    const isLive = useMemo(() => {
        return (
            currentCollectionVideo?.broadcast?.details?.broadcastStatus ===
            "Active"
        );
    }, [currentCollectionVideo?.broadcast?.details?.broadcastStatus]);

    const unpublished = useMemo(
        () =>
            currentCollectionVideo?.broadcast?.details?.broadcastStatus ===
            "Unpublished",
        [currentCollectionVideo?.broadcast?.details?.broadcastStatus]
    );

    const { isScheduled, scheduledThumbnail, thumbnail } =
        useCollectionVideoThumbnail({
            collectionVideo: currentCollectionVideo,
            variant: isTablet && !isSmallTablet ? "compact" : "full"
        });

    const resizedPoster = useMemo(
        () => getCorrectedAspectRatioThumbnailURL(videoPoster, 1920),
        [videoPoster]
    );

    const isLoading = useMemo(
        () => !hasLoadedSingleBroadcastResponse || !hasSetDefault,
        [hasLoadedSingleBroadcastResponse, hasSetDefault]
    );
    const displayError = useMemo(
        () => currentCollectionVideo == null && hasLoadingError,
        [currentCollectionVideo, hasLoadingError]
    );
    const isPracticePending = useMemo(
        () =>
            currentCollectionVideo?.broadcast?.details?.broadcastStatus ===
                "Ready" &&
            currentCollectionVideo?.broadcast?.details?.broadcastType ===
                "Practice",
        [currentCollectionVideo]
    );

    return (
        <div className={cx("embedded-video-player")} key={playerKey}>
            <div className={cx("video-player", "lite-player")}>
                <VideoPlayerPlaceholderBackground>
                    {isLoading ? (
                        <Loading />
                    ) : (
                        <>
                            {displayError && (
                                <div className={cx("error-message")}>
                                    There was an issue retrieving the latest
                                    video data.
                                </div>
                            )}

                            {(unpublished || !currentCollectionVideo) &&
                                !isLoading && (
                                    <div className={cx("error-message")}>
                                        This video is not available for viewing.
                                    </div>
                                )}

                            {currentCollectionVideo != null && isScheduled && (
                                <div className={cx("countdown-container")}>
                                    <Image
                                        src={thumbnail}
                                        alt="countdown thumbnail"
                                        className={cx("countdown-thumbnail")}
                                    />
                                    <div
                                        className={cx(
                                            "countdown-thumbnail",
                                            "future"
                                        )}
                                    >
                                        {scheduledThumbnail}
                                    </div>
                                </div>
                            )}

                            {isPracticePending && (
                                <div className={cx("error-message")}>
                                    Enter practice mode in the Switcher iOS app
                                    and watch your test livestream here.
                                </div>
                            )}
                        </>
                    )}
                </VideoPlayerPlaceholderBackground>

                {currentCollectionVideo != null &&
                    !unpublished &&
                    !isScheduled &&
                    !isPracticePending && (
                        <>
                            <Suspense fallback={<></>}>
                                <ShakaPlayer
                                    key={videoUrl?.dash}
                                    src={videoUrl}
                                    poster={resizedPoster}
                                    isLive={isLive}
                                    isFeaturedTrailer
                                    isLitePlayer
                                />
                            </Suspense>
                        </>
                    )}
            </div>
        </div>
    );
};
