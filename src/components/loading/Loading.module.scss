.loading-container {
    display: flex;
    justify-content: center;
    position: absolute;
    z-index: 2000;
    background-color: #00000066;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 1;
    transition: 0.25s opacity ease-in;
    align-items: center;
    pointer-events: none;
    overflow: hidden;
    border-top-left-radius: rem(16);
    border-bottom-left-radius: rem(16);

    @include mobile {
        border-radius: 0;
    }

    &.no-radius {
        border-radius: 0;
    }

    &.no-overlay {
        background-color: transparent;
    }
}
