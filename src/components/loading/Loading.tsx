import React from "react";
import LoadingAnimation from "./LoadingAnimation";
import styles from "./Loading.module.scss";
import classNames from "classnames/bind";
const cx = classNames.bind(styles);

interface LoadingProps {
    variant?: "default" | "no-overlay" | "no-radius";
}

/** A loading display element. */
const Loading: React.FC<LoadingProps> = ({ variant }: LoadingProps) => (
    <div className={cx("loading-container", variant)}>
        <LoadingAnimation />
    </div>
);

export default Loading;
