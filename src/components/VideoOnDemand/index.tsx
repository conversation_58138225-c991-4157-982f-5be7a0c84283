import { useSelector } from "react-redux";
import { RootState } from "store/reducers";
import PrevVideoIcon from "assets/icons/prev-video.svg?react";
import NextVideoIcon from "assets/icons/next-video.svg?react";
import { useMemo, useCallback } from "react";
import { shorten } from "helpers/stringHelpers";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import { useGatedContent } from "hooks/useGatedContent";
import { useSetNewCollectionVideo } from "hooks/useSetNewCollectionVideo";
import { CollectionVideoResponse } from "@switcherstudio/player-api-client";
import useCollectionWithVideos from "hooks/useCollectionWithVideos";
import { RestoreAccessPrompt } from "components/Prompts/RestoreAccess";

const cx = classNames.bind(styles);

export const VideoOnDemand = () => {
    const { upcomingVideos, onDemandVideos, hasLoaded } =
        useCollectionWithVideos();

    const { currentCollectionVideo, activeCollectionTab } = useSelector(
        (s: RootState) => s.videoSession
    );
    const {
        playlistBroadcastIsGated,
        playlistBroadcastIsEntitled,
        playerIsGated,
        gatedContentDisabled
    } = useGatedContent();
    const setNewCollectionVideo = useSetNewCollectionVideo();

    const currentVideoSet = useMemo(() => {
        return activeCollectionTab === "OnDemand"
            ? onDemandVideos
            : upcomingVideos;
    }, [onDemandVideos, upcomingVideos, activeCollectionTab]);

    const currentCollectionVideoIndex = useMemo(() => {
        if (currentCollectionVideo == null || currentVideoSet == null)
            return -1;
        return currentVideoSet?.findIndex((collectionVideo) => {
            return (
                collectionVideo?.broadcast?.details?.id ===
                currentCollectionVideo?.broadcast?.details?.id
            );
        });
    }, [currentCollectionVideo, currentVideoSet]);

    const prevVideo: CollectionVideoResponse | null | undefined =
        useMemo(() => {
            if (!hasLoaded) return null;

            if (currentVideoSet == null || currentCollectionVideoIndex <= 0)
                return null;

            return currentVideoSet?.[currentCollectionVideoIndex - 1];
        }, [hasLoaded, currentVideoSet, currentCollectionVideoIndex]);

    const nextVideo: CollectionVideoResponse | null | undefined =
        useMemo(() => {
            if (!hasLoaded) return null;

            if (
                currentVideoSet == null ||
                currentCollectionVideoIndex === currentVideoSet.length - 1
            )
                return null;

            return currentVideoSet[currentCollectionVideoIndex + 1];
        }, [hasLoaded, currentVideoSet, currentCollectionVideoIndex]);

    const handleClickPreviousVideo = useCallback(() => {
        setNewCollectionVideo(prevVideo);
    }, [prevVideo, setNewCollectionVideo]);

    const handleClickNextVideo = useCallback(() => {
        setNewCollectionVideo(nextVideo);
    }, [nextVideo, setNewCollectionVideo]);

    const hasRestoreAccessPrompt = useMemo(
        () =>
            (playlistBroadcastIsGated || playerIsGated) &&
            !playlistBroadcastIsEntitled &&
            !gatedContentDisabled,
        [
            playlistBroadcastIsGated,
            playerIsGated,
            playlistBroadcastIsEntitled,
            gatedContentDisabled
        ]
    );

    return (
        <div
            className={cx("video-on-demand-container", {
                "no-restore-access": !hasRestoreAccessPrompt
            })}
        >
            {prevVideo ? (
                <button
                    className={cx("video-nav-button", "prev")}
                    onClick={handleClickPreviousVideo}
                >
                    <div className={cx("video-nav-icon")}>
                        <PrevVideoIcon />
                    </div>
                    <div className={cx("video-nav-text")}>
                        <div className={cx("video-nav-type")}>Previous</div>
                        <div className={cx("video-nav-title")}>
                            {shorten(
                                prevVideo?.broadcast?.details?.title ?? "",
                                40
                            )}
                        </div>
                    </div>
                </button>
            ) : (
                /* NO PREVIOUS VIDEO */
                <div className={cx("video-nav-button", "prev")}></div>
            )}
            {hasRestoreAccessPrompt ? (
                <RestoreAccessPrompt variant="Video" />
            ) : (
                /* NO RESTORE PURCHASE */
                <div></div>
            )}
            {nextVideo ? (
                <button
                    className={cx("video-nav-button", "next")}
                    onClick={handleClickNextVideo}
                >
                    <div className={cx("video-nav-text")}>
                        <div className={cx("video-nav-type")}>Next</div>
                        <div className={cx("video-nav-title")}>
                            {shorten(
                                nextVideo?.broadcast?.details?.title ?? "",
                                40
                            )}
                        </div>
                    </div>
                    <div className={cx("video-nav-icon")}>
                        <NextVideoIcon />
                    </div>
                </button>
            ) : (
                /* NO NEXT VIDEO */
                <div className={cx("video-nav-button", "next")}></div>
            )}
        </div>
    );
};
