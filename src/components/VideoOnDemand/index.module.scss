.video-on-demand-container {
    display: flex;
    justify-content: space-between;
    gap: rem(40);
    align-items: center;
    padding: rem(16) rem(32);
    margin: rem(-16) rem(16) rem(16) rem(16);
    position: relative;
    min-height: rem(69);
    border-radius: 0 0 rem(16) rem(16);
    border: rem(1) solid
        rgba(var(--interactive-panel-background-contrast-color-rgb), 0.1);
    background-color: rgba(var(--interactive-panel-background-color-rgb));

    @include mobile {
        margin: 0;
        background-color: rgba(
            var(--interactive-panel-background-color-rgb),
            0.95
        );
        justify-content: center;
        border-radius: 0;
    }

    &.modal-active {
        .video-nav-button {
            height: 2.5rem;
            opacity: 0;
            pointer-events: none;
        }
    }

    .video-nav-button {
        background-color: unset;
        border: none;
        display: flex;
        align-items: center;
        gap: rem(8);
        padding: 0;
        cursor: pointer;
        width: rem(250);
        color: rgba(var(--interactive-panel-text-color-rgb));
        height: 2.5rem;

        @include mobile {
            display: none;
        }

        &:hover {
            .video-nav-icon {
                background-color: rgba(
                    var(--interactive-panel-background-contrast-color-rgb),
                    0.1
                );
            }
        }

        &.prev {
            text-align: left;
            justify-content: flex-start;
        }

        &.next {
            text-align: right;
            justify-content: flex-end;
        }

        .video-nav-icon {
            display: flex;
            justify-content: center;
            align-items: center;
            width: rem(40);
            height: rem(40);
            border-radius: 50%;
            background-color: rgba(
                var(--interactive-panel-background-contrast-color-rgb),
                0.06
            );
            transition: 0.25s background-color ease-in-out;
            flex-shrink: 0;

            svg {
                width: rem(12);
                height: rem(12);
            }
        }

        .video-nav-text {
            overflow: hidden;
            text-overflow: ellipsis;

            .video-nav-type {
                color: rgba(var(--interactive-panel-text-color-rgb));
                font-weight: 600;
                font-size: rem(11);
                line-height: rem(16);
                text-transform: uppercase;
            }

            .video-nav-title {
                color: rgba(var(--interactive-panel-text-color-rgb));
                font-weight: 600;
                font-size: rem(14);
                line-height: rem(20);
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
            }

            @media screen and (max-width: 650px) {
                font-size: rem(13);
            }
        }
    }
    .video-nav-restore-access {
        display: flex;
        font-size: rem(14);
        font-weight: 600;
        line-height: rem(20);
        letter-spacing: 0;
        text-align: center;
        align-items: center;
        gap: rem(2);
        color: rgba(var(--interactive-panel-text-color-rgb));

        @media screen and (max-width: 650px) {
            font-size: rem(13);
        }

        @media screen and (max-width: 1072px) {
            flex-direction: column;
        }

        .restore-link {
            color: rgba(var(--brand-color-primary-rgb));

            @media screen and (max-width: 900px) {
                display: block;
            }

            &:hover {
                cursor: pointer;
                color: var(--brand-color-primary-dark);
            }
        }
    }
}

/* Styles for specific player states */
:global(.is-embed:not(.is-expanded)) .video-on-demand-container {
    margin: 0;
    border-radius: 0;
}

@include mobile {
    :global(:not(.tool-active)) .video-on-demand-container.no-restore-access {
        display: none;
    }
}
