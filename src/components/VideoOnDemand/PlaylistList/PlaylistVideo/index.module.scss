.playlist-video {
    cursor: pointer;
    margin: 0 rem(24);
    padding: rem(16);
    display: flex;
    gap: rem(12);
    border-radius: rem(12);
    background-color: rgba(var(--interactive-panel-background-color-rgb));
    transition: 0.25s background-color ease-in-out;
    justify-content: flex-start;
    align-items: center;
    position: relative;

    @include mobile {
        margin: 0;
        padding: rem(21) rem(16);
        border-radius: 0;
    }

    &.current {
        background-color: rgba(var(--interactive-panel-background-contrast-color-rgb), 0.1);
        pointer-events: none;

        .playlist-video-info {
            > div {
                color: rgba(var(--interactive-panel-text-color-rgb), 0.8);
            }
            .playlist-video-title {
                font-weight: 600;
                @include wrapTextOverflow();
            }
        }
    }

    &.dark-backdrop:hover {
        background-color: rgba(255, 255, 255, 0.8);
    }

    &:hover {
        background-color: rgba(var(--interactive-panel-background-contrast-color-rgb), 0.05);
    }

    .playlist-video-image-container {
        position: relative;
        width: rem(133.66);
        min-width: rem(133.66);

        .playlist-video-thumbnail-container {
            @include sixteenByNine();

            container: video-overlay / inline-size;

            .playlist-video-image {
                position: absolute;
                top: 0;
                left: 0;
                height: 100%;
                width: 100%;
                background: #0c0c0e;
                border-radius: rem(6);
                object-fit: contain;

                &.future,
                &.offline {
                    background: black;
                }

                &.future {
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    background: rgba(0, 0, 0, 0.4);
                    backdrop-filter: blur(rem(4));

                    svg {
                        height: 100%;
                        width: 80%;
                        & > * {
                            fill: white;
                        }
                    }
                }
            }
        }
    }

    &.is-vertical {
        .playlist-video-image-container {
            width: rem(61.72);
            min-width: rem(61.72);
        }

        .playlist-video-thumbnail-container {
            @include nineBySixteen();
        }
    }
    .playlist-video-info {
        .playlist-video-title {
            color: rgba(var(--interactive-panel-text-color-rgb));
            font-weight: 400;
            font-size: rem(16);
            line-height: rem(22);
        }

        .playlist-video-meta {
            margin: rem(2) 0 rem(8);
            font-weight: 400;
            font-size: rem(14);
            line-height: rem(20);
            color: rgba(var(--interactive-panel-text-color-rgb), 0.5);
        }
        > div {
            color: rgba(var(--interactive-panel-text-color-rgb), 0.5);
        }
    }
}
