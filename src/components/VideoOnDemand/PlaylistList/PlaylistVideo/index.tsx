import timeSince from "helpers/timeFrom";
import { useMemo } from "react";
import { useSelector } from "react-redux";
import { RootState } from "store/reducers";
import { useSetNewCollectionVideo } from "hooks/useSetNewCollectionVideo";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import { Image } from "components/Image";
import { isDayOf, isInFuture, isInPast, localizeTime } from "helpers/time";
import { CollectionVideoResponse } from "@switcherstudio/player-api-client";
import useCollectionWithVideos from "hooks/useCollectionWithVideos";
import { useCollectionVideoThumbnail } from "hooks/useCollectionVideoThumbnail";
import { VideoBadges } from "components/VideoBadges";
import dayjs from "dayjs";
const cx = classNames.bind(styles);

interface PlaylistVideoProps {
    collectionVideo?: CollectionVideoResponse;
    darkBackdrop?: boolean;
}

export const PlaylistVideo = ({
    collectionVideo,
    darkBackdrop
}: PlaylistVideoProps) => {
    const { collection } = useCollectionWithVideos();
    const { currentCollectionVideo } = useSelector(
        (s: RootState) => s.videoSession
    );
    const setNewCollectionVideo = useSetNewCollectionVideo();

    const isVertical = useMemo(
        () => collection?.details?.aspectRatio === "NineBySixteen",
        [collection?.details?.aspectRatio]
    );

    const {
        thumbnail,
        scheduledThumbnail,
        isScheduledLive,
        isScheduledUpload
    } = useCollectionVideoThumbnail({
        collectionVideo,
        variant: isVertical ? "compact-vertical" : "compact"
    });

    const broadcastMeta = useMemo(() => {
        if (collectionVideo?.broadcast?.details?.broadcastStatus === "Active")
            return "LIVE";
        if (
            isInFuture(collectionVideo?.broadcast?.details?.endedAt) &&
            isInPast(collectionVideo?.broadcast?.details?.startsAt)
        )
            return "Premiering";
        if (
            collectionVideo?.broadcast?.details?.endedAt &&
            !isScheduledLive &&
            !isScheduledUpload
        ) {
            return `${timeSince(
                collectionVideo?.broadcast?.details?.endedAt
            )} • ${Math.round(
                (collectionVideo?.broadcast?.videos?.[0]?.details
                    ?.duration as number) / 60
            )} min`;
        }

        let metaPrefix = isScheduledLive ? "Live" : "Premieres";
        const isToday = isDayOf(collectionVideo?.broadcast?.details?.startsAt);
        return `${metaPrefix} ${
            isToday
                ? `at ${localizeTime(
                      collectionVideo?.broadcast?.details?.startsAt,
                      "h:mm A"
                  )}`
                : dayjs(collectionVideo?.broadcast?.details?.startsAt).format(
                      "dddd, MMMM DD, YYYY hh:mm A"
                  )
        }`;
    }, [
        collectionVideo?.broadcast?.details?.broadcastStatus,
        collectionVideo?.broadcast?.details?.startsAt,
        collectionVideo?.broadcast?.details?.endedAt,
        isScheduledLive,
        isScheduledUpload,
        collectionVideo?.broadcast?.videos
    ]);

    return (
        <div
            className={cx("playlist-video", {
                current:
                    currentCollectionVideo?.broadcast?.details?.id ===
                    collectionVideo?.broadcast?.details?.id,
                "dark-backdrop": darkBackdrop,
                "is-vertical":
                    collection?.details?.aspectRatio === "NineBySixteen"
            })}
            onClick={() => setNewCollectionVideo(collectionVideo)}
        >
            <div className={cx("playlist-video-image-container")}>
                <div className={cx("playlist-video-thumbnail-container")}>
                    <>
                        {thumbnail && (
                            <Image
                                src={thumbnail}
                                alt="video thumbnail"
                                className={cx("playlist-video-image")}
                            />
                        )}
                        {scheduledThumbnail && (
                            <div
                                className={cx("playlist-video-image", "future")}
                            >
                                {scheduledThumbnail}
                            </div>
                        )}
                    </>
                </div>
            </div>

            <div className={cx("playlist-video-info")}>
                <div className={cx("playlist-video-title")}>
                    {collectionVideo?.broadcast?.details?.title}
                </div>
                <div className={cx("playlist-video-meta")}>{broadcastMeta}</div>
                {!!collectionVideo && (
                    <VideoBadges
                        isPlaylist={true}
                        collectionVideo={collectionVideo}
                    ></VideoBadges>
                )}
            </div>
        </div>
    );
};
