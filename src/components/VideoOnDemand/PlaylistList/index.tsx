import { useEventTracking } from "hooks/useEventTracking";
import { PlaylistVideo } from "./PlaylistVideo";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import useCollectionWithVideos from "hooks/useCollectionWithVideos";
import { useCallback, useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
    getCollectionVideosOnDemand,
    getCollectionVideosUpcoming
} from "store/Catalog/thunks";
import { setActiveCollectionTab } from "store/VideoSession/slice";
import { RootState } from "store/reducers";
import { AppDispatch } from "store/store";
import Loading from "components/loading/Loading";
const cx = classNames.bind(styles);

export const PlaylistList = () => {
    const dispatch = useDispatch<AppDispatch>();
    const [isLoadingMoreVideos, setIsLoadingMoreVideos] = useState(false);
    const { trackEvent } = useEventTracking();
    const playlistContainerRef = useRef<HTMLDivElement | null>(null);

    const { activeCollectionTab } = useSelector(
        (s: RootState) => s.videoSession
    );

    const {
        onDemandVideos,
        upcomingVideos,
        onDemandVideosCount,
        upcomingVideosCount,
        collection,
        hasLoaded
    } = useCollectionWithVideos();

    // Set the default active tab when the collection is loaded
    useEffect(() => {
        if (
            activeCollectionTab ||
            !collection ||
            collection?.details?.isLivestreamPlayer === undefined
        )
            return;

        dispatch(
            setActiveCollectionTab(
                collection?.details?.isLivestreamPlayer
                    ? "Upcoming"
                    : collection.details?.defaultCollectionTab || "OnDemand"
            )
        );
    }, [dispatch, activeCollectionTab, collection]);

    // when user scrolls to the bottom of the playlist, load more videos
    const loadMoreVideos = useCallback(async () => {
        if (isLoadingMoreVideos) return; // Prevent duplicate calls
        setIsLoadingMoreVideos(true);

        try {
            if (activeCollectionTab === "OnDemand") {
                await dispatch(
                    getCollectionVideosOnDemand({
                        collectionId: collection?.details?.id ?? ""
                    })
                );
            }
            if (activeCollectionTab === "Upcoming") {
                await dispatch(
                    getCollectionVideosUpcoming({
                        collectionId: collection?.details?.id ?? ""
                    })
                );
            }
            trackEvent("Load More Videos on Scroll", {
                collectionId: collection?.details?.id,
                collectionType: activeCollectionTab
            });
        } catch (error) {
            console.error("Error loading more videos:", error);
        } finally {
            setIsLoadingMoreVideos(false);
        }
    }, [
        dispatch,
        collection?.details?.id,
        isLoadingMoreVideos,
        trackEvent,
        activeCollectionTab
    ]);

    const handleScroll = useCallback(() => {
        const container = playlistContainerRef.current;
        if (!container) return;
        if (
            (activeCollectionTab === "OnDemand" &&
                onDemandVideos.length === onDemandVideosCount) ||
            (activeCollectionTab === "Upcoming" &&
                upcomingVideos.length === upcomingVideosCount)
        )
            return; // No more videos to load

        const scrollTop = container.scrollTop; // How far the user has scrolled
        const scrollHeight = container.scrollHeight; // Total height of the content
        const clientHeight = container.clientHeight; // Visible height of the container

        // Check if the user is near the bottom of the container
        if (scrollTop + clientHeight >= scrollHeight - 200) {
            loadMoreVideos();
        }
    }, [
        loadMoreVideos,
        onDemandVideosCount,
        onDemandVideos,
        upcomingVideosCount,
        upcomingVideos,
        activeCollectionTab
    ]);

    useEffect(() => {
        const container = playlistContainerRef.current;
        if (!container) return;
        container.addEventListener("scroll", handleScroll);

        return () => {
            container.removeEventListener("scroll", handleScroll);
        };
    }, [handleScroll]);

    if (!hasLoaded) return <Loading variant="no-overlay" />;

    return (
        <div className={cx("playlist-container")}>
            {!collection?.details?.isLivestreamPlayer && (
                <div className={cx("playlist-tabs")}>
                    <button
                        className={cx("playlist-tab-item", {
                            active: activeCollectionTab == "OnDemand"
                        })}
                        onClick={() =>
                            dispatch(setActiveCollectionTab("OnDemand"))
                        }
                    >
                        <small>{`On-Demand ${
                            onDemandVideosCount !== undefined
                                ? `(${onDemandVideosCount})`
                                : ""
                        }`}</small>
                    </button>
                    <button
                        className={cx("playlist-tab-item", {
                            active: activeCollectionTab == "Upcoming"
                        })}
                        onClick={() =>
                            dispatch(setActiveCollectionTab("Upcoming"))
                        }
                    >
                        <small>{`Upcoming ${
                            upcomingVideosCount !== undefined
                                ? `(${upcomingVideosCount})`
                                : ""
                        }`}</small>
                    </button>
                </div>
            )}
            <div
                className={cx("playlist-tab-content-container", {
                    "no-tabs": !!collection?.details?.isLivestreamPlayer
                })}
                ref={playlistContainerRef}
            >
                <div
                    className={cx("playlist-list", {
                        active: activeCollectionTab == "OnDemand"
                    })}
                >
                    <div className={cx("playlist")}>
                        {onDemandVideos?.length ? (
                            onDemandVideos?.map((collectionVideo, index) => (
                                <PlaylistVideo
                                    collectionVideo={collectionVideo}
                                    key={index}
                                />
                            ))
                        ) : (
                            <p className={cx("empty-state")}>
                                No on-demand content in this collection
                            </p>
                        )}
                    </div>
                </div>
                <div
                    className={cx("playlist-list", {
                        active: activeCollectionTab == "Upcoming"
                    })}
                >
                    <div className={cx("playlist")}>
                        {upcomingVideos?.length ? (
                            upcomingVideos?.map((collectionVideo, index) => (
                                <PlaylistVideo
                                    collectionVideo={collectionVideo}
                                    key={index}
                                />
                            ))
                        ) : (
                            <p className={cx("empty-state")}>
                                No upcoming content in this collection
                            </p>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};
