.playlist-container {
    .playlist-tabs {
        display: flex;
        border-top: rem(0.5) solid
            rgba(var(--interactive-panel-background-contrast-color-rgb), 0.1);
        border-bottom: rem(0.5) solid
            rgba(var(--interactive-panel-background-contrast-color-rgb), 0.1);

        .playlist-tab-item {
            flex-basis: 50%;
            font-weight: 500;
            line-height: rem(24);
            height: rem(48);
            text-align: center;
            text-transform: uppercase;
            border: none;
            color: var(--interactive-panel-text-color);
            position: relative;
            background: rgba(
                var(--interactive-panel-background-contrast-color-rgb),
                0.1
            );
            transition: 0.25s color ease-in-out;

            &:after {
                content: "";
                display: block;
                position: absolute;
                opacity: 0;
                left: 0;
                bottom: 0;
                height: rem(3);
                width: 100%;
                background-color: var(--brand-color-primary);
                transition: 0.25s opacity ease-in-out;
            }

            &:hover {
                color: var(--brand-color-primary);
                &:after {
                    opacity: 0.2;
                }
            }

            &.active {
                color: var(--brand-color-primary);
                font-weight: 700;
                &:after {
                    opacity: 1;
                }
            }
        }
    }

    .playlist-tab-content-container {
        position: absolute;

        top: rem(50);
        right: 0;
        bottom: 0;
        left: 0;
        overflow-y: auto;

        &.no-tabs {
            top: 0;
        }

        .playlist-list {
            position: absolute;
            z-index: 2001;
            top: 0;
            padding: rem(13) 0;

            width: 100%;
            display: none;
            flex-direction: column;

            background-color: rgba(
                var(--interactive-panel-background-color-rgb)
            );
            transition: 0.3s opacity ease-in-out;

            &.active {
                display: flex;
            }

            .playlist-section-title {
                color: rgba(var(--interactive-panel-text-color-rgb));
                font-weight: 400;
                font-size: rem(14);
                line-height: rem(20);
                text-transform: uppercase;
                padding: rem(14) rem(40) rem(8);

                @include mobile {
                    padding: rem(16) rem(16) rem(8);
                }
            }

            .playlist {
                overflow-y: auto;
                padding-bottom: rem(16);
                display: flex;
                flex-direction: column;
                gap: rem(1);
                padding-top: 1px;

                @include mobile {
                    overflow: initial;
                }

                @media screen and (max-height: 610px) {
                    overflow: initial;
                }

                .empty-state {
                    font-size: rem(14);
                    text-align: center;
                    color: var(--interactive-panel-text-color);
                }
            }
        }
    }
}
