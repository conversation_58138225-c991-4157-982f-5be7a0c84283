import { useTranslation } from "react-i18next";
import styles from "./InlineNotification.module.scss";

export interface InlineNotificationProps {
    text: string;
    type: InlineNotificationType;
}

type InlineNotificationType = "success" | "info" | "warning" | "error";

const InlineNotification = ({ text, type }: InlineNotificationProps) => {
    const { t } = useTranslation();
    const notificationRole = type === "error" ? "alert" : "note";
    return (
        <p
            className={`${styles["inline-notification"]} ${styles[type]}`}
            role={notificationRole}
        >
            {t(text)}
        </p>
    );
};

export default InlineNotification;
