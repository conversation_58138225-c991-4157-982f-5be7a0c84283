@import "./NotificationContainer.module.scss";

.alert {
    --alert-opacity: 1;

    opacity: 1;
    transition: opacity 0.1s ease-in-out;

    @starting-style {
        opacity: 0;
    }

    position: relative;
    padding: rem(20) rem(26);
    border: rem(1) solid transparent;
    border-radius: 0.25rem;

    display: flex;
    width: fit-content;
    color: #212529;
    &.danger {
        background-color: rgba(248, 215, 218, var(--alert-opacity));
        border-color: #f5c6cb;
    }

    &.success {
        background-color: rgba(212, 237, 218, var(--alert-opacity));
        border-color: #c3e6cb;
    }

    &.info {
        flex-direction: row-reverse;
        justify-content: flex-end;
        gap: rem(22);

        background: linear-gradient(
                0deg,
                rgba(36, 138, 61, 0.08),
                rgba(36, 138, 61, 0.08)
            ),
            #ffffff;
        border: 1px solid rgba(36, 138, 61, 0.24);
    }

    .message {
        .click-action {
            text-decoration: underline;
            margin-top: 1rem;
            cursor: pointer;
        }
    }
    .close {
        background: none;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;

        svg {
            transition: 0.25s transform ease-in-out;
            width: rem(14);
            height: rem(14);
            color: #212529;
        }

        &:hover {
            svg {
                transform: scale3d(1.1, 1.1, 1);
                color: #212529;
            }
        }
    }
}

.alert-dismissible {
    padding-right: 4rem;
}
