.notification-container {
    z-index: 2000;
    visibility: collapse;
    transition: 0.5s;
    position: absolute;
    top: rem(10);
    right: rem(17);
    width: 62%;

    display: flex;
    gap: rem(20);
    flex-direction: column;

    align-items: flex-end;

    &.bottom-left {
        top: auto;
        right: auto;
        left: rem(17);
        bottom: rem(75);

        align-items: flex-start;
    }
}

.show {
    visibility: visible;
}
