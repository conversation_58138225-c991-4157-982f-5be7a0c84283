import React, { useState, useCallback, useEffect, useMemo } from "react";
import { useSelector } from "react-redux";
import { RootState } from "store/reducers";
import classNames from "classnames/bind";
import styles from "./index.module.scss";
import { useTranslation } from "react-i18next";
import { useEventTracking } from "hooks/useEventTracking";
import { SearchBar } from "components/inputs/text-input/SearchBar";
import { useDispatch } from "react-redux";
import { searchCatalogVideos } from "store/Catalog/thunks";
import { AppDispatch } from "store/store";
import { resetSearch } from "store/Catalog/slice";
import { CollectionContent } from "components/Catalog/Collections/Collection/CollectionContent";
import { eventBus } from "helpers/eventBus";

const cx = classNames.bind(styles);

interface SearchProps {}

export const Search: React.FC<SearchProps> = () => {
    const {
        catalog,
        currentSearchTerm,
        hasLoadedSearchResultsResponse,
        searchResultsResponse
    } = useSelector((s: RootState) => s.catalogState);
    const { trackEvent } = useEventTracking();
    const { t } = useTranslation();
    const isSearchEnabled = catalog?.details?.isSearchEnabled;
    const dispatch = useDispatch<AppDispatch>();
    const [searchQuery, setSearchQuery] = useState<string>("");
    const [isSearchLoading, setIsSearchLoading] = useState(false);

    const handleSearch = useCallback(
        (searchTerm: string) => {
            try {
                // Prevent empty searches
                if (!searchTerm.trim()) {
                    return;
                }

                dispatch(
                    searchCatalogVideos({
                        projectId: catalog?.details?.projectId ?? "",
                        searchTerm: searchTerm,
                        page: 1,
                        pageSize: 23 // Default page size
                    })
                );

                // disable the search bar while loading
                setIsSearchLoading(true);
            } catch (error) {
                console.error("Error searching catalog videos:", error);
            }
        },
        [dispatch, catalog?.details?.projectId]
    );

    useEffect(() => {
        if (hasLoadedSearchResultsResponse) {
            setIsSearchLoading(false);

            // Track search
            trackEvent("Search Catalog", {
                searchResultsResponse
            });
        }
    }, [
        hasLoadedSearchResultsResponse,
        setIsSearchLoading,
        searchResultsResponse,
        currentSearchTerm,
        trackEvent
    ]);

    const onReset = useCallback(() => {
        if (!searchQuery.trim()) {
            return;
        }
        dispatch(resetSearch());

        // takes care of resizing the catalog when the search is reset
        eventBus.emit("searchReset");
        setSearchQuery("");
    }, [dispatch, searchQuery]);

    const showReset = useMemo<boolean>(() => {
        return !!currentSearchTerm;
    }, [currentSearchTerm]);

    const catalogSearchEmptyState = (
        <div className={cx("empty-state-container")}>
            <p>{t("search-bar:no-results") + `"${currentSearchTerm}"`}</p>
        </div>
    );

    const catalogsearchResultsResponse = (
        <div className={cx("search-results-container")}>
            <p>
                {searchResultsResponse?.totalRecords?.toString() +
                    (searchResultsResponse?.totalRecords === 1
                        ? t("search-bar:result-for")
                        : t("search-bar:results-for")) +
                    `"${currentSearchTerm}"`}
            </p>
            <CollectionContent
                collectionVideoData={searchResultsResponse}
                collectionVideoDataType="search"
            />
        </div>
    );

    return (
        isSearchEnabled && (
            <div className={cx("search-container")}>
                <SearchBar
                    isLoading={isSearchLoading}
                    onChange={(event) => setSearchQuery(event.target.value)}
                    value={searchQuery ?? ""}
                    onSubmit={() => handleSearch(searchQuery)}
                    onReset={onReset}
                    showReset={showReset}
                />
                {hasLoadedSearchResultsResponse &&
                    (searchResultsResponse?.totalRecords == 0
                        ? catalogSearchEmptyState
                        : catalogsearchResultsResponse)}
            </div>
        )
    );
};
