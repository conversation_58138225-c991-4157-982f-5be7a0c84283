import { PropsWithChildren } from "react";
import styles from "./index.module.scss";

export interface CardProps extends PropsWithChildren {
    className?: string;
    position?: "background" | "foreground";
}

export const Card: React.FC<CardProps> = ({
    children,
    className,
    position = "background"
}) => (
    <div className={`${className} ${styles["card"]} ${styles[position]}`}>
        {children}
    </div>
);
