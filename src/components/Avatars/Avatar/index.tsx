import { DetailedHTMLProps, ImgHTMLAttributes } from "react";
import styles from "./index.module.scss";

export interface AvatarProps
    extends DetailedHTMLProps<
        ImgHTMLAttributes<HTMLImageElement>,
        HTMLImageElement
    > {
    variant?: "circle";
}

export const Avatar = ({ variant = "circle", ...props }: AvatarProps) => {
    return <img className={styles[variant]} {...props}></img>;
};
