import React, {
    PropsWithChildren,
    useLayoutEffect,
    useRef,
    useState
} from "react";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
const cx = classNames.bind(styles);

export interface TruncatedElementProps extends PropsWithChildren {
    className?: string;
    onReadMore?: (...args: any) => any;
}

export const TruncatedElement = ({
    className,
    onReadMore,
    children
}: TruncatedElementProps) => {
    const [isTruncated, setIsTruncated] = useState<boolean>(false);
    const [isReadingMore, setIsReadingMore] = useState<boolean>(false);

    const truncatedElementRef = useRef<HTMLDivElement>(null);
    const truncatedElementTextRef = useRef<HTMLDivElement>(null);

    // Set truncated status
    useLayoutEffect(() => {
        const calcTruncated = () => {
            const { offsetHeight, scrollHeight } =
                truncatedElementRef.current || {};
            if (offsetHeight && scrollHeight && offsetHeight < scrollHeight) {
                setIsTruncated(true);
            } else {
                setIsTruncated(false);
            }
        };

        // allow recalc to happen after it's visible when the player
        setTimeout(calcTruncated, 5);

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    if (children?.valueOf() === "") {
        return null;
    }

    return (
        <div className={className}>
            <div
                className={cx("truncated-element", {
                    "is-reading-more": isReadingMore,
                    "is-truncated": isTruncated
                })}
                onClick={() => {
                    setIsReadingMore(true);
                    onReadMore && onReadMore();
                }}
            >
                <div
                    ref={truncatedElementRef}
                    className={cx("truncated-element-snippet")}
                >
                    <div
                        ref={truncatedElementTextRef}
                        className={cx("truncated-element-text")}
                    >
                        {children}
                    </div>
                </div>
                {isTruncated && !isReadingMore && (
                    <div className={cx("truncated-element-read-more")}>
                        ...Read More
                    </div>
                )}
            </div>
        </div>
    );
};
