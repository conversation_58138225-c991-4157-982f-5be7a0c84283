.truncated-element {
    display: block;
    position: relative;
    overflow: hidden;
    contain: content;

    &.is-truncated {
        cursor: pointer;

        .truncated-element-snippet {
            -webkit-mask-image: linear-gradient(
                    to top,
                    transparent 0%,
                    transparent rem(20),
                    rgb(255, 255, 255) rem(20),
                    rgb(255, 255, 255) 100%
                ),
                linear-gradient(
                    to right,
                    rgb(255, 255, 255) 0%,
                    rgb(255, 255, 255) rem(150),
                    transparent rem(240),
                    transparent 100%
                );
        }
    }

    &.is-reading-more {
        cursor: initial;

        .truncated-element-snippet {
            max-height: initial;
            -webkit-mask-image: none;
        }
    }

    .truncated-element-snippet {
        position: relative;
        overflow: hidden;
        max-height: rem(60);
        white-space: pre-wrap;

        .truncated-element-text {
            white-space: pre-wrap;
            color: var(--interactive-panel-text-color);
        }
    }

    .truncated-element-read-more {
        position: absolute;
        right: rem(10);
        bottom: 0;
        color: var(--brand-color-primary);
        font-size: rem(14);
        line-height: rem(20);
        font-weight: 700;
        min-width: rem(90);
        &:hover {
            color: var(--brand-color-primary-dark);
        }
    }
}
