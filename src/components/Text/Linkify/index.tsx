import React from "react";
import ReactDOMServer from "react-dom/server";

interface LinkifyProps {
    input: string;
}
export const Linkify = ({ input }: LinkifyProps) => {
    const urlRegex = () =>
        /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_+.~#?&//=]*)/g;
    const htmlRegex = /(<([^>]+)>)/gi;

    // Get `<a>` element as string
    const linkify = (href: string) =>
        ReactDOMServer.renderToString(
            React.createElement(
                "a",
                {
                    name: "a",
                    href: href,
                    text: href,
                    target: "_blank"
                },
                href
            )
        );

    const cleanString = (string: string) => {
        return string.replace(htmlRegex, "");
    };

    const linkifyUrls = (string: string) => {
        let displayString = cleanString(string);
        return displayString.replace(urlRegex(), (match: string) =>
            linkify(match)
        );
    };

    return <div dangerouslySetInnerHTML={{ __html: linkifyUrls(input) }} />;
};
