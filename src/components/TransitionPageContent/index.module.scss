.transition-page {
    display: flex;
    flex-flow: column nowrap;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    text-align: center;

    &.page {
        width: 100vw;
        height: 100vh;
    }

    &.modal {
        padding: rem(40);
    }

    h4,
    p {
        margin: 0;
    }

    div[class*="loading-container"] {
        margin-bottom: 2rem;
    }

    .status-icon {
        width: rem(40);
        height: rem(40);
        margin-bottom: 0.5rem;
    }
}
