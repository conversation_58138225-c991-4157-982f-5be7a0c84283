import ReplayIcon from "assets/icons/replay.svg?react";
import ProductTag from "assets/icons/product-tag.svg?react";
import SuccessIcon from "assets/icons/success-filled.svg?react";
import TicketIcon from "assets/icons/gated.svg?react";
import LockIcon from "assets/icons/lock.svg?react";
import { useMemo } from "react";
import { CollectionVideoResponse } from "@switcherstudio/player-api-client";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import { displayAmount } from "../../helpers/currency";
import { useGatedContent } from "hooks/useGatedContent";
import { useTranslation } from "react-i18next";
const cx = classNames.bind(styles);

interface VideoBadgesProps {
    isPlaylist?: boolean;
    collectionVideo?: CollectionVideoResponse;
    isOverlay?: boolean;
}
export const VideoBadges = ({
    isPlaylist = false,
    collectionVideo,
    isOverlay = false
}: VideoBadgesProps) => {
    const { t } = useTranslation();

    const {
        playerPurchaseEntitlements,
        playerIsEntitled,
        playerIsGated,
        playlistBroadcastIsGated,
        playlistBroadcastPurchaseEntitlements,
        playlistBroadcastEmailEntitlements,
        playlistBroadcastPasswordEntitlements,
        playlistBroadcastIsEntitled,
        playlistBroadcastInvoices
    } = useGatedContent(collectionVideo);

    const videoBadges = useMemo(() => {
        let broadcastDetails = collectionVideo?.broadcast?.details ?? undefined;
        let badges = [];
        if (broadcastDetails?.broadcastStatus === "Active")
            badges.push({
                slug: "live"
            });
        if (!isPlaylist && broadcastDetails?.broadcastStatus === "Ended")
            badges.push({
                slug: "replay",
                icon: <ReplayIcon />
            });
        if (broadcastDetails?.enableLiveShopping)
            badges.push({
                slug: "shop",
                icon: <ProductTag />
            });
        if (playlistBroadcastInvoices.length > 0)
            badges.push({
                slug: "purchased",
                icon: <SuccessIcon />
            });

        if (playerIsGated && !playerIsEntitled && isOverlay) {
            if (
                playlistBroadcastPurchaseEntitlements.length > 0 &&
                !playlistBroadcastIsEntitled
            ) {
                badges.push({
                    slug: t("badges:buy-or-subscribe"),
                    icon: <TicketIcon />
                });
            } else {
                const prices = playerPurchaseEntitlements?.[0]?.prices;
                if (prices && prices.length > 1) {
                    const monthlyPass = playerPurchaseEntitlements
                        .find((pe) =>
                            pe.prices?.some(
                                (p) => p.details?.purchaseInterval === "Monthly"
                            )
                        )
                        ?.prices?.find(
                            (p) => p.details?.purchaseInterval === "Monthly"
                        );
                    const amount = monthlyPass?.details?.amount
                        ? displayAmount(monthlyPass?.details?.amount, {
                              signed: true,
                              compact: false
                          })
                        : "$0";
                    const frequency =
                        monthlyPass?.details?.purchaseInterval === "Yearly"
                            ? t("badges:year")
                            : t("badges:month");

                    badges.push({
                        slug: `${t(
                            "badges:starting-at"
                        )} ${amount}/${frequency}`,
                        icon: <TicketIcon />
                    });
                } else {
                    prices?.forEach((price) => {
                        const amount = price?.details?.amount
                            ? displayAmount(price?.details?.amount, {
                                  signed: true,
                                  compact: false
                              })
                            : "$0";
                        const frequency =
                            price?.details?.purchaseInterval === "Yearly"
                                ? t("badges:year")
                                : t("badges:month");
                        badges.push({
                            slug: `${amount}/${frequency}`,
                            icon: <TicketIcon />
                        });
                    });
                }
            }
        } else if (playlistBroadcastIsGated && !playlistBroadcastIsEntitled) {
            if (playlistBroadcastPurchaseEntitlements?.length > 0) {
                playlistBroadcastPurchaseEntitlements?.forEach((p) => {
                    p.prices?.forEach((price) => {
                        const text = price?.details?.isTimeLimitedAccess
                            ? `${t("badges:rent-for")} ${
                                  price?.details?.amount
                                      ? displayAmount(price?.details?.amount, {
                                            signed: true
                                        })
                                      : "$0"
                              }`
                            : `${t("badges:buy-for")} ${
                                  price?.details?.amount
                                      ? displayAmount(price?.details?.amount, {
                                            signed: true
                                        })
                                      : "$0"
                              }`;

                        badges.push({
                            slug: text,
                            icon: <TicketIcon />
                        });
                    });
                });
            } else if (
                playlistBroadcastPasswordEntitlements?.length > 0 ||
                playlistBroadcastEmailEntitlements?.length > 0
            ) {
                badges.push({
                    slug: "",
                    icon: <LockIcon />
                });
            }
        }
        return badges;
    }, [
        collectionVideo?.broadcast?.details,
        isPlaylist,
        playlistBroadcastIsGated,
        playlistBroadcastIsEntitled,
        playerIsGated,
        playerIsEntitled,
        playlistBroadcastPurchaseEntitlements,
        playlistBroadcastPasswordEntitlements,
        playlistBroadcastEmailEntitlements,
        t,
        playerPurchaseEntitlements,
        playlistBroadcastInvoices,
        isOverlay
    ]);

    return (
        <>
            {videoBadges.length > 0 && (
                <div
                    className={cx("video-badges", {
                        "is-playlist": isPlaylist
                    })}
                >
                    {videoBadges.map((badge, index) => {
                        return (
                            <div
                                className={cx(
                                    isOverlay
                                        ? "video-badge-white"
                                        : "video-badge",
                                    "badge",
                                    badge.slug
                                )}
                                key={badge.slug + index}
                            >
                                {badge.icon}
                                {badge.slug}
                            </div>
                        );
                    })}
                </div>
            )}
        </>
    );
};
