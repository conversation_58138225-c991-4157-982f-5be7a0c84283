.video-badges {
    padding: rem(16) 0;
    border-top: rem(0.5) solid
        rgba(var(--interactive-panel-text-color-rgb), 0.8);
    display: flex;
    gap: rem(8);

    &.is-playlist {
        padding: 0;
        border-top: 0;
    }

    .video-badge {
        color: rgba(var(--interactive-panel-text-color-rgb), 0.8);
        border: 0.09rem solid rgba(var(--interactive-panel-text-color-rgb), 0.8);
    }

    .video-badge-white {
        color: white;
        border: 0.09rem solid white;
    }

    svg {
        width: rem(14);
        height: rem(14);
    }

    .live {
        background-color: #da1f0e;
        color: #ffffff;
        text-transform: uppercase;
        font-weight: 700;
        border: none;
    }

    .replay {
        background-color: #4d5358;
        color: white;
        border: none;
    }
}
