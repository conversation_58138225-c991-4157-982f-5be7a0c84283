.container {
    & > img {
        width: 100%;
        height: 100%;
        position: absolute;
        object-fit: contain;
        background-color: #000;

        transition: 0.25s border-radius ease-in-out;
        border-radius: rem(16) rem(0) rem(0) rem(0);

        @include mobile {
            border-radius: 0;
        }
    }
}

/* Styles for specific player states */
:global(.is-embed:not(.is-expanded)) {
    .container > img {
        border-radius: 0;
    }
}

:global(.tool-active) {
    .container > img {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }
}
