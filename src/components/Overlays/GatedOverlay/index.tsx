import TicketIcon from "assets/icons/ticketnew.svg?react";
import LockIcon from "assets/icons/lock.svg?react";
import { useTranslation } from "react-i18next";
import { Overlay, OverlayWithEntitlements } from "..";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import { useMemo } from "react";
import { useGatedContent } from "hooks/useGatedContent";
const cx = classNames.bind(styles);

export interface GatedOverlayProps extends OverlayWithEntitlements {
    poster?: string;
    minimized?: boolean;
}

export const GatedOverlay: React.FC<GatedOverlayProps> = ({
    poster,
    collection,
    collectionVideo,
    video
}) => {
    const { t } = useTranslation();

    const {
        availablePurchaseEntitlements,
        availablePasswordEntitlements,
        availableEmailEntitlements
    } = useGatedContent();

    const hasOnlySubscriptions = useMemo(() => {
        if (availablePurchaseEntitlements.length === 0) {
            return false;
        }
        return availablePurchaseEntitlements.every((e) => {
            return (
                e.prices?.every((price) => price.details?.isRecurring) &&
                (e.details.type === "Catalog" ||
                    e.details.type === "Collection")
            );
        });
    }, [availablePurchaseEntitlements]);

    const overlayText = useMemo<string>(() => {
        if (hasOnlySubscriptions) {
            return t("overlays:subscribe-to-watch");
        }
        if (availablePurchaseEntitlements.length > 0) {
            return t("overlays:gated-content-text");
        }
        if (availableEmailEntitlements.length > 0) {
            return t("overlays:gated-content-email-access");
        }
        if (availablePasswordEntitlements.length > 0) {
            return t("overlays:gated-content-password-access");
        }
        return "";
    }, [
        hasOnlySubscriptions,
        availablePurchaseEntitlements.length,
        availableEmailEntitlements.length,
        availablePasswordEntitlements.length,
        t
    ]);

    const btnText = useMemo<string>(() => {
        const playerProducts = collection?.entitlements;
        const broadcastProducts = collectionVideo?.entitlements;

        if (playerProducts?.length === 1 && broadcastProducts?.length === 0) {
            return t("overlays:subscribe");
        }
        if (playerProducts) {
            if (playerProducts.length > 1) {
                return t("overlays:see-options");
            }
            if (playerProducts.length > 0 && collectionVideo?.isEntitled) {
                return t("overlays:more-details");
            }
        }

        return t("overlays:see-options");
    }, [t, collection, collectionVideo]);

    const overlayIcon = useMemo<JSX.Element>(() => {
        if (availablePurchaseEntitlements.length > 0) {
            return <TicketIcon />;
        } else if (
            availableEmailEntitlements.length > 0 ||
            availablePasswordEntitlements.length > 0
        ) {
            return <LockIcon />;
        }
        return <></>;
    }, [
        availablePurchaseEntitlements,
        availableEmailEntitlements,
        availablePasswordEntitlements
    ]);

    return (
        <div className={cx("container")}>
            {poster && <img src={poster} alt="Broadcast Thumbnail" />}

            <Overlay
                show={true}
                icon={overlayIcon}
                text={overlayText}
                collection={collection}
                collectionVideo={collectionVideo}
                btnText={btnText}
                video={video}
            />
        </div>
    );
};
