.container {
    width: 100%;
    height: 100%;
    position: absolute;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    & > div {
        display: flex;
        width: 40%;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 0.75rem;
        text-align: center;

        @include mobile {
            width: 80%;
        }

        & > p {
            margin: 0;
            font-weight: 500;
            size: 18px;
            line-height: 24px;
        }

        & > div {
            font-size: rem(24);
            font-weight: 500;

            word-wrap: break-word;
            white-space: pre-wrap;
            word-break: break-word;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;

            @include mobile {
                font-size: rem(18);
            }
        }

        .overlay-icon {
            & > span > svg {
                width: 54px;
                height: 54px;
                color: white;
            }
        }
    }

    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(rem(4));

    transition: 0.25s border-radius ease-in-out;
    border-radius: rem(16) rem(16) rem(0) rem(0);

    @include mobile {
        border-radius: 0;
    }

    & * {
        color: white;
        border-color: white;
    }

    & > strong {
        cursor: inherit;
        user-select: none;
    }
}

$mini-size: 3rem;

.minimized {
    left: calc(50% - $mini-size/2);
    top: calc(50% - $mini-size/2);
    width: inherit !important;
    height: inherit !important;

    & > div {
        width: $mini-size !important;
        height: $mini-size !important;
        padding: 0.5rem !important;
    }
}

/* Styles for specific player states */
:global(.tool-active) .container {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

:global(.is-embed:not(.is-expanded)) .container {
    border-radius: 0;
}
