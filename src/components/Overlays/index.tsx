import classNames from "classnames/bind";
import styles from "./index.module.scss";
import { useSelector } from "react-redux";
import { RootState } from "store/reducers";
import { VideoBadges } from "../VideoBadges";
import { Button } from "../Buttons/Button";
import { useToggleInteractiveMode } from "../../hooks/useToggleInteractiveMode";
import {
    CollectionResponse,
    CollectionVideoResponse
} from "@switcherstudio/player-api-client";
const cx = classNames.bind(styles);

export interface OverlayWithEntitlements {
    collection: CollectionResponse | undefined;
    collectionVideo?: CollectionVideoResponse | null;
    video: HTMLVideoElement;
}
export interface OverlayProps extends OverlayWithEntitlements {
    show: boolean;
    icon: JSX.Element;
    header?: string;
    text?: string;
    btnText?: string;
}

export const Overlay: React.FC<OverlayProps> = ({
    icon,
    show,
    text,
    collectionVideo,
    btnText,
    video
}) => {
    const { isExpanded, isEmbed } = useSelector(
        (s: RootState) => s.videoSession
    );
    const { toggleInteractive } = useToggleInteractiveMode(video);

    if (!show) {
        return null;
    }

    return (
        <div className={cx("container")}>
            <div>
                {(isExpanded || !isEmbed) && (
                    <>
                        <div className={cx("overlay-icon")}>
                            {icon && <span>{icon}</span>}
                        </div>
                        {text && <p>{text}</p>}
                    </>
                )}
                {isEmbed && !isExpanded && (
                    <>
                        <div>{collectionVideo?.broadcast?.details?.title}</div>
                        <VideoBadges
                            isPlaylist={true}
                            isOverlay={true}
                        ></VideoBadges>
                        {btnText && (
                            <Button
                                text={btnText}
                                onClick={() => toggleInteractive(true)}
                                isOverlay={true}
                            />
                        )}
                    </>
                )}
            </div>
        </div>
    );
};
