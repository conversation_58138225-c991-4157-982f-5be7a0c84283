import Close from "assets/icons/close.svg?react";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
const cx = classNames.bind(styles);

export interface CloseIconProps {
    side?: "left" | "right";
    background?: boolean;
}

export const CloseIcon = ({
    side = "left",
    background = true
}: CloseIconProps) => {
    return <Close className={cx("icon", side, { background })}></Close>;
};
