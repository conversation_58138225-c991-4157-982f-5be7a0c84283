import React, { PropsWithChildren } from "react";
import { useKeyPress } from "hooks/useKeyPress";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import { CloseIcon } from "components/Icons/CloseIcon";
const cx = classNames.bind(styles);

export interface DrawerModalProps extends PropsWithChildren {
    isOpen: boolean;
    onClose: () => void;
}

export const DrawerModal: React.FC<DrawerModalProps> = ({
    children,
    isOpen,
    onClose
}) => {
    useKeyPress("Escape", onClose);

    if (!isOpen) {
        return null;
    }

    return (
        <div className={cx("modal-wrapper")} onClick={onClose}>
            <CloseIcon side="right" />
            <div
                className={cx("modal-body")}
                onClick={(e) => e.stopPropagation()}
            >
                {children}
            </div>
        </div>
    );
};
