.purchase-options-modal {
    // height: 50vh;
    .modal-footer {
        padding: 0;
        border-top: none;
    }
}

.purchase-options-container {
    justify-content: center;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
}
.loader {
    min-width: 100%;
    height: 10vh;
}

.purchase-option {
    margin: rem(24) 0;
    justify-content: center;
    @include wrapTextOverflow();
}

.separator {
    align-self: center;
    font-size: rem(18);
    font-weight: 400;
    display: flex;
    align-items: center;
    text-align: center;
    align-self: center;
    color: var(--interactive-panel-text-color);
    min-width: 90%;
}

.separator::before,
.separator::after {
    content: "";
    flex: 1;
    border-bottom: rem(1) solid var(--interactive-panel-text-color);
}

.separator:not(:empty)::before {
    margin-right: 0.5rem;
}

.separator:not(:empty)::after {
    margin-left: 0.5rem;
}

.payments-not-accepted {
    padding-bottom: 2rem;
}
