.modal-wrapper {
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.24);
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;

    .modal-container {
        background-color: rgb(var(--interactive-panel-background-color-rgb));
        color: rgb(var(--interactive-panel-text-color-rgb));
        border-radius: rem(12);

        max-width: rem(320);
        max-height: 90%;

        display: flex;
        flex-direction: column;
        @include mobile {
            max-width: 90%;
        }

        .modal-header {
            padding: rem(24) rem(16) 0;
            font-weight: 700;
            font-size: rem(18);
            line-height: rem(24);
        }

        .modal-body {
            padding: rem(16) rem(16) 0;

            h4 {
                margin: 1rem;
            }

            p {
                margin-top: 0;
            }
        }

        .modal-footer {
            padding: rem(10) rem(26) rem(26);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: rem(22);
        }
    }
}
