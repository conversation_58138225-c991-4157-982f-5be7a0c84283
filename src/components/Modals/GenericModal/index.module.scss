.modal-wrapper {
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.24);
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100vw;
    height: 100vh;
    button {
        color: rgb(var(--interactive-panel-text-color-rgb));
    }

    .modal-container {
        background-color: rgb(var(--interactive-panel-background-color-rgb));
        border: rem(1) solid
            rgb(var(--interactive-panel-background-contrast-color-rgb), 0.15);
        border-radius: rem(12);
        color: rgb(var(--interactive-panel-text-color-rgb));

        width: 40%;
        max-height: 80%;

        display: flex;
        flex-direction: column;

        @include mobile {
            width: 90%;
            max-height: 90%;
        }

        .modal-header {
            padding: rem(20) rem(24);
            font-weight: 700;
            font-size: rem(18);
            line-height: rem(22);
            border-bottom: rem(1) solid
                rgba(
                    var(--interactive-panel-background-contrast-color-rgb),
                    0.1
                );

            display: flex;
            justify-content: space-between;
            align-items: center;

            .modal-close {
                background: none;
                border: none;
                cursor: pointer;
                display: flex;
                align-items: center;

                svg {
                    transition: 0.25s transform ease-in-out;
                    width: rem(14);
                    height: rem(14);
                    color: rgb(var(--interactive-panel-text-color-rgb));
                }

                &:hover {
                    svg {
                        transform: scale3d(1.1, 1.1, 1);
                        color: var(--interactive-panel-text-color-dark);
                    }
                }
            }
        }

        .modal-body {
            padding: rem(16) rem(24);
            overflow: auto;

            h4 {
                margin: 1rem;
            }

            p {
                margin-top: 0;

                a {
                    color: var(--brand-color-primary);
                    font-weight: 600;
                }
            }
        }

        .body-with-footer {
            padding-bottom: rem(0);
        }

        .modal-footer {
            padding: rem(0) rem(24) rem(16);
            .buttons {
                display: flex;
                justify-content: flex-end;

                button:last-child:not(.full-width) {
                    margin-left: 1rem;
                }

                .full-width {
                    width: 100%;
                    margin-left: 0;
                }
            }

            .footer-link {
                width: 90%;
                margin: 1.5rem 5% 0 5%;
                color: var(--brand-color-primary);
                font-weight: 700;
                font-size: 14px;
                line-height: 20px;
                text-align: center;
            }
        }
    }
    .stripe-checkout-embed {
        max-width: 80%;
        max-height: 80%;
        width: rem(435);
        padding-bottom: rem(6);
        background-color: #fff;
        color: #000;

        .modal-header > button > svg {
            color: #000 !important;
        }

        .modal-body {
            padding: 0;
            border-radius: 1rem;
            transition: background-color 0.2s ease-in-out;
        }

        .modal-body {
            scrollbar-width: thin;
        }

        .modal-footer {
            padding: 0.75rem;
            > div > button {
                background-color: rgba(0, 116, 212) !important;
                border-radius: rem(8);
                font-weight: 550;
            }
        }

        @include mobile {
            max-width: 90%;
            max-height: 90%;
            width: 90%;
        }
    }
}
