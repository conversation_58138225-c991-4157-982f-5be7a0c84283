import QRCode from "react-qr-code";
import { DrawerModal } from "../DrawerModal";

export interface QRCodeModalProps {
    /**The URL link for the QR code */
    link: string | null;
    /**Callback function to close the modal */
    onClose: () => void;
}

/**
 * Renders a QR code modal component.
 */
export const QRCodeModal = ({ link, onClose }: QRCodeModalProps) => {
    return (
        <DrawerModal isOpen={!!link} onClose={onClose}>
            <QRCode size={230} value={link ?? ""} />
        </DrawerModal>
    );
};
