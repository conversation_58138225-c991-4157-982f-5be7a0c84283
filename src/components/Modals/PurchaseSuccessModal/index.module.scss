.success-modal-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    &.isLoading {
        & > :first-child {
            // loading spinner
            position: absolute;
            circle {
                stroke: black;
            }
        }

        *:not(:first-child) {
            // Hide children (except spinner) but keep the height
            opacity: 0;
        }
    }

    & p {
        text-align: center;
    }
}
