import { WebLinkButton } from "components/Buttons/WebLinkButton";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "store/reducers";
import styles from "./index.module.scss";
import { WebLinkResponse } from "@switcherstudio/player-api-client";
import useCollectionWithVideos from "hooks/useCollectionWithVideos";

export interface WebLinkGroupProps {
    setLink: (link: string) => void;
    purchased?: boolean;
    level?: "player" | "broadcast";
}

export const WebLinkGroup = ({
    setLink,
    purchased = false,
    level = "player"
}: WebLinkGroupProps) => {
    const { collection } = useCollectionWithVideos();

    const { currentCollectionVideo } = useSelector(
        (s: RootState) => s.videoSession
    );

    const [links, setLinks] = useState<WebLinkResponse[]>([]);

    useEffect(() => {
        const getLinks = async () => {
            if (level === "player" && !!collection?.webLinks) {
                setLinks(collection?.webLinks);
            }

            if (
                level === "broadcast" &&
                !!currentCollectionVideo?.broadcast?.webLinks
            ) {
                setLinks(currentCollectionVideo?.broadcast?.webLinks);
            }
        };

        getLinks();
    }, [currentCollectionVideo, level, purchased, collection]);

    if (!links || links.length === 0) return null;

    return (
        <div className={styles["weblinks"]}>
            {links?.map((link) => (
                <WebLinkButton
                    onQRClick={() => setLink(link?.details?.link ?? "")}
                    key={link?.details?.id}
                    link={link}
                />
            ))}
        </div>
    );
};
