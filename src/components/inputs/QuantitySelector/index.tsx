import { useEffect } from "react";
import TrashIcon from "assets/icons/trash.svg?react";
import PlusIcon from "assets/icons/plus.svg?react";
import MinusIcon from "assets/icons/minus.svg?react";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
const cx = classNames.bind(styles);

interface QuantitySelectorProps {
    quantity: number;
    setQuantity: (qty: number) => void;
    onTrash?: () => void;
    maxQuantity?: number;
    onQuantityReduced?: (newQty: number) => void;
}
export const QuantitySelector = ({
    quantity,
    setQuantity,
    maxQuantity,
    onTrash,
    onQuantityReduced = () => {}
}: QuantitySelectorProps) => {
    const incrementQuantity = () => {
        const newQty = quantity + 1;
        if (!maxQuantity || newQty <= maxQuantity) {
            setQuantity(quantity + 1);
        }
    };
    const decrementQuantity = () => {
        if (quantity >= 2) {
            setQuantity(quantity - 1);
        }
    };

    useEffect(() => {
        if (!!maxQuantity) {
            if (quantity > maxQuantity) {
                setQuantity(maxQuantity);
                onQuantityReduced(maxQuantity);
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [quantity, maxQuantity, setQuantity]);

    const decrementorDisabled = quantity <= 1 && !onTrash;
    const incrementorDisabled = !!maxQuantity && quantity >= maxQuantity;

    return (
        <div className={cx("selector-row")}>
            <button
                className={cx("selector-button", {
                    "disabled": decrementorDisabled
                })}
                onClick={
                    quantity <= 1 && !!onTrash ? onTrash : decrementQuantity
                }
                aria-disabled={decrementorDisabled}
                disabled={decrementorDisabled}
            >
                {quantity <= 1 && !!onTrash ? <TrashIcon /> : <MinusIcon />}
            </button>

            <div className={cx("selector-quantity-display")}>{quantity}</div>

            <button
                className={cx("selector-button", {
                    "disabled": incrementorDisabled
                })}
                onClick={incrementQuantity}
                aria-disabled={incrementorDisabled}
                disabled={incrementorDisabled}
            >
                <PlusIcon />
            </button>
        </div>
    );
};
