.selector-row {
    display: flex;
    flex-flow: row nowrap;
    justify-content: flex-start;
    align-items: center;
    gap: 4ox;

    .selector-button {
        transition: 0.25s background-color ease-in-out;
        width: rem(32);
        height: rem(32);
        display: flex;
        align-items: center;
        justify-content: center;

        border: none;
        background-color: rgba(
            var(--interactive-panel-background-contrast-color-rgb),
            0.06
        );
        border-radius: rem(22);

        cursor: pointer;

        svg {
            width: rem(12);
            height: rem(12);
            * {
                fill: rgba(var(--interactive-panel-text-color-rgb));
            }
        }

        &.disabled {
            cursor: not-allowed;
            opacity: 0.32;
        }

        &:hover {
            background-color: rgba(
                var(--interactive-panel-background-contrast-color-rgb),
                0.1
            );
        }
    }

    .selector-quantity-display {
        width: rem(32);
        font-weight: 400;
        font-size: rem(14);
        line-height: rem(20);
        text-align: center;
        color: rgba(var(--interactive-panel-text-color-rgb));
    }
}
