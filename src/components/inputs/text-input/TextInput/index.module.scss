.input-container {
    & > label {
        font-size: rem(14);
        font-weight: 700;
    }

    & > div {
        margin: 1rem 0;
    }
}

.input-wrapper {
    border: none;
    display: flex !important;
    flex-wrap: nowrap;
    border-radius: rem(8);

    &:focus-visible {
        outline: none !important;
    }

    & > input {
        padding: 1rem;
        width: 100%;
        border-radius: rem(8);
        border: rem(2) solid #c1c7cd;

        &:focus-visible {
            outline: none !important;
        }

        &:focus {
            border-color: var(--brand-color-primary-dark);
        }
    }
}

.toast {
    background-color: #f8d7da;
    padding: 12px 24px;
    text-align: center;
    margin-top: 12px;
    border-radius: 8px;
    transition: opacity 0.3s ease-in-out;
    position: relative;
}

.error-text {
    color: #721c24;
}
