.search-bar-container {
    width: rem(419);
    .search-bar {
        color: rgba(var(--embed-text-color-rgb));
    }
}

.submit-button,
.reset-button {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;

    width: 3.4rem;
    height: 3.4rem;

    background-color: transparent;

    border: none;

    svg {
        width: 24px;
        height: 24px;
    }
}

.submit-button {
    border-radius: rem(8);
    border-left: rem(1) solid rgba(var(--embed-text-color-rgb));

    svg {
        width: 32px;
        height: 32px;
    }

    &:hover {
        background-color: rgba(var(--embed-text-color-rgb), 0.2);
    }
}

.input-container {
    .search-label {
        color: rgba(var(--embed-text-color-rgb));
        font-size: rem(14);
        font-weight: 400;
    }
    & > div {
        margin: 0.25rem 0;
    }
}

.input-wrapper {
    display: grid !important;
    grid-template-columns: 1fr auto auto;
    border-radius: rem(8);
    position: relative;
    background-color: white;
    border: rem(1) solid rgba(var(--embed-text-color-rgb));

    &:focus-visible {
        outline: none !important;
    }

    & > input {
        padding: 1rem;

        border-radius: rem(8);
        border: none;

        &:focus-visible {
            outline: none !important;
        }

        &:focus {
            border-color: var(--brand-color-primary-dark);
        }

        &::-webkit-search-cancel-button {
            display: none;
        }
    }
}
