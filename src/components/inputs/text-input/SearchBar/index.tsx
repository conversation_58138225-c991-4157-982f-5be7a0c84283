import React, { ChangeEvent, useCallback, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import SearchIcon from "assets/icons/search.svg?react";
import ResetIcon from "assets/icons/close.svg?react";
import styles from "./index.module.scss";
import { eventBus } from "helpers/eventBus";

export interface SearchBarProps {
    value: string;
    error?: string;
    onChange: (event: ChangeEvent<HTMLInputElement>) => void;
    isLoading: boolean;
    label?: string;
    onSubmit?: (
        event:
            | React.MouseEvent<HTMLButtonElement>
            | React.KeyboardEvent<HTMLInputElement>
    ) => void;
    onReset?: () => void;
    showReset?: boolean;
}

export const SearchBar: React.FC<SearchBarProps> = ({
    value,
    error,
    onChange,
    isLoading,
    onSubmit,
    onReset,
    showReset = false
}) => {
    const { t } = useTranslation();

    const handleFocus = useCallback(() => {
        eventBus.emit("disableKeyboardControls");
    }, []);

    const handleBlur = useCallback(() => {
        eventBus.emit("enableKeyboardControls");
    }, []);

    const inputRef = useRef<HTMLInputElement>(null);

    // utilized to attach another event to the reset/clear button
    useEffect(() => {
        const input = inputRef.current;
        if (input && onReset) {
            input.addEventListener("search", onReset);
            return () => {
                input.removeEventListener("search", onReset);
            };
        }
    }, [onReset]);

    const handleSubmit = useCallback(
        (
            event:
                | React.MouseEvent<HTMLButtonElement>
                | React.KeyboardEvent<HTMLInputElement>
        ) => {
            event.preventDefault();

            if (onSubmit) {
                onSubmit(event);
            }
        },
        [onSubmit]
    );

    const handleReset = useCallback(
        (
            event:
                | React.MouseEvent<HTMLButtonElement>
                | React.KeyboardEvent<HTMLInputElement>
        ) => {
            event.preventDefault();

            if (onReset) {
                onReset();
            }
        },
        [onReset]
    );

    return (
        <div className={styles["search-bar-container"]}>
            <div
                className={`${styles["input-container"]}`}
                onFocus={handleFocus}
                onBlur={handleBlur}
            >
                <label htmlFor="searchbar" className={styles["search-label"]}>
                    {t("search-bar:label")}
                </label>
                <div className={styles["input-wrapper"]}>
                    <input
                        aria-label="Search"
                        ref={inputRef}
                        type="search"
                        id="searchbar"
                        value={value || ""}
                        onChange={onChange}
                        disabled={isLoading}
                        onKeyDown={(e) => {
                            if (e.key === "Enter" && onSubmit) {
                                onSubmit(e);
                            }
                        }}
                    />
                    {showReset && (
                        <button
                            className={styles["reset-button"]}
                            type="reset"
                            value="Reset"
                            title="Reset"
                            disabled={isLoading}
                            onClick={handleReset}
                        >
                            <ResetIcon />
                        </button>
                    )}
                    <button
                        className={styles["submit-button"]}
                        type="submit"
                        value="Search"
                        title="Search"
                        disabled={isLoading}
                        onClick={handleSubmit}
                    >
                        <SearchIcon />
                    </button>
                </div>

                <div className={error ? styles["toast"] : ""}>
                    <small
                        id={`searchbar-help`}
                        aria-describedby="searchbar"
                        className={error ? styles["error-text"] : ""}
                    >
                        {error}
                    </small>
                </div>
            </div>
        </div>
    );
};
