$drawer-height: rem(36);

.background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    pointer-events: none;
    background-color: transparent;
    transition: background-color 0.3s ease-out;

    &.isOpen {
        pointer-events: initial;
        background-color: rgba(0, 0, 0, 0.24);
    }
}

.drawer {
    position: fixed;
    bottom: -100%;
    left: 0;
    width: 100%;

    display: flex;
    flex-direction: column;
    align-items: center;

    box-sizing: border-box;
    padding: rem(40);
    padding-top: 0;
    border-radius: rem(40) rem(40) 0 0;

    color: rgb(var(--interactive-panel-text-color-rgb));
    background: rgb(var(--interactive-panel-background-color-rgb));
    transition: bottom 0.5s ease-out;

    &.isOpen {
        bottom: 0;
    }

    & > span {
        width: 100%;
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        justify-content: space-between;

        & h3 {
            margin: 2rem 0;
        }
    }

    & svg {
        cursor: pointer;
    }

    hr {
        border: solid rem(1)
            rgba(var(--interactive-panel-background-contrast-color-rgb), 0.1);
        border-radius: rem(12);
    }
}
