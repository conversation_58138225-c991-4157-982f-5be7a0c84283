import classNames from "classnames/bind";
import { PropsWithChildren, useRef } from "react";
import { useClickOutside } from "hooks/useClickOutside";
import Close from "assets/icons/close.svg?react";
import styles from "./index.module.scss";
const cx = classNames.bind(styles);

export interface DrawerProps extends PropsWithChildren {
    /** Text to be shown in header */
    header: string;
    /** determines if drawer is open */
    isOpen: boolean;
    /** to be called when drawer is closed */
    onClose: () => void;
}

export const Drawer = ({ header, isOpen, onClose, children }: DrawerProps) => {
    const ref = useRef(null);
    useClickOutside(ref, onClose);

    return (
        <div className={cx("background", { isOpen })}>
            <div className={cx("drawer", { isOpen })} ref={ref}>
                <span>
                    <h3>{header}</h3>
                    <Close onClick={onClose} />
                </span>
                <hr />
                {children}
            </div>
        </div>
    );
};
