import { Button } from "components/Buttons/Button";
import { Card } from "components/Cards/Card";
import { displayAmount } from "helpers/currency";
import { useCallback, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { Drawer } from "../Drawer";
import styles from "./index.module.scss";

export interface RequestAccessDrawerProps {
    /**  */
    header: string;
    description: string;
    type: "pay-per-view";
    amount: number | undefined;
    isOpen: boolean;
    onClose: () => void;
}

export const RequestAccessDrawer = ({
    header,
    description,
    type,
    amount,
    isOpen,
    onClose
}: RequestAccessDrawerProps) => {
    const { t } = useTranslation();

    const _displayAmount = useMemo<string>(
        () =>
            amount
                ? displayAmount(amount, { signed: true, compact: false })
                : "$0",
        [amount]
    ); // Should we account for "free" videos (content is free, but viewer have to enter email/phone)

    const toPayment = useCallback(() => {
        console.log("opens checkout");
    }, []);

    const restorePurchase = useCallback(() => {
        console.log("restores purchases");
    }, []);

    return (
        <Drawer header={header} isOpen={isOpen} onClose={onClose}>
            <div className={styles["contents"]}>
                <p>{description}</p>
                <Card className={styles["pricing-card"]}>
                    <p>{t(`labels:${type}`)}</p>
                    <strong>{_displayAmount}</strong>
                </Card>
                <Button
                    text={t("buttons:continue-to-payment")}
                    onClick={toPayment}
                />
                <span>
                    <p>{t("prompts:restore-purchase")}</p>
                    <Button
                        text={t("buttons:restore-purchase")}
                        onClick={restorePurchase}
                        type="link"
                    />
                </span>
            </div>
        </Drawer>
    );
};
