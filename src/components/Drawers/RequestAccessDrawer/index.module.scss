.contents {
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    gap: 2rem;

    & p {
        margin: 0;
    }

    & > span {
        text-align: center;
        & > p {
            color: #0000008f;
        }
    }
}

.pricing-card {
    width: 100%;
    display: flex;
    flex-direction: row !important;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: space-between;

    & > p {
        margin: 0;
    }
}
