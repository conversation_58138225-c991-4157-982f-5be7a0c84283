import { useEffect, useState, useMemo, Suspense } from "react";
import Loading from "../loading/Loading";
import { VideoPlayerPlaceholderBackground } from "../Placeholder";
import { setToolState } from "../../store/VideoSession/slice";
import { ToolDrawer } from "components/ToolDrawer";
import ToolToggle from "assets/icons/tool-toggle.svg?react";
import ToolChevronPortrait from "assets/icons/chevron-up.svg?react";
import ToolChevronLandscape from "assets/icons/chevron-left.svg?react";
import CloseIcon from "assets/icons/close.svg?react";
import { useDispatch } from "react-redux";
import { AppDispatch } from "store/store";
import { useSelector } from "react-redux";
import { RootState } from "store/reducers";
import { VideoOnDemand } from "components/VideoOnDemand";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import { useToggleInteractiveMode } from "hooks/useToggleInteractiveMode";
import { useGatedContent } from "hooks/useGatedContent";
import React from "react";
import { getCorrectedAspectRatioThumbnailURL } from "helpers/imageHelpers";
import { useIsMobile } from "hooks/useIsMobile";
import useHandleUpcomingPolling from "components/EmbeddedVideoPlayer/hooks/useHandleUpcomingPolling";
import { useHandleDefaultState } from "components/EmbeddedVideoPlayer/hooks/useHandleDefaultState";
import useCollectionWithVideos from "hooks/useCollectionWithVideos";
import { useCollectionVideoThumbnail } from "hooks/useCollectionVideoThumbnail";
import { IFrameType } from "store/VideoSession/types";
import { useMediaQuery } from "hooks/useMediaQuery";
import { Image } from "components/Image";
import useHandleProcessingPolling from "./hooks/useHandleProcessingPolling";
import { NotificationContainer } from "components/notification/NotificationContainer";
const cx = classNames.bind(styles);

const ShakaPlayer = React.lazy(() => import("../ShakaPlayer"));

export interface VideoUrl {
    dash: string | undefined;
    hls: string | undefined;
}

export const EmbeddedVideoPlayer = () => {
    const dispatch = useDispatch<AppDispatch>();
    const {
        interactiveNotifications: { notifications }
    } = useSelector((s: RootState) => s.notifications);

    // Load data from store
    const {
        isToolDrawerActive,
        currentCollectionVideo,
        isEmbed,
        isExpanded,
        playHasStarted,
        isEnded,
        hasSetDefault,
        iframeType
    } = useSelector((s: RootState) => s.videoSession);
    const { hasLoadingError } = useSelector((s: RootState) => s.catalogState);
    const { collection, onDemandVideos, hasLoaded } = useCollectionWithVideos();
    // set local state variables
    const [isToggleable, setIsToggleable] = useState<boolean>(true);
    const { isMobile, isLandscapeMobile, isPortraitMobile } = useIsMobile();
    const isTablet = useMediaQuery({ maxWidth: 915, minWidth: 720 });
    const isSmallTablet = useMediaQuery({ maxWidth: 720, minWidth: 600 });

    const isDisabled = !collection?.creatorIsValid;

    const broadcastIsFeatured = useMemo<boolean | undefined>(
        () =>
            !collection && !currentCollectionVideo
                ? false
                : collection?.details?.idleState === "SelectVideo" &&
                  collection?.details?.idleBroadcastId ===
                      currentCollectionVideo?.broadcast?.details?.id,
        [currentCollectionVideo, collection]
    );

    const {
        playlistBroadcastIsGated,
        playlistBroadcastIsEntitled,
        playerIsGated,
        playerIsEntitled
    } = useGatedContent();
    const { toggleInteractive } = useToggleInteractiveMode();
    const [videoUrl, setVideoUrl] = useState<VideoUrl>();
    const [videoPoster, setVideoPoster] = useState<string>();

    /** if user defaulted interactive mode, open player in interactive mode on initial play (only) */
    useEffect(() => {
        if (
            isEmbed &&
            playHasStarted &&
            collection?.details?.autoOpenInteractiveMode &&
            iframeType === IFrameType.Main &&
            isToggleable
        ) {
            toggleInteractive(true);
            setIsToggleable(false);
        }
    }, [
        toggleInteractive,
        playHasStarted,
        collection?.details?.autoOpenInteractiveMode,
        iframeType,
        isEmbed,
        isToggleable
    ]);

    // handle the default state initial load
    useHandleDefaultState();

    // begin polling for latest upcoming data
    useHandleUpcomingPolling();

    // begin polling for processing data
    useHandleProcessingPolling();

    // handle currentCollectionVideo switch
    const [isLiveProcessing, setIsLiveProcessing] = useState<boolean>(false);
    const [resetKey, setResetKey] = useState<number>(0);

    useEffect(() => {
        if (!hasSetDefault) return;

        const video = currentCollectionVideo?.broadcast?.videos?.[0];

        // set if current video is processing but the user was watching it.
        setIsLiveProcessing(
            currentCollectionVideo?.broadcast?.details?.broadcastStatus ===
                "Ended" && video?.details?.status === "live-inprogress"
        );

        const hls = video?.details?.playback?.hls;
        const dash = video?.details?.playback?.dash;
        setVideoUrl({ hls, dash } as VideoUrl);

        setVideoPoster(
            currentCollectionVideo?.broadcast?.thumbnail?.details?.url ??
                video?.details?.thumbnail
        );

        // if the video is processing, we need to set the resetKey to force a re-render
        if (isLiveProcessing) {
            //console.log("RESET KEY TO RELOAD COMPONENT");
            setResetKey((prevKey) => prevKey + 1);
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [dispatch, currentCollectionVideo, hasSetDefault]);

    const isLoading = useMemo<boolean>(
        () => !hasLoaded || !hasSetDefault,
        [hasLoaded, hasSetDefault]
    );

    const displayError = useMemo<boolean>(
        () =>
            !isLoading &&
            currentCollectionVideo == null &&
            !isDisabled &&
            hasLoadingError,
        [isLoading, currentCollectionVideo, isDisabled, hasLoadingError]
    );
    const displayNoVideos = useMemo<boolean>(
        () =>
            (currentCollectionVideo == null ||
                currentCollectionVideo?.broadcast?.details?.id ===
                    "00000000-0000-0000-0000-000000000000") &&
            !isLoading &&
            !isDisabled &&
            !displayError,
        [isLoading, currentCollectionVideo, isDisabled, displayError]
    );

    const expandButtonText = useMemo(() => {
        switch (collection?.details?.defaultInteractiveTab) {
            case "About":
                return "Details";
            case "Products":
                return currentCollectionVideo?.broadcast?.details
                    ?.enableLiveShopping
                    ? "Shop"
                    : "Details";
            case "VoD":
                return "Collection";
            default:
                return "Details";
        }
    }, [collection?.details?.defaultInteractiveTab, currentCollectionVideo]);

    const broadcastIsRestricted = useMemo<boolean>(
        () => !!playlistBroadcastIsGated && !playlistBroadcastIsEntitled,
        [playlistBroadcastIsEntitled, playlistBroadcastIsGated]
    );
    const isProcessing = useMemo<boolean>(
        () =>
            hasLoaded &&
            !currentCollectionVideo?.broadcast?.videos?.[0]?.details
                ?.readyToStream &&
            currentCollectionVideo?.broadcast?.videos?.[0]?.details?.status !==
                "live-inprogress",
        [currentCollectionVideo, hasLoaded]
    );

    const thumbnailVariant = useMemo(() => {
        if (isTablet && !isSmallTablet) return "compact";
        return "full";
    }, [isTablet, isSmallTablet]);

    const { isScheduled, scheduledThumbnail, thumbnail } =
        useCollectionVideoThumbnail({
            collectionVideo: currentCollectionVideo,
            variant: thumbnailVariant
        });

    const resizedPoster = useMemo(
        () => getCorrectedAspectRatioThumbnailURL(videoPoster, 1920),
        [videoPoster]
    );

    const drawerIcon = useMemo(() => {
        // if not mobile view, use drawer icon
        if (!isMobile) return <ToolToggle />;

        // if player is in mobile view and is landscape, use left/right chevron icon
        if (isLandscapeMobile) return <ToolChevronLandscape />;

        // if player is in mobile view and is portrait, use up/down chevron icon
        return <ToolChevronPortrait />;
    }, [isMobile, isLandscapeMobile]);

    /** Automatically opens the tool drawer in specific scenarios:
     *
     * 1. When in portrait mobile view (except for scheduled content)
     * 2. When content is gated/restricted and the user isn't entitled to view it
     *
     * This automatic behavior ensures users can access paywall information or
     * additional content details when needed, particularly on smaller screens
     * where UI space is limited. Works in tandem with the showToolDrawerToggle
     * function that controls the visibility of the toggle icon.
     */
    useEffect(() => {
        // Exit conditions - don't open drawer if any of these are true
        const drawerAlreadyOpen = isToolDrawerActive;
        const notPortraitMobileView = !isPortraitMobile;
        const isScheduledContentOnMobile = isPortraitMobile && isScheduled;

        if (
            drawerAlreadyOpen ||
            notPortraitMobileView ||
            isScheduledContentOnMobile
        ) {
            return;
        }

        // Conditions that trigger automatic drawer opening
        const contentIsRestricted = broadcastIsRestricted;
        const playerContentIsGatedAndNotAccessible =
            playerIsGated && !playerIsEntitled && !broadcastIsFeatured;

        // If any opening condition is met, automatically open the drawer
        if (contentIsRestricted || playerContentIsGatedAndNotAccessible) {
            dispatch(setToolState(true));
        }
    }, [
        playerIsGated,
        playerIsEntitled,
        dispatch,
        broadcastIsRestricted,
        isToolDrawerActive,
        isPortraitMobile,
        isScheduled,
        broadcastIsFeatured
    ]);

    /**
     * This function determines whether to display the tool drawer toggle icon.
     * There are key differences between mobile and desktop display due to the size
     * of the screen and design considerations. On portrait mobile when the drawer
     * is closed, a button with text replaces this toggle. The toggle also respects
     * content restrictions and gating status.
     *
     * NOTE: The useEffect above controls actual tool drawer opening behavior, which
     * might automatically reopen the drawer in certain conditions even when the
     * toggle is clicked.
     */
    const showToolDrawerToggle = useMemo(() => {
        // Don't show if player is disabled
        if (isDisabled) return false;

        // Don't show for restricted content unless it's scheduled
        if (broadcastIsRestricted && !isScheduled) return false;

        // On portrait mobile when drawer is closed, a text button replaces this toggle
        if (isPortraitMobile && !isToolDrawerActive) return false;

        // if viewing on desktop and the player is configured for portrait, then don't show the toggle
        if (!isMobile && collection?.details?.aspectRatio === "NineBySixteen")
            return false;

        // Skip remaining checks if we know we should show the toggle
        // in expanded mode or when not in embed mode
        if (isExpanded || !isEmbed) return true;

        // Don't show if content is gated and not accessible
        const contentIsNotGated = !playerIsGated && !playlistBroadcastIsGated;
        const playerContentIsAccessible = playerIsGated && playerIsEntitled;
        const playlistContentIsAccessible =
            playlistBroadcastIsGated && playlistBroadcastIsEntitled;

        // Don't show if all content access conditions fail
        if (
            !(
                contentIsNotGated ||
                playerContentIsAccessible ||
                playlistContentIsAccessible
            )
        )
            return false;

        // Default case - if we've passed all checks, show the toggle
        return true;
    }, [
        isMobile,
        collection,
        broadcastIsRestricted,
        isDisabled,
        isScheduled,
        isExpanded,
        isEmbed,
        playerIsGated,
        playerIsEntitled,
        playlistBroadcastIsGated,
        playlistBroadcastIsEntitled,
        isPortraitMobile,
        isToolDrawerActive
    ]);

    /** This button expands the player to show interactive mode or toggles the tool drawer
     * in portrait mobile view. It's replaced with "Subscribe" text when the video is
     * featured, gated, and the user isn't entitled.
     */
    const showExpandButton = useMemo(() => {
        // Don't show while content is loading
        if (isLoading) return false;

        // Only show in embed mode OR when viewing in portrait mobile (even without embed)
        const properDisplayMode = isEmbed || (!isEmbed && isPortraitMobile);
        if (!properDisplayMode) return false;

        // Only show if player is NOT expanded OR (in portrait mobile AND tool drawer is closed)
        const properExpandState =
            !isExpanded || (isPortraitMobile && !isToolDrawerActive);
        if (!properExpandState) return false;

        // Don't show if content is gated and user isn't entitled, UNLESS it's scheduled
        if (
            playlistBroadcastIsGated &&
            !playlistBroadcastIsEntitled &&
            !isScheduled
        )
            return false;

        // Default to showing the expand button if all conditions are met
        return true;
    }, [
        isLoading,
        isEmbed,
        isPortraitMobile,
        isExpanded,
        isToolDrawerActive,
        playlistBroadcastIsGated,
        playlistBroadcastIsEntitled,
        isScheduled
    ]);

    /**
     * Determines whether to display the video player component.
     * The player is shown only when there's valid video content that's ready
     * to stream, not scheduled for future release, and not in processing state.
     * This ensures we only mount the player when content can actually be played.
     */
    const showVideoPlayer = useMemo(() => {
        // Don't show if there's no current video selected
        if (currentCollectionVideo == null) return false;

        // Don't show if the player is disabled (invalid creator)
        if (isDisabled) return false;

        // Don't show for scheduled content (upcoming broadcasts)
        if (isScheduled) return false;

        // Don't show for content still being processed
        if (isProcessing) return false;

        // Default case - show the video player if all checks pass
        return true;
    }, [currentCollectionVideo, isDisabled, isScheduled, isProcessing]);

    /**
     * Determines whether to display the tool drawer component.
     * The drawer contains additional content and controls like chat, product info,
     * and other interactive elements. It's conditionally shown based on player
     * state and embed mode.
     */
    const showToolDrawer = useMemo(() => {
        // Don't show if player is disabled (invalid creator)
        if (isDisabled) return false;

        // Only show in expanded interactive mode or when not in embed mode
        if (!(isExpanded || !isEmbed)) return false;

        // Default case - show the tool drawer if all checks pass
        return true;
    }, [isDisabled, isExpanded, isEmbed]);

    /**
     * Determines whether to display the Video On Demand component.
     * This component shows playlists of available videos. It's shown in expanded
     * or non-embed mode, with additional conditions for mobile display that depend
     * on content gating status.
     */
    const showVideoOnDemand = useMemo(() => {
        // Don't show if player is disabled
        if (isDisabled) return false;

        // Only show in expanded mode or when not in embed mode
        if (!(isExpanded || !isEmbed)) return false;

        // On mobile, only show when content is gated (either playlist or player)
        if (isMobile && !playlistBroadcastIsGated && !playerIsGated)
            return false;

        // Default case - show VOD component if all checks pass
        return true;
    }, [
        isDisabled,
        isExpanded,
        isEmbed,
        isMobile,
        playlistBroadcastIsGated,
        playerIsGated
    ]);

    if (isLoading) return <Loading />;

    return (
        <div className={cx("embedded-video-player")} key={resetKey}>
            <div
                className={cx("video-player-container", {
                    "player-disabled": isDisabled,
                    "is-vertical":
                        collection?.details?.aspectRatio === "NineBySixteen"
                })}
            >
                <div className={cx("video-player")}>
                    {isExpanded && isEmbed && (
                        <button
                            className={cx("interactive-collapse-button")}
                            onClick={() => toggleInteractive(false)}
                        >
                            <CloseIcon />
                        </button>
                    )}

                    <NotificationContainer
                        visible={notifications.length > 0}
                        notifications={notifications}
                        shouldFade={notifications.every(
                            (n) =>
                                n?.fadeMilliseconds && n?.fadeMilliseconds > 0
                        )}
                        position="bottom-left"
                    />

                    <VideoPlayerPlaceholderBackground>
                        <>
                            {displayError && (
                                <div className={cx("error-message")}>
                                    There was an issue retrieving the latest
                                    video data.
                                </div>
                            )}

                            {isDisabled && (
                                <div className={cx("offline-message")}>
                                    The content is currently unavailable.
                                </div>
                            )}

                            {displayNoVideos && (
                                <div className={cx("offline-message")}>
                                    <b>New content coming soon</b>
                                    {(onDemandVideos?.length ?? 0) > 0 && (
                                        <>
                                            <br />
                                            Watch saved videos on our playlist.
                                        </>
                                    )}
                                </div>
                            )}

                            {isProcessing && (
                                <div className={cx("countdown-container")}>
                                    <Image
                                        src={thumbnail}
                                        alt="countdown thumbnail"
                                        className={cx("countdown-thumbnail")}
                                    />
                                    <div
                                        className={cx(
                                            "countdown-thumbnail",
                                            "future"
                                        )}
                                    >
                                        <div className={cx("offline-message")}>
                                            {`This ${
                                                !!currentCollectionVideo
                                                    ?.broadcast?.details
                                                    ?.inputId
                                                    ? "livestream has ended and"
                                                    : "video"
                                            } is
                                                currently processing.`}
                                            <br /> Please check back soon to
                                            watch it on-demand.
                                        </div>
                                    </div>
                                </div>
                            )}

                            {isScheduled && (
                                <div className={cx("countdown-container")}>
                                    <Image
                                        src={thumbnail}
                                        alt="countdown thumbnail"
                                        className={cx("countdown-thumbnail")}
                                    />
                                    <div
                                        className={cx(
                                            "countdown-thumbnail",
                                            "future"
                                        )}
                                    >
                                        {scheduledThumbnail}
                                    </div>
                                </div>
                            )}
                        </>
                    </VideoPlayerPlaceholderBackground>

                    {showExpandButton && (
                        <button
                            className={cx(
                                "btn",
                                broadcastIsFeatured &&
                                    playerIsGated &&
                                    !playerIsEntitled
                                    ? "subscribe-button"
                                    : "interactive-expand-button"
                            )}
                            onClick={() =>
                                isExpanded
                                    ? dispatch(
                                          setToolState(!isToolDrawerActive)
                                      )
                                    : toggleInteractive(true)
                            }
                        >
                            {(broadcastIsFeatured || isScheduled) &&
                            playerIsGated &&
                            !playerIsEntitled
                                ? "Subscribe"
                                : expandButtonText}
                        </button>
                    )}

                    {showToolDrawerToggle && (
                        <div
                            className={cx("tool-drawer-toggle", {
                                "video-is-ended": isEnded
                            })}
                            onClick={() => {
                                dispatch(setToolState(!isToolDrawerActive));
                            }}
                        >
                            {drawerIcon}
                        </div>
                    )}

                    {showVideoPlayer && (
                        <Suspense fallback={<></>}>
                            <ShakaPlayer
                                key={videoUrl?.dash}
                                src={videoUrl}
                                poster={resizedPoster}
                                collection={collection}
                                isLive={
                                    currentCollectionVideo?.broadcast?.details
                                        ?.broadcastStatus === "Active"
                                }
                                broadcastIsRestricted={broadcastIsRestricted}
                            />
                        </Suspense>
                    )}
                </div>

                {showToolDrawer && <ToolDrawer />}
            </div>
            {showVideoOnDemand && <VideoOnDemand></VideoOnDemand>}
        </div>
    );
};
