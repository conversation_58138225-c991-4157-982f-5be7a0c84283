@mixin top-button {
    position: absolute;
    left: 50%;

    background-color: rgba(#fff, 0.6);
    color: #000;

    &:hover {
        background-color: rgba(#fff, 1);
    }

    backdrop-filter: blur(rem(8));
    transform: translateX(-50%) translateZ(0);
    top: rem(16);
    z-index: 10;
    white-space: nowrap;
    transition: 0.25s background-color ease-in-out;
    cursor: pointer;
    display: flex;
    gap: rem(12);
    align-items: center;
    text-transform: uppercase;
}

.embedded-video-player {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    &:has(.is-vertical) {
        @media screen and (min-width: 952px) {
            width: calc(
                (100vh * 9 / 16) + 454px
            ); // Width to fit 9:16 video + 454px sidebar
            max-width: 100%;
            left: 50%;
            transform: translateX(-50%); // Center the component
        }
    }

    .video-player-container {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        padding: rem(16);

        @include mobile {
            padding: 0;
        }
    }

    .error-message,
    .offline-message {
        font-size: rem(22);
        line-height: rem(28);
        text-align: center;
        color: white;
    }

    .countdown-container {
        display: block;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        position: absolute;
        overflow: hidden;

        .countdown-thumbnail {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: contain;
            background-color: #000;

            &.future {
                display: flex;
                justify-content: center;
                align-items: center;

                background: rgba(0, 0, 0, 0.4);
                backdrop-filter: blur(rem(4));

                svg {
                    height: 100%;
                    width: 80%;
                    & > * {
                        fill: white;
                    }
                }
            }
        }
    }

    .video-player {
        width: 100%;
        height: 100%;
        position: relative;

        transition: 0.3s width ease-in-out;

        @include mobile-portrait {
            transition: 0.3s height ease-in-out;
        }

        .interactive-expand-button {
            @include top-button;

            svg {
                width: rem(20);
                height: rem(20);
            }

            &:after {
                display: block;
            }
        }

        .interactive-collapse-button {
            position: absolute;
            top: rem(29);
            left: rem(29);
            color: #fff;
            z-index: 10;
            background: none;
            border: none;
            padding: 0;
            cursor: pointer;

            display: flex;
            justify-content: center;
            align-items: center;
            background: rgba(12, 12, 14, 0.32);
            border-radius: rem(20);
            width: rem(40);
            height: rem(40);

            @include mobile {
                top: rem(10);
                left: rem(12);
            }

            svg {
                transition: 0.25s transform ease-in-out;
                width: rem(14);
                height: rem(14);
            }

            &:hover {
                svg {
                    transform: scale3d(1.1, 1.1, 1);
                }
            }
        }

        .subscribe-button {
            @include top-button;
        }

        .tool-drawer-toggle {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            right: rem(27);
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            border-radius: rem(50);
            z-index: 10;
            cursor: pointer;
            font-weight: bold;
            background: rgba(12, 12, 14, 0.32);
            border-radius: rem(20);
            width: rem(40);
            height: rem(40);

            @include mobile {
                top: rem(10);
                right: rem(12);
                transform: none;
            }

            &.video-is-ended {
                top: rem(10);
                right: rem(27);
                transform: none;

                @include mobile {
                    right: rem(12);
                }
            }

            &:hover {
                svg {
                    @include hover-glow-shadow();
                }

                svg {
                    width: rem(18);
                    height: rem(12);
                    @include prep-glow-shadow();
                }
            }
        }
    }

    .lite-player {
        > div {
            border-radius: 0;
        }
    }
}

/* Styles for specific player states */
:global(.is-embed:not(.is-expanded)) {
    .embedded-video-player .video-player-container {
        padding: 0;
    }

    @include mobile-in-global {
        .embedded-video-player .video-player-container {
            .tool-drawer-toggle {
                display: none;
            }
        }

        &:global(.tool-active) {
            .embedded-video-player .video-player-container {
                .video-player {
                    width: 100%;
                }
            }
        }
    }

    @include mobile-portrait-in-global {
        .embedded-video-player .video-player-container {
            .video-player {
                width: 100%;
                height: 100%;
            }
        }
    }
}

:global(.tool-active.is-expanded),
:global(.tool-active:not(.is-embed)) {
    .embedded-video-player
        .video-player-container:not(.player-disabled)
        .video-player {
        width: calc(100% - rem(480));

        .tool-drawer-toggle {
            svg {
                transform: rotateY(180deg);
            }
        }
    }

    @include mobile-portrait-in-global {
        .embedded-video-player
            .video-player-container:not(.player-disabled)
            .video-player {
            width: 100%;

            .tool-drawer-toggle {
                svg {
                    transform: rotateX(180deg);
                }
            }
        }
    }
}

:global(.tool-active.is-expanded) {
    @include mobile-portrait-in-global {
        .embedded-video-player .video-player-container:not(.player-disabled) {
            .video-player {
                width: 100%;
                height: 56.25vw;
            }

            &.is-vertical .video-player {
                height: 50%;
            }
        }
    }
}

:global(.is-playing) .embedded-video-player .video-player {
    .interactive-expand-button,
    .interactive-collapse-button {
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.25s ease 0s;
    }

    &:hover {
        .interactive-expand-button,
        .interactive-collapse-button {
            opacity: 1;
            pointer-events: all;
        }
    }
}
