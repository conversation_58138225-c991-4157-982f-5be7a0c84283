import { useEffect, useState } from "react";
import { useIsTabVisible } from "../../../hooks/useIsTabVisible";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch } from "store/store";
import { RootState } from "store/reducers";
import { useInterval } from "../../../hooks/useInterval";
import useCollectionWithVideos from "../../../hooks/useCollectionWithVideos";
import { getCollectionVideosProcessingPolling } from "store/Catalog/thunks";
import { useSetNewCollectionVideo } from "hooks/useSetNewCollectionVideo";
import { NotificationType } from "store/notification/types";
import { addNotification } from "store/notification/slice";

const DEFAULT_POLLING_INTERVAL =
    parseInt(import.meta.env.VITE_DEFAULT_POLLING_INTERVAL || "") || 15000;

const useHandleProcessingPolling = () => {
    const isTabVisible = useIsTabVisible();
    const [delay, setDelay] = useState<number | null>(DEFAULT_POLLING_INTERVAL);
    const dispatch = useDispatch<AppDispatch>();

    const { onDemandVideos, processingVideos } = useCollectionWithVideos();

    const { hasSetDefault, currentCollectionVideo } = useSelector(
        (s: RootState) => s.videoSession
    );
    const { configuredCollectionId } = useSelector(
        (s: RootState) => s.catalogState
    );
    const setNewCollectionVideo = useSetNewCollectionVideo();

    // Handle processing video to ready
    useEffect(() => {
        if (
            !hasSetDefault ||
            !currentCollectionVideo ||
            currentCollectionVideo?.broadcast?.videos?.[0]?.details
                ?.readyToStream
        )
            return;

        // Check if the current processing video exists in onDemandVideos with "ready" status
        const videoInOnDemand = onDemandVideos?.find(
            (collectionVideo) =>
                collectionVideo?.broadcast?.details?.id ===
                    currentCollectionVideo?.broadcast?.details?.id &&
                collectionVideo?.broadcast?.videos?.[0]?.details?.readyToStream
        );

        const userWatchingEndedLiveVideo =
            currentCollectionVideo?.broadcast?.videos?.[0]?.details?.status ===
            "live-inprogress";

        if (videoInOnDemand && !userWatchingEndedLiveVideo) {
            // Video has finished processing and is now ready, update it
            // console.log(
            //     "Video has finished processing and is now ready, update it",
            //     videoInOnDemand?.broadcast?.details?.id,
            //     "Ready to stream:",
            //     videoInOnDemand?.broadcast?.videos?.[0]?.details?.readyToStream,
            //     "User watching ended live:",
            //     userWatchingEndedLiveVideo
            // );
            setNewCollectionVideo(videoInOnDemand);
        }

        if (userWatchingEndedLiveVideo && !!videoInOnDemand) {
            // Notify the user that the video is now available
            dispatch(
                addNotification({
                    type: NotificationType.Info,
                    message: "To watch the full stream, ",
                    clickText: "refresh this page.",
                    clickAction: () => {
                        // console.log(
                        //     "SET NEW VIDEO",
                        //     videoInOnDemand?.broadcast?.details?.id,
                        //     videoInOnDemand?.broadcast?.details?.title
                        // );
                        setNewCollectionVideo(videoInOnDemand);
                    },
                    class: "interactives"
                })
            );
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [
        dispatch,
        hasSetDefault,
        processingVideos?.length,
        setNewCollectionVideo,
        currentCollectionVideo
    ]);

    // Enable/disable polling
    useEffect(() => {
        if (isTabVisible && processingVideos?.length > 0)
            return setDelay(DEFAULT_POLLING_INTERVAL);
        setDelay(null);
    }, [isTabVisible, processingVideos]);

    // Execute polling to get processing videos
    useInterval(async () => {
        if (!configuredCollectionId) return;
        await dispatch(
            getCollectionVideosProcessingPolling({
                collectionId: configuredCollectionId
            })
        );
    }, delay);
};

export default useHandleProcessingPolling;
