import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/reducers";
import { AppDispatch } from "store/store";
import {
    setActiveCollectionTab,
    setHasSetDefault
} from "store/VideoSession/slice";
import useCollectionWithVideos from "hooks/useCollectionWithVideos";
import { useSetNewCollectionVideo } from "hooks/useSetNewCollectionVideo";
import { client } from "api/playerClient";
import { isInFuture } from "helpers/time";

export const useHandleDefaultState = () => {
    const dispatch = useDispatch<AppDispatch>();

    const {
        preselectedBroadcastId,
        collectionId,
        hasSetDefault,
        currentCollectionVideo
    } = useSelector((s: RootState) => s.videoSession);

    const { onDemandVideos, upcomingVideos } = useCollectionWithVideos();

    const setNewCollectionVideo = useSetNewCollectionVideo();

    // handle default state
    useEffect(() => {
        const setDefault = async () => {
            if (currentCollectionVideo) return;

            // Priority 1: Broadcast Preselected
            if (collectionId && preselectedBroadcastId) {
                const currentVideo = await client.byBroadcast(
                    collectionId,
                    preselectedBroadcastId
                );

                if (currentVideo) {
                    setNewCollectionVideo(currentVideo);
                    dispatch(setHasSetDefault(true));

                    if (isInFuture(currentVideo?.broadcast?.details?.startsAt))
                        dispatch(setActiveCollectionTab("Upcoming"));

                    return;
                }
            }

            // Priority 2: Live Video
            if (
                upcomingVideos?.[0]?.broadcast?.details?.broadcastStatus ===
                "Active"
            ) {
                setNewCollectionVideo(upcomingVideos?.[0]);

                // return immediately to prevent other handlings
                dispatch(setHasSetDefault(true));
                return;
            }

            // Priority 3: Featured Trailer (auto-injected as first item in OnDemand)
            // Priority 4: First in Playlist
            setNewCollectionVideo(onDemandVideos?.[0]);

            // return immediately to prevent other handlings
            dispatch(setHasSetDefault(true));
        };

        setDefault();
    }, [
        dispatch,
        setNewCollectionVideo,
        hasSetDefault,
        upcomingVideos,
        onDemandVideos,
        preselectedBroadcastId,
        collectionId,
        currentCollectionVideo
    ]);
};
