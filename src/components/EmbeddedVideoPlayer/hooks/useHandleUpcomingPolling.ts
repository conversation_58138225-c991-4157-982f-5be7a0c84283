import { useEffect, useState } from "react";
import { useIsTabVisible } from "../../../hooks/useIsTabVisible";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch } from "store/store";
import { RootState } from "store/reducers";
import { useInterval } from "../../../hooks/useInterval";
import useCollectionWithVideos from "../../../hooks/useCollectionWithVideos";
import { getCollectionVideosUpcomingPolling } from "store/Catalog/thunks";
import { CollectionVideoResponse } from "@switcherstudio/player-api-client";
import { useSetNewCollectionVideo } from "hooks/useSetNewCollectionVideo";

const DEFAULT_POLLING_INTERVAL =
    parseInt(import.meta.env.VITE_DEFAULT_POLLING_INTERVAL || "") || 15000;

const useHandleUpcomingPolling = () => {
    const isTabVisible = useIsTabVisible();
    const [delay, setDelay] = useState<number | null>(DEFAULT_POLLING_INTERVAL);
    const dispatch = useDispatch<AppDispatch>();

    const { currentCollectionVideo, hasSetDefault } = useSelector(
        (s: RootState) => s.videoSession
    );

    const { upcomingVideos } = useCollectionWithVideos();

    const { configuredCollectionId } = useSelector(
        (s: RootState) => s.catalogState
    );

    const setNewCollectionVideo = useSetNewCollectionVideo();

    // Handle countdown transition to live
    // If current video does not have videos, and there is an upcoming video with videos, set the current video to update data and load the stream
    useEffect(() => {
        if (
            !hasSetDefault ||
            !currentCollectionVideo ||
            (currentCollectionVideo?.broadcast?.videos?.length ?? 0) > 0
        )
            return;

        const newUpcomingVideo = upcomingVideos?.find(
            (upcomingCollectionVideo) =>
                upcomingCollectionVideo?.broadcast?.details?.id ===
                    currentCollectionVideo?.broadcast?.details?.id &&
                (upcomingCollectionVideo?.broadcast?.videos?.length ?? 0) > 0
        );

        if (newUpcomingVideo) {
            setNewCollectionVideo(newUpcomingVideo);
        }
    }, [
        setNewCollectionVideo,
        currentCollectionVideo,
        hasSetDefault,
        upcomingVideos
    ]);

    // Handle live video leaving live state
    useEffect(() => {
        if (
            !hasSetDefault ||
            !currentCollectionVideo ||
            currentCollectionVideo?.broadcast?.details?.broadcastStatus !==
                "Active"
        )
            return;

        // If the video is still in the upcoming list, it will still be live
        const isStillLive = upcomingVideos?.some(
            (upcomingCollectionVideo) =>
                upcomingCollectionVideo?.broadcast?.details?.id ===
                currentCollectionVideo?.broadcast?.details?.id
        );

        // If the video isn't still live, set the currentVideoState to be a non-live state
        if (!isStillLive) {
            //console.log("Set state of current video to ended", isStillLive);

            setNewCollectionVideo({
                ...currentCollectionVideo,
                broadcast: {
                    ...currentCollectionVideo.broadcast,
                    details: {
                        ...currentCollectionVideo.broadcast.details,
                        broadcastStatus: "Ended",
                        startsAt: new Date(Date.now() - 60000 * 5) // subtract 5 minutes to give buffer to UI calculations
                    }
                }
            } as CollectionVideoResponse);
        }
    }, [
        setNewCollectionVideo,
        hasSetDefault,
        currentCollectionVideo,
        upcomingVideos
    ]);

    // Enable/disable polling when moving away from tab
    useEffect(() => {
        if (isTabVisible) return setDelay(DEFAULT_POLLING_INTERVAL);
        setDelay(null);
    }, [isTabVisible]);

    // Execute polling to get upcoming videos
    useInterval(async () => {
        if (!configuredCollectionId) return;
        await dispatch(
            getCollectionVideosUpcomingPolling({
                collectionId: configuredCollectionId
            })
        );
    }, delay);
};

export default useHandleUpcomingPolling;
