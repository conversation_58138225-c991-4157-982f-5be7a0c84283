import { useState } from "react";
import {
    LazyLoadImage,
    LazyLoadImageProps
} from "react-lazy-load-image-component";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
const cx = classNames.bind(styles);

export interface ImageProps extends Partial<LazyLoadImageProps> {
    lazy?: boolean;
}

/**
 * An image component and lazy loads images - takes same props and HTMLImage.
 */
export const Image = ({ placeholder, lazy = false, ...props }: ImageProps) => {
    const [loading, setLoading] = useState(true);

    return (
        <>
            <span className={cx("image", { loading })}>
                {lazy ? (
                    <LazyLoadImage
                        {...props}
                        onLoad={() => setLoading(false)}
                        threshold={1000}
                    />
                ) : (
                    <img {...props} onLoad={() => setLoading(false)} />
                )}
            </span>
            <span className={cx("placeholder", { loading })}>
                {placeholder}
            </span>
        </>
    );
};
