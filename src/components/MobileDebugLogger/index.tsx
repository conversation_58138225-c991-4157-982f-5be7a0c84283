import React from "react";
import { useSelector } from "react-redux";
import { RootState } from "store/reducers";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
const cx = classNames.bind(styles);

export const MobileDebugLogger = ({
    blockUnderlyingContent
}: {
    blockUnderlyingContent: boolean;
}) => {
    const { debugLog } = useSelector((s: RootState) => s.mobileDebugLogger);

    return (
        <div
            className={cx("mobile-debug-logger-layer", {
                blockUnderlyingContent
            })}
        >
            {debugLog.map((log, index) => (
                <p key={index}>{log}</p>
            ))}
        </div>
    );
};
