import { Button } from "../Button";
import { RootState } from "store/reducers";
import { useSelector } from "react-redux";
import { Modals } from "store/Modals/types";
import { t } from "i18next";

interface PurchaseButtonProps {
    playerId?: string;
    isOnlySubscribe?: boolean;
}

export const PurchaseButton = ({
    playerId,
    isOnlySubscribe
}: PurchaseButtonProps) => {
    const { parentFrame } = useSelector((s: RootState) => s.videoSession);
    const { catalog } = useSelector((s: RootState) => s.catalogState);

    const openPurchaseModal = () => {
        parentFrame?.openPurchaseModal(
            Modals.PurchaseOptions,
            catalog?.details?.id ?? "",
            playerId ?? ""
        );
    };
    return (
        <Button
            text={`${
                isOnlySubscribe ? t("buttons:subscribe") : t("buttons:purchase")
            }`.toUpperCase()}
            type="large"
            onClick={openPurchaseModal}
        />
    );
};
