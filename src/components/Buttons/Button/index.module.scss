.link {
    color: var(--brand-color-secondary-dark);
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: rem(16);
    font-weight: 700;
}

.floating {
    border: none;
    background-color: transparent;
    color: #042c3b;

    &:hover {
        text-decoration: underline;
    }
}

.large {
    // width: -moz-fit-content;
    width: fit-content;
    font-size: rem(12) !important;
    padding: rem(14) rem(18) !important;
    background-color: var(--brand-color-primary) !important;
    color: var(--brand-color-secondary) !important;
    &:hover {
        opacity: 0.8;
    }
}

.video {
    border-radius: rem(8) !important;
    background-color: transparent !important;
    color: var(--brand-color-primary) !important;
    font-weight: 700;
    border: 1px solid !important;
    border-color: var(--brand-color-primary) !important;
    width: 100%;
    aspect-ratio: 16 / 9;
    padding: rem(4) rem(8);

    &.vertical {
        aspect-ratio: 9 / 16;
    }

    &:hover {
        cursor: pointer;
        color: var(--brand-color-primary-dark) !important;
        border-color: var(--brand-color-primary-dark) !important;
    }
    @media screen and (max-width: 1229px) and (min-width: 1159px) {
        &:not(.vertical) {
            padding: 20px;
        }
    }
    @media screen and (max-width: 789px) and (min-width: 743px) {
        &:not(.vertical) {
            padding: 20px;
        }
    }
    @media screen and (max-width: 331px) and (min-width: 311px) {
        &:not(.vertical) {
            padding: 20px;
        }
    }
}

.collection {
    @extend .link;
    color: var(--brand-color-primary) !important;

    &:hover {
        cursor: pointer;
        color: var(--brand-color-primary-dark) !important;
    }
}
