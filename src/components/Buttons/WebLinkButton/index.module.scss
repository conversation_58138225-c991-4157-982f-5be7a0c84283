.container {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: rem(6);

    .qr-icon {
        display: inline;

        > button > svg {
            color: var(--interactive-panel-text-color) !important;

            &:hover {
                color: var(--interactive-panel-text-color-dark) !important;
            }
        }
    }

    @include mobile {
        .qr-icon {
            display: none;
        }
    }
}
