import { Button } from "../Button";
import styles from "./index.module.scss";
import { IconButton } from "../IconButton";
import QRIcon from "assets/icons/scan.svg?react";
import { cleanUrl } from "helpers/url";
import { useLinkNavigation } from "hooks/useLinkNavigation";
import { WebLinkResponse } from "@switcherstudio/player-api-client";

export interface WebLinkButtonProps {
    link: WebLinkResponse;
    onQRClick?: () => void;
}

export const WebLinkButton = ({ link, onQRClick }: WebLinkButtonProps) => {
    const toLink = useLinkNavigation({ link: link?.details?.link ?? "" });

    if (!link?.details?.link || link?.details?.link === "") {
        return null;
    }

    return (
        <div className={styles["container"]}>
            <Button
                type="outline"
                onClick={toLink}
                text={
                    !link?.details?.title || link?.details?.title === ""
                        ? cleanUrl(link?.details?.link)
                        : link?.details?.title
                }
            />
            <div className={styles["qr-icon"]}>
                <IconButton icon={QRIcon} onClick={onQRClick} />
            </div>
        </div>
    );
};
