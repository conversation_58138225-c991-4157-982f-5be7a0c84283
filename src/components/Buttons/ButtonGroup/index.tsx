import { useState } from "react";
import { TruthyProductValue } from "../../../store/shopping/types";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
const cx = classNames.bind(styles);

interface ButtonGroupProps {
    buttons: TruthyProductValue[];
    onClick: (value: string) => void;
    activeValue: string;
}
export const ButtonGroup = ({
    buttons,
    onClick,
    activeValue
}: ButtonGroupProps) => {
    const [clickedId, setClickedId] = useState("");

    const handleClick = (opt: TruthyProductValue) => {
        if (opt.isAvailable) {
            setClickedId(opt.value);
            onClick(opt.value);
        }
    };

    return (
        <div className={cx("button-group")}>
            {buttons.map((opt, i) => (
                <button
                    key={i}
                    name={opt.value}
                    onClick={() => handleClick(opt)}
                    disabled={!opt.isAvailable}
                    className={cx("button-group-button", {
                        "active":
                            (clickedId === "" && opt.value === activeValue) ||
                            opt.value === clickedId
                    })}
                >
                    {opt.value}
                </button>
            ))}
        </div>
    );
};
