.button-group {
    display: flex;
    gap: rem(12);
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    overflow-y: auto;

    .button-group-button {
        padding: rem(10) rem(16);
        border-radius: rem(8);
        background: #ffffff;
        border: rem(2) solid rgba(121, 121, 121, 0.16);

        cursor: pointer;

        font-weight: 400;
        font-size: rem(14);
        line-height: rem(20);
        letter-spacing: rem(0.5);
        font-family: var(--embed-font-family);

        transition: 0.25s background-color ease-in-out, 0.25s border ease-in-out;

        &.active {
            font-weight: 600;
            border: rem(2) solid #121212;
            background: rgba(18, 18, 18, 0.08);
        }

        &:hover:enabled {
            border: rem(2) solid lighten(#121212, 30);
            background: rgba(18, 18, 18, 0.02);
        }

        &:disabled {
            background-color: rgba(
                var(--brand-color-primary-rgb),
                0.15
            ) !important;
            color: rgba(var(--brand-color-secondary-rgb), 0.4);
        }
    }
}
