import { ButtonHTMLAttributes, DetailedHTMLProps } from "react";
import styles from "./index.module.scss";

export interface IconButtonProps
    extends DetailedHTMLProps<
        ButtonHTMLAttributes<HTMLButtonElement>,
        HTMLButtonElement
    > {
    icon: React.FunctionComponent<React.SVGProps<SVGSVGElement>>;
    additionalClasses?: string[];
}

export const IconButton = ({
    icon,
    additionalClasses,
    ...props
}: IconButtonProps) => {
    const finalClasses = [styles.button];
    if (additionalClasses) {
        finalClasses.push(...additionalClasses);
    }

    return (
        <button className={finalClasses.join(" ")} {...props}>
            {icon({})}
        </button>
    );
};
