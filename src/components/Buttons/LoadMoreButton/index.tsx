import classNames from "classnames/bind";
import styles from "./index.module.scss";
import { Button } from "../Button";
import { useTranslation } from "react-i18next";
const cx = classNames.bind(styles);

export interface LoadMoreButtonProps {
    /** text to be shown on button */
    text: string;
    /** callback called on click */
    onClick: () => void;
    /** true renders button as disabled */
    disabled?: boolean;
    variant: "collection" | "video";
    isVertical?: boolean;
    error?: any;
}

export const LoadMoreButton = ({
    text,
    onClick,
    variant,
    disabled = false,
    error = null,
    isVertical = false
}: LoadMoreButtonProps) => {
    const { t } = useTranslation();

    return (
        <div className={cx("loadmore-button")}>
            <Button
                text={text}
                type={variant === "collection" ? "collection" : "video"}
                onClick={onClick}
                disabled={disabled}
                isVertical={isVertical}
            />
            {error && <p>{t("errors:try-again")}</p>}
        </div>
    );
};
