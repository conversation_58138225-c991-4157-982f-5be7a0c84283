import React from 'react';
import { ChatUser } from '../../types/chat';
import styles from './UserList.module.scss';
import classNames from 'classnames/bind';

const cx = classNames.bind(styles);

interface UserListProps {
    users: ChatUser[];
    currentUserId?: string;
    className?: string;
}

export const UserList: React.FC<UserListProps> = ({ 
    users, 
    currentUserId, 
    className 
}) => {
    // Sort users: current user first, then by username
    const sortedUsers = React.useMemo(() => {
        return [...users].sort((a, b) => {
            if (a.id === currentUserId) return -1;
            if (b.id === currentUserId) return 1;
            return a.username.localeCompare(b.username);
        });
    }, [users, currentUserId]);

    // Generate avatar from username
    const getAvatarText = (username: string) => {
        return username.charAt(0).toUpperCase();
    };

    // Get user color or generate one from username
    const getUserColor = (user: ChatUser) => {
        if (user.color) {
            return user.color;
        }
        
        // Generate color from username hash
        let hash = 0;
        for (let i = 0; i < user.username.length; i++) {
            hash = user.username.charCodeAt(i) + ((hash << 5) - hash);
        }
        
        const hue = Math.abs(hash) % 360;
        return `hsl(${hue}, 70%, 50%)`;
    };

    // Format last seen time
    const formatLastSeen = (lastSeen: any) => {
        try {
            const date = lastSeen.seconds 
                ? new Date(lastSeen.seconds * 1000)
                : new Date(lastSeen);
            
            const now = new Date();
            const diffMs = now.getTime() - date.getTime();
            const diffMins = Math.floor(diffMs / 60000);
            
            if (diffMins < 1) return 'Just now';
            if (diffMins < 60) return `${diffMins}m ago`;
            
            const diffHours = Math.floor(diffMins / 60);
            if (diffHours < 24) return `${diffHours}h ago`;
            
            const diffDays = Math.floor(diffHours / 24);
            return `${diffDays}d ago`;
        } catch (error) {
            return '';
        }
    };

    if (users.length === 0) {
        return (
            <div className={cx('user-list', 'empty', className)}>
                <div className={cx('user-list-header')}>
                    <h3>Online Users</h3>
                    <span className={cx('user-count')}>0</span>
                </div>
                <div className={cx('empty-state')}>
                    <p>No users online</p>
                </div>
            </div>
        );
    }

    return (
        <div className={cx('user-list', className)}>
            <div className={cx('user-list-header')}>
                <h3>Online Users</h3>
                <span className={cx('user-count')}>{users.length}</span>
            </div>
            
            <div className={cx('users-container')}>
                {sortedUsers.map((user) => {
                    const isCurrentUser = user.id === currentUserId;
                    const userColor = getUserColor(user);
                    
                    return (
                        <div 
                            key={user.id} 
                            className={cx('user-item', {
                                'current-user': isCurrentUser,
                                'online': user.isOnline,
                                'offline': !user.isOnline
                            })}
                        >
                            <div 
                                className={cx('user-avatar')}
                                style={{ backgroundColor: userColor }}
                            >
                                {getAvatarText(user.username)}
                                <div className={cx('online-indicator', {
                                    'online': user.isOnline,
                                    'offline': !user.isOnline
                                })} />
                            </div>
                            
                            <div className={cx('user-info')}>
                                <div className={cx('user-name')}>
                                    {isCurrentUser ? `${user.username} (You)` : user.username}
                                </div>
                                
                                <div className={cx('user-status')}>
                                    {user.isOnline ? (
                                        <span className={cx('status-online')}>Online</span>
                                    ) : (
                                        <span className={cx('status-offline')}>
                                            {formatLastSeen(user.lastSeen)}
                                        </span>
                                    )}
                                </div>
                            </div>
                        </div>
                    );
                })}
            </div>
        </div>
    );
};
