import React, { useEffect, useRef, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store/reducers';
import { AppDispatch } from '../../store/store';
import { initializeChat, sendChatMessage, cleanupChat } from '../../store/Chat/thunks';
import { MessageList } from './MessageList';
import { MessageInput } from './MessageInput';
import { UserList } from './UserList';
import { ChatHeader } from './ChatHeader';
import styles from './ChatPanel.module.scss';
import classNames from 'classnames/bind';

const cx = classNames.bind(styles);

interface ChatPanelProps {
    roomId: string;
    className?: string;
}

export const ChatPanel: React.FC<ChatPanelProps> = ({ roomId, className }) => {
    const dispatch = useDispatch<AppDispatch>();
    const [showUserList, setShowUserList] = useState(false);
    const [isMinimized, setIsMinimized] = useState(false);
    const chatContainerRef = useRef<HTMLDivElement>(null);

    const {
        currentUser,
        currentRoom,
        messages,
        onlineUsers,
        isConnected,
        isLoading,
        error
    } = useSelector((state: RootState) => state.chat);

    // Initialize chat when component mounts
    useEffect(() => {
        if (roomId) {
            dispatch(initializeChat(roomId));
        }

        // Cleanup when component unmounts
        return () => {
            dispatch(cleanupChat());
        };
    }, [dispatch, roomId]);

    // Handle sending messages
    const handleSendMessage = (message: string) => {
        if (currentRoom && message.trim()) {
            dispatch(sendChatMessage({
                message: message.trim(),
                roomId: currentRoom.id
            }));
        }
    };

    // Handle user list toggle
    const handleToggleUserList = () => {
        setShowUserList(!showUserList);
    };

    // Handle minimize/maximize
    const handleToggleMinimize = () => {
        setIsMinimized(!isMinimized);
    };

    // Show loading state
    if (isLoading && !currentUser) {
        return (
            <div className={cx('chat-panel', 'loading', className)}>
                <div className={cx('loading-content')}>
                    <div className={cx('loading-spinner')}></div>
                    <span>Connecting to chat...</span>
                </div>
            </div>
        );
    }

    // Show error state
    if (error && !currentUser) {
        return (
            <div className={cx('chat-panel', 'error', className)}>
                <div className={cx('error-content')}>
                    <span className={cx('error-message')}>Failed to connect to chat</span>
                    <button 
                        className={cx('retry-button')}
                        onClick={() => dispatch(initializeChat(roomId))}
                    >
                        Retry
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div 
            className={cx('chat-panel', {
                'minimized': isMinimized,
                'disconnected': !isConnected
            }, className)}
            ref={chatContainerRef}
        >
            <ChatHeader
                currentUser={currentUser}
                onlineCount={onlineUsers.length}
                isConnected={isConnected}
                onToggleUserList={handleToggleUserList}
                onToggleMinimize={handleToggleMinimize}
                isMinimized={isMinimized}
                showUserList={showUserList}
            />

            {!isMinimized && (
                <>
                    <div className={cx('chat-content')}>
                        <div className={cx('main-chat-area')}>
                            <MessageList 
                                messages={messages}
                                currentUserId={currentUser?.id}
                            />
                            
                            {error && (
                                <div className={cx('error-banner')}>
                                    <span>{error}</span>
                                </div>
                            )}
                        </div>

                        {showUserList && (
                            <div className={cx('user-list-sidebar')}>
                                <UserList 
                                    users={onlineUsers}
                                    currentUserId={currentUser?.id}
                                />
                            </div>
                        )}
                    </div>

                    <MessageInput
                        onSendMessage={handleSendMessage}
                        disabled={!isConnected || !currentUser}
                        placeholder={
                            !isConnected 
                                ? "Connecting..." 
                                : !currentUser 
                                    ? "Please wait..." 
                                    : "Type a message..."
                        }
                    />
                </>
            )}

            {!isConnected && (
                <div className={cx('connection-status')}>
                    <span>Reconnecting...</span>
                </div>
            )}
        </div>
    );
};
