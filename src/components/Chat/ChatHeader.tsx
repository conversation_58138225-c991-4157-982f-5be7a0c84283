import React from 'react';
import { ChatUser } from '../../types/chat';
import styles from './ChatHeader.module.scss';
import classNames from 'classnames/bind';

const cx = classNames.bind(styles);

interface ChatHeaderProps {
    currentUser: ChatUser | null;
    onlineCount: number;
    isConnected: boolean;
    onToggleUserList: () => void;
    onToggleMinimize: () => void;
    isMinimized: boolean;
    showUserList: boolean;
    className?: string;
}

export const ChatHeader: React.FC<ChatHeaderProps> = ({
    currentUser,
    onlineCount,
    isConnected,
    onToggleUserList,
    onToggleMinimize,
    isMinimized,
    showUserList,
    className
}) => {
    return (
        <div className={cx('chat-header', className)}>
            <div className={cx('header-left')}>
                <div className={cx('chat-title')}>
                    <span className={cx('title-text')}>Live Chat</span>
                    <div className={cx('connection-status', {
                        'connected': isConnected,
                        'disconnected': !isConnected
                    })}>
                        <div className={cx('status-dot')} />
                        <span className={cx('status-text')}>
                            {isConnected ? 'Connected' : 'Disconnected'}
                        </span>
                    </div>
                </div>
                
                {!isMinimized && (
                    <div className={cx('user-info')}>
                        {currentUser && (
                            <span className={cx('current-user')}>
                                {currentUser.username}
                            </span>
                        )}
                    </div>
                )}
            </div>

            <div className={cx('header-right')}>
                {!isMinimized && (
                    <button
                        className={cx('user-list-button', {
                            'active': showUserList
                        })}
                        onClick={onToggleUserList}
                        aria-label={showUserList ? 'Hide user list' : 'Show user list'}
                        title={showUserList ? 'Hide user list' : 'Show user list'}
                    >
                        <svg 
                            className={cx('users-icon')} 
                            viewBox="0 0 24 24" 
                            fill="none" 
                            stroke="currentColor"
                        >
                            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                            <circle cx="9" cy="7" r="4"/>
                            <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                        </svg>
                        <span className={cx('user-count')}>{onlineCount}</span>
                    </button>
                )}

                <button
                    className={cx('minimize-button')}
                    onClick={onToggleMinimize}
                    aria-label={isMinimized ? 'Maximize chat' : 'Minimize chat'}
                    title={isMinimized ? 'Maximize chat' : 'Minimize chat'}
                >
                    {isMinimized ? (
                        <svg 
                            className={cx('maximize-icon')} 
                            viewBox="0 0 24 24" 
                            fill="none" 
                            stroke="currentColor"
                        >
                            <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"/>
                        </svg>
                    ) : (
                        <svg 
                            className={cx('minimize-icon')} 
                            viewBox="0 0 24 24" 
                            fill="none" 
                            stroke="currentColor"
                        >
                            <path d="M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3"/>
                        </svg>
                    )}
                </button>
            </div>
        </div>
    );
};
