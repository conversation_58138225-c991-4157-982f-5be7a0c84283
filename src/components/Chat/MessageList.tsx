import React, { useEffect, useRef } from 'react';
import { ChatMessage } from '../../types/chat';
import { MessageItem } from './MessageItem';
import styles from './MessageList.module.scss';
import classNames from 'classnames/bind';

const cx = classNames.bind(styles);

interface MessageListProps {
    messages: ChatMessage[];
    currentUserId?: string;
    className?: string;
}

export const MessageList: React.FC<MessageListProps> = ({ 
    messages, 
    currentUserId, 
    className 
}) => {
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);
    const [shouldAutoScroll, setShouldAutoScroll] = React.useState(true);

    // Auto-scroll to bottom when new messages arrive
    useEffect(() => {
        if (shouldAutoScroll && messagesEndRef.current) {
            messagesEndRef.current.scrollIntoView({ 
                behavior: 'smooth',
                block: 'end'
            });
        }
    }, [messages, shouldAutoScroll]);

    // Check if user has scrolled up to disable auto-scroll
    const handleScroll = () => {
        if (containerRef.current) {
            const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
            const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10; // 10px threshold
            setShouldAutoScroll(isAtBottom);
        }
    };

    // Group consecutive messages from the same user
    const groupedMessages = React.useMemo(() => {
        const groups: ChatMessage[][] = [];
        let currentGroup: ChatMessage[] = [];

        messages.forEach((message, index) => {
            const prevMessage = messages[index - 1];
            const isSameUser = prevMessage && prevMessage.userId === message.userId;
            const isWithinTimeLimit = prevMessage && 
                (message.timestamp.seconds - prevMessage.timestamp.seconds) < 300; // 5 minutes

            if (isSameUser && isWithinTimeLimit) {
                currentGroup.push(message);
            } else {
                if (currentGroup.length > 0) {
                    groups.push([...currentGroup]);
                }
                currentGroup = [message];
            }
        });

        if (currentGroup.length > 0) {
            groups.push(currentGroup);
        }

        return groups;
    }, [messages]);

    if (messages.length === 0) {
        return (
            <div className={cx('message-list', 'empty', className)}>
                <div className={cx('empty-state')}>
                    <div className={cx('empty-icon')}>💬</div>
                    <p className={cx('empty-text')}>
                        No messages yet. Be the first to say hello!
                    </p>
                </div>
            </div>
        );
    }

    return (
        <div 
            className={cx('message-list', className)}
            ref={containerRef}
            onScroll={handleScroll}
        >
            <div className={cx('messages-container')}>
                {groupedMessages.map((messageGroup, groupIndex) => (
                    <div key={groupIndex} className={cx('message-group')}>
                        {messageGroup.map((message, messageIndex) => (
                            <MessageItem
                                key={message.id}
                                message={message}
                                isOwn={message.userId === currentUserId}
                                showAvatar={messageIndex === 0} // Only show avatar for first message in group
                                showUsername={messageIndex === 0} // Only show username for first message in group
                            />
                        ))}
                    </div>
                ))}
                
                {/* Scroll anchor */}
                <div ref={messagesEndRef} />
            </div>

            {/* Scroll to bottom button */}
            {!shouldAutoScroll && (
                <button
                    className={cx('scroll-to-bottom')}
                    onClick={() => {
                        setShouldAutoScroll(true);
                        messagesEndRef.current?.scrollIntoView({ 
                            behavior: 'smooth',
                            block: 'end'
                        });
                    }}
                    aria-label="Scroll to bottom"
                >
                    ↓
                </button>
            )}
        </div>
    );
};
