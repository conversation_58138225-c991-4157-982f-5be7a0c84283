@import "../../variables";
@import "../../mixins";

.message-item {
    display: flex;
    gap: 8px;
    margin-bottom: 4px;

    &.own-message {
        flex-direction: row-reverse;
    }

    &.compact {
        margin-bottom: 2px;

        .message-content {
            margin-left: 32px; // Account for avatar space
        }

        &.own-message .message-content {
            margin-left: 0;
            margin-right: 32px;
        }
    }
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 14px;
    flex-shrink: 0;
    margin-top: 2px;

    &.own-avatar {
        order: 2;
    }
}

.message-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
    min-width: 0;
}

.message-header {
    display: flex;
    align-items: baseline;
    gap: 8px;
    margin-bottom: 2px;
}

.message-username {
    font-weight: 600;
    font-size: 12px;
    flex-shrink: 0;
}

.message-time {
    font-size: 11px;
    color: var(--color-text-secondary);
    opacity: 0.7;
}

.message-bubble {
    max-width: 80%;
    padding: 8px 12px;
    border-radius: 12px;
    word-wrap: break-word;
    position: relative;

    &.own-bubble {
        background: var(--color-primary);
        color: white;
        align-self: flex-end;
        border-bottom-right-radius: 4px;
    }

    &.other-bubble {
        background: var(--color-background-secondary);
        color: var(--color-text-primary);
        border-bottom-left-radius: 4px;
    }
}

.message-text {
    font-size: 14px;
    line-height: 1.4;
    margin: 0;
    white-space: pre-wrap;
}

.message-link {
    color: inherit;
    text-decoration: underline;
    
    &:hover {
        text-decoration: none;
    }

    .own-bubble & {
        color: rgba(255, 255, 255, 0.9);
    }

    .other-bubble & {
        color: var(--color-primary);
    }
}

// Responsive design
@media (max-width: 480px) {
    .message-item {
        gap: 6px;
    }

    .message-avatar {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }

    .message-bubble {
        max-width: 85%;
        padding: 6px 10px;
        border-radius: 10px;

        &.own-bubble {
            border-bottom-right-radius: 3px;
        }

        &.other-bubble {
            border-bottom-left-radius: 3px;
        }
    }

    .message-text {
        font-size: 13px;
    }

    .message-username {
        font-size: 11px;
    }

    .message-time {
        font-size: 10px;
    }

    .compact .message-content {
        margin-left: 28px;

        &.own-message {
            margin-left: 0;
            margin-right: 28px;
        }
    }
}
