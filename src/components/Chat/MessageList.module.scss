@import "../../variables";
@import "../../mixins";

.message-list {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
    padding: 8px 0;
    scroll-behavior: smooth;
    overflow-y: auto;

    &.empty {
        justify-content: center;
        align-items: center;
    }
}

.messages-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 0 12px;
    min-height: min-content;
}

.message-group {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    color: var(--color-text-secondary);
    text-align: center;
    padding: 20px;
}

.empty-icon {
    font-size: 32px;
    opacity: 0.5;
}

.empty-text {
    font-size: 14px;
    margin: 0;
    max-width: 200px;
}

.scroll-to-bottom {
    position: absolute;
    bottom: 16px;
    right: 16px;
    width: 36px;
    height: 36px;
    background: var(--color-primary);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: bold;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.2s;
    z-index: 5;

    &:hover {
        background: var(--color-primary-dark);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    &:active {
        transform: translateY(0);
    }
}

// Custom scrollbar
.message-list::-webkit-scrollbar {
    width: 6px;
}

.message-list::-webkit-scrollbar-track {
    background: var(--color-background-secondary);
}

.message-list::-webkit-scrollbar-thumb {
    background: var(--color-border);
    border-radius: 3px;

    &:hover {
        background: var(--color-text-secondary);
    }
}

// Responsive design
@media (max-width: 480px) {
    .messages-container {
        padding: 0 8px;
    }

    .scroll-to-bottom {
        width: 32px;
        height: 32px;
        bottom: 12px;
        right: 12px;
        font-size: 14px;
    }

    .empty-state {
        padding: 16px;
    }

    .empty-icon {
        font-size: 24px;
    }

    .empty-text {
        font-size: 12px;
    }
}
