@import "../../variables";
@import "../../mixins";

.chat-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: var(--color-background);
    border-radius: 8px;
    overflow: hidden;
    position: relative;

    &.loading {
        justify-content: center;
        align-items: center;
        min-height: 200px;
    }

    &.error {
        justify-content: center;
        align-items: center;
        min-height: 200px;
    }

    &.minimized {
        height: auto;
        
        .chat-content,
        .connection-status {
            display: none;
        }
    }

    &.disconnected {
        opacity: 0.7;
    }
}

.loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    color: var(--color-text-secondary);
    font-size: 14px;
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid var(--color-border);
    border-top: 2px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    color: var(--color-error);
    font-size: 14px;
}

.error-message {
    text-align: center;
}

.retry-button {
    padding: 8px 16px;
    background: var(--color-primary);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.2s;

    &:hover {
        background: var(--color-primary-dark);
    }
}

.chat-content {
    display: flex;
    flex: 1;
    min-height: 0;
}

.main-chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.user-list-sidebar {
    width: 200px;
    border-left: 1px solid var(--color-border);
    background: var(--color-background-secondary);
}

.error-banner {
    padding: 8px 12px;
    background: var(--color-error-background);
    color: var(--color-error);
    font-size: 12px;
    text-align: center;
    border-bottom: 1px solid var(--color-error);
}

.connection-status {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 8px;
    background: var(--color-warning-background);
    color: var(--color-warning);
    text-align: center;
    font-size: 12px;
    z-index: 10;
}

// Responsive design
@media (max-width: 768px) {
    .chat-content {
        flex-direction: column;
    }

    .user-list-sidebar {
        width: 100%;
        border-left: none;
        border-top: 1px solid var(--color-border);
        max-height: 150px;
        overflow-y: auto;
    }
}

@media (max-width: 480px) {
    .chat-panel {
        border-radius: 0;
    }
}
