@import "../../variables";
@import "../../mixins";

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: var(--color-background-secondary);
    border-bottom: 1px solid var(--color-border);
    min-height: 60px;
}

.header-left {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex: 1;
}

.chat-title {
    display: flex;
    align-items: center;
    gap: 8px;
}

.title-text {
    font-weight: 600;
    font-size: 16px;
    color: var(--color-text-primary);
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 4px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    transition: background-color 0.2s;

    .connected & {
        background: var(--color-success);
    }

    .disconnected & {
        background: var(--color-error);
    }
}

.status-text {
    font-size: 12px;
    color: var(--color-text-secondary);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.current-user {
    font-size: 12px;
    color: var(--color-text-secondary);
    font-style: italic;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

.user-list-button {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 8px;
    background: transparent;
    border: 1px solid var(--color-border);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    color: var(--color-text-secondary);

    &:hover {
        background: var(--color-background-hover);
        color: var(--color-text-primary);
    }

    &.active {
        background: var(--color-primary);
        color: white;
        border-color: var(--color-primary);
    }
}

.users-icon {
    width: 16px;
    height: 16px;
}

.user-count {
    font-size: 12px;
    font-weight: 500;
}

.minimize-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: transparent;
    border: 1px solid var(--color-border);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    color: var(--color-text-secondary);

    &:hover {
        background: var(--color-background-hover);
        color: var(--color-text-primary);
    }
}

.minimize-icon,
.maximize-icon {
    width: 16px;
    height: 16px;
}

// Responsive design
@media (max-width: 480px) {
    .chat-header {
        padding: 8px 12px;
        min-height: 50px;
    }

    .title-text {
        font-size: 14px;
    }

    .user-list-button {
        padding: 4px 6px;
    }

    .minimize-button {
        width: 28px;
        height: 28px;
    }
}
