import React from 'react';
import { ChatMessage } from '../../types/chat';
import styles from './MessageItem.module.scss';
import classNames from 'classnames/bind';

const cx = classNames.bind(styles);

interface MessageItemProps {
    message: ChatMessage;
    isOwn: boolean;
    showAvatar?: boolean;
    showUsername?: boolean;
    className?: string;
}

export const MessageItem: React.FC<MessageItemProps> = ({
    message,
    isOwn,
    showAvatar = true,
    showUsername = true,
    className
}) => {
    // Format timestamp
    const formatTime = (timestamp: any) => {
        try {
            const date = timestamp.seconds 
                ? new Date(timestamp.seconds * 1000)
                : new Date(timestamp);
            
            return date.toLocaleTimeString([], { 
                hour: '2-digit', 
                minute: '2-digit',
                hour12: false
            });
        } catch (error) {
            return '';
        }
    };

    // Generate avatar from username
    const getAvatarText = (username: string) => {
        return username.charAt(0).toUpperCase();
    };

    // Get user color or generate one from username
    const getUserColor = () => {
        if (message.userColor) {
            return message.userColor;
        }
        
        // Generate color from username hash
        let hash = 0;
        for (let i = 0; i < message.username.length; i++) {
            hash = message.username.charCodeAt(i) + ((hash << 5) - hash);
        }
        
        const hue = Math.abs(hash) % 360;
        return `hsl(${hue}, 70%, 50%)`;
    };

    // Detect and linkify URLs in message
    const linkifyMessage = (text: string) => {
        const urlRegex = /(https?:\/\/[^\s]+)/g;
        const parts = text.split(urlRegex);
        
        return parts.map((part, index) => {
            if (urlRegex.test(part)) {
                return (
                    <a
                        key={index}
                        href={part}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={cx('message-link')}
                    >
                        {part}
                    </a>
                );
            }
            return part;
        });
    };

    const userColor = getUserColor();

    return (
        <div 
            className={cx('message-item', {
                'own-message': isOwn,
                'other-message': !isOwn,
                'show-avatar': showAvatar,
                'compact': !showAvatar
            }, className)}
        >
            {showAvatar && !isOwn && (
                <div 
                    className={cx('message-avatar')}
                    style={{ backgroundColor: userColor }}
                >
                    {getAvatarText(message.username)}
                </div>
            )}

            <div className={cx('message-content')}>
                {showUsername && (
                    <div className={cx('message-header')}>
                        <span 
                            className={cx('message-username')}
                            style={{ color: userColor }}
                        >
                            {isOwn ? 'You' : message.username}
                        </span>
                        <span className={cx('message-time')}>
                            {formatTime(message.timestamp)}
                        </span>
                    </div>
                )}

                <div className={cx('message-bubble', {
                    'own-bubble': isOwn,
                    'other-bubble': !isOwn
                })}>
                    <div className={cx('message-text')}>
                        {linkifyMessage(message.message)}
                    </div>
                </div>
            </div>

            {showAvatar && isOwn && (
                <div 
                    className={cx('message-avatar', 'own-avatar')}
                    style={{ backgroundColor: userColor }}
                >
                    {getAvatarText(message.username)}
                </div>
            )}
        </div>
    );
};
