import React, { useState, useRef, KeyboardEvent } from 'react';
import styles from './MessageInput.module.scss';
import classNames from 'classnames/bind';

const cx = classNames.bind(styles);

interface MessageInputProps {
    onSendMessage: (message: string) => void;
    disabled?: boolean;
    placeholder?: string;
    maxLength?: number;
    className?: string;
}

export const MessageInput: React.FC<MessageInputProps> = ({
    onSendMessage,
    disabled = false,
    placeholder = "Type a message...",
    maxLength = 500,
    className
}) => {
    const [message, setMessage] = useState('');
    const [isComposing, setIsComposing] = useState(false);
    const textareaRef = useRef<HTMLTextAreaElement>(null);

    // Handle sending message
    const handleSend = () => {
        const trimmedMessage = message.trim();
        if (trimmedMessage && !disabled) {
            onSendMessage(trimmedMessage);
            setMessage('');
            
            // Reset textarea height
            if (textareaRef.current) {
                textareaRef.current.style.height = 'auto';
            }
        }
    };

    // Handle key press events
    const handleKeyPress = (e: KeyboardEvent<HTMLTextAreaElement>) => {
        // Send on Enter (but not Shift+Enter)
        if (e.key === 'Enter' && !e.shiftKey && !isComposing) {
            e.preventDefault();
            handleSend();
        }
    };

    // Handle input change
    const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        const value = e.target.value;
        
        // Limit message length
        if (value.length <= maxLength) {
            setMessage(value);
        }

        // Auto-resize textarea
        const textarea = e.target;
        textarea.style.height = 'auto';
        textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`; // Max 120px height
    };

    // Handle composition events (for IME input)
    const handleCompositionStart = () => {
        setIsComposing(true);
    };

    const handleCompositionEnd = () => {
        setIsComposing(false);
    };

    // Check if send button should be enabled
    const canSend = message.trim().length > 0 && !disabled;

    return (
        <div className={cx('message-input', { disabled }, className)}>
            <div className={cx('input-container')}>
                <textarea
                    ref={textareaRef}
                    value={message}
                    onChange={handleInputChange}
                    onKeyPress={handleKeyPress}
                    onCompositionStart={handleCompositionStart}
                    onCompositionEnd={handleCompositionEnd}
                    placeholder={placeholder}
                    disabled={disabled}
                    className={cx('message-textarea')}
                    rows={1}
                    maxLength={maxLength}
                />
                
                <button
                    onClick={handleSend}
                    disabled={!canSend}
                    className={cx('send-button', {
                        'can-send': canSend
                    })}
                    aria-label="Send message"
                >
                    <svg 
                        className={cx('send-icon')} 
                        viewBox="0 0 24 24" 
                        fill="none" 
                        stroke="currentColor"
                    >
                        <path d="m22 2-7 20-4-9-9-4z"/>
                        <path d="M22 2 11 13"/>
                    </svg>
                </button>
            </div>
            
            <div className={cx('input-footer')}>
                <span className={cx('character-count', {
                    'near-limit': message.length > maxLength * 0.8
                })}>
                    {message.length}/{maxLength}
                </span>
                
                <span className={cx('input-hint')}>
                    Press Enter to send, Shift+Enter for new line
                </span>
            </div>
        </div>
    );
};
