import { Timestamp } from 'firebase/firestore';

export interface ChatUser {
    id: string;
    username: string;
    isOnline: boolean;
    lastSeen: Timestamp;
    color?: string; // Optional color for username display
}

export interface ChatMessage {
    id: string;
    userId: string;
    username: string;
    message: string;
    timestamp: Timestamp;
    roomId: string;
    userColor?: string; // Optional color for message display
}

export interface ChatRoom {
    id: string;
    name: string;
    description?: string;
    createdAt: Timestamp;
    isActive: boolean;
    participantCount: number;
    lastActivity: Timestamp;
}

export interface ChatState {
    currentUser: ChatUser | null;
    currentRoom: ChatRoom | null;
    messages: ChatMessage[];
    onlineUsers: ChatUser[];
    isConnected: boolean;
    isLoading: boolean;
    error: string | null;
}

export interface SendMessagePayload {
    message: string;
    roomId: string;
}

export interface JoinRoomPayload {
    roomId: string;
    username?: string;
}

// Firestore document interfaces (for type safety with Firebase)
export interface ChatMessageDoc {
    userId: string;
    username: string;
    message: string;
    timestamp: Timestamp;
    roomId: string;
    userColor?: string;
}

export interface ChatUserDoc {
    username: string;
    isOnline: boolean;
    lastSeen: Timestamp;
    color?: string;
}

export interface ChatRoomDoc {
    name: string;
    description?: string;
    createdAt: Timestamp;
    isActive: boolean;
    participantCount: number;
    lastActivity: Timestamp;
}
