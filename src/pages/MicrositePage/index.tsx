import { useEffect, useMemo, useRef } from "react";
import { useSearchParams } from "react-router-dom";
import { validate as isUuid } from "uuid";

import styles from "./index.module.scss";
import classNames from "classnames/bind";
const cx = classNames.bind(styles);

const MicrositePage = () => {
    const [searchParams] = useSearchParams();

    const catalogId = useMemo<string | null>(() => {
        const potentialUuid = searchParams.get("c");
        return potentialUuid && isUuid(potentialUuid) ? potentialUuid : null;
    }, [searchParams]);

    const playerId = useMemo<string | null>(() => {
        const potentialUuid = searchParams.get("p");
        return potentialUuid && isUuid(potentialUuid) ? potentialUuid : null;
    }, [searchParams]);

    const broadcastId = useMemo<string | null>(() => {
        const potentialUuid = searchParams.get("b");
        return potentialUuid && isUuid(potentialUuid) ? potentialUuid : null;
    }, [searchParams]);

    const containerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (
            containerRef.current &&
            (!!catalogId || !!playerId || !!broadcastId)
        ) {
            containerRef.current.innerHTML = `<div
                class="dff402f7-5be0-4890-b831-95c5b63ddb42"
                data-hostname="${window.location.origin}"
                data-path="/embed"
                ${!!catalogId ? `data-catalogid="${catalogId}"` : ""}
                ${!!playerId ? `data-videoplayerid="${playerId}"` : ""}
                ${!!broadcastId ? `data-broadcastid="${broadcastId}"` : ""}
                data-microsite="true"
                data-location="iframe"
            ></div>`;
            // @ts-expect-error The SwitcherPlayerApp is included in the index.html
            window.switcherPlayerApp.init();
        }
    }, [catalogId, playerId, broadcastId]);

    return (
        <div className={cx("microsite-page-container")} ref={containerRef} />
    );
};

export default MicrositePage;
