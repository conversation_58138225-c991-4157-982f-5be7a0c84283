import { useCallback, useEffect, useMemo, useState } from "react";
import { useSearchParams } from "react-router-dom";
import { AppDispatch } from "store/store";
import { EmbeddedVideoPlayer } from "../../components/EmbeddedVideoPlayer";
import { useDispatch, useSelector } from "react-redux";
import {
    clearVideoSession,
    refreshVideoSession,
    setEmbedState,
    setExpandedState,
    setIframeType,
    setIsAutoPlay,
    setIsIframeVisible,
    setIsMicrosite,
    setParentFrame,
    setUpcomingPreselectedPageNumber,
    setOnDemandPreselectedPageNumber,
    setReferrerUrl,
    setToolState,
    setCollectionId,
    setPreselectedBroadcastId
} from "store/VideoSession/slice";
import { connectToParent } from "penpal";
import { IFrameType, ParentFrame } from "store/VideoSession/types";
import { RootState } from "store/reducers";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import { setConsentTracking } from "../../store/ConsentTracking/slice";
import { client } from "api/playerClient";
import { getCreator } from "store/PlayerCreator/thunks";
// import { getBrandProfile } from "store/BrandProfile/thunks";
import { useRefreshToken } from "hooks/useRefreshToken";
import { EmbeddedVideoPlayerLite } from "components/EmbeddedVideoPlayerLite";
import { Guid } from "helpers/guids";
import { Catalog } from "components/Catalog";
import { useSetCSSCustomProperties } from "hooks/useSetCSSCustomProperties";
import {
    getCatalog,
    getCollection,
    getSingleBroadcastResponse
} from "store/Catalog/thunks";
import { useRollbar } from "@rollbar/react";
import { Modals } from "store/Modals/types";
import useCollectionWithVideos from "hooks/useCollectionWithVideos";
import {
    ICreatorCustomerTicket,
    PasswordClaim
} from "store/CreatorCustomers/types";
import {
    setCreatorCustomerTicket,
    setPasswordClaims
} from "store/CreatorCustomers/slice";
import {
    clearCatalog,
    setCatalogDetails,
    setCollectionDetails,
    setConfiguredCatalogId,
    setConfiguredCollectionId,
    setConfiguredBroadcastId,
    setIsGatedContentDisabled
} from "store/Catalog/slice";
import { setActiveModal } from "store/Modals/slice";
import { v4 as uuidv4 } from "uuid";
import { usePageTracking } from "hooks/usePageTracking";
import { validate as isUuid } from "uuid";
import { useIsMobile } from "hooks/useIsMobile";
import {
    CatalogDetailsResponse,
    CollectionDetailsResponse
} from "@switcherstudio/player-api-client";
import { usePlayerStateClasses } from "hooks/usePlayerStateClasses";
import { usePlayerHistoryNavigation } from "hooks/usePlayerHistoryNavigation";
import { useOpenPreselectedPlayer } from "hooks/useOpenPreselectedPlayer";

const cx = classNames.bind(styles);

const EmbedPage = () => {
    useSetCSSCustomProperties();
    const dispatch = useDispatch<AppDispatch>();
    const [searchParams] = useSearchParams();
    const { isPortraitMobile } = useIsMobile();
    usePlayerHistoryNavigation();

    const projectId = useMemo<string | null | undefined>(
        () => searchParams.get("projectId") ?? Guid.empty,
        [searchParams]
    );

    const [catalogId, setCatalogId] = useState<string | null | undefined>(
        searchParams.get("c")
    );

    const [playerId, setPlayerId] = useState<string | null | undefined>(
        searchParams.get("p")
    );

    const broadcastId = useMemo<string | null | undefined>(
        () => searchParams.get("b"),
        [searchParams]
    );

    const isMicrosite = useMemo<boolean>(
        () => searchParams.get("microsite") === "true",
        [searchParams]
    );

    const [hasFetchedBearerToken, setHasFetchedBearerToken] =
        useState<boolean>(false);
    const [appKey, setAppKey] = useState<string>(Guid.empty);
    const { getNewRefreshToken } = useRefreshToken({ lazyLoad: true });

    const { collection } = useCollectionWithVideos();
    const { catalog } = useSelector((s: RootState) => s.catalogState);
    const { parentFrame, iframeType, isEmbed, isIframeVisible } = useSelector(
        (s: RootState) => s.videoSession
    );

    const rollbar = useRollbar();
    const { trackPage } = usePageTracking();

    // Reload the data after a new token is has been sent in from the parent frame
    const embedRefresh = useCallback(() => {
        dispatch(clearCatalog());
        dispatch(refreshVideoSession());
        if (catalogId && catalogId !== Guid.empty) {
            dispatch(setConfiguredCatalogId(catalogId));
            dispatch(
                getCatalog({
                    catalogId: catalogId,
                    loadCollections: iframeType === IFrameType.Main
                })
            );
        }
        if (playerId && playerId !== Guid.empty) {
            dispatch(setConfiguredCollectionId(playerId));
            dispatch(
                getCollection({
                    collectionId: playerId
                })
            );
        }
    }, [dispatch, catalogId, playerId, iframeType]);

    // This effect triggers the fresh.  We can't use the function directly in the parent frame function as it will be stale.
    useEffect(() => {
        if (appKey !== Guid.empty) {
            embedRefresh();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [appKey]);

    // Clear the player/catalog ids when the iframe is no longer visible (which resets the state)
    useEffect(() => {
        if (!isIframeVisible) {
            dispatch(clearCatalog());
            dispatch(clearVideoSession());
            setCatalogId(null);
            setPlayerId(null);
        }
    }, [dispatch, isIframeVisible]);

    // Perform the embed initialization.
    // - Set initial variables
    // - Configure the connection to the parent frame
    useEffect(() => {
        const iframeParam = searchParams.get("iframeType") ?? "";
        const iframeType = Object.values(IFrameType).includes(
            iframeParam as IFrameType
        )
            ? (iframeParam as IFrameType)
            : IFrameType.Main;
        dispatch(setIframeType(iframeType));

        // The embed state is true if any of the following is true:
        // - The iframeType is not the main player
        // - The catalogId is not null
        // - We are NOT in the microsite
        dispatch(
            setEmbedState(
                iframeType !== IFrameType.Main || !!catalogId || !isMicrosite
            )
        );
        dispatch(setReferrerUrl(searchParams.get("referrerUrl")));
        dispatch(setIsIframeVisible(iframeType === IFrameType.Main));
        dispatch(setIsMicrosite(isMicrosite));

        const isAutoPlay = (searchParams.get("autoplay") ?? "") === "true";
        dispatch(setIsAutoPlay(isAutoPlay));

        const hasConnection = connectToParent<ParentFrame>({
            methods: {
                setDoNotTrack(doNotTrack: boolean) {
                    dispatch(setConsentTracking(doNotTrack));
                    return;
                },
                setCatalogDetails(payload: CatalogDetailsResponse) {
                    if (payload.id) {
                        dispatch(setConfiguredCatalogId(payload.id));
                    }
                    dispatch(setCatalogDetails(payload));
                    return;
                },
                setCollectionDetails(payload: CollectionDetailsResponse) {
                    dispatch(setCollectionDetails(payload));
                    return;
                },
                loadPlayer(
                    catalogId: string,
                    collectionId: string,
                    upcomingPreselectedPageNumber: number | null,
                    onDemandPreselectedPageNumber: number | null,
                    preselectedBroadcastId: string,
                    autoPlay: boolean
                ) {
                    setCatalogId(catalogId);
                    setPlayerId(collectionId);
                    dispatch(setIsIframeVisible(true));
                    dispatch(setIframeType(IFrameType.AuxPlayer));
                    dispatch(setIsAutoPlay(autoPlay));

                    dispatch(setCollectionId(collectionId));
                    dispatch(setPreselectedBroadcastId(preselectedBroadcastId));

                    dispatch(
                        setUpcomingPreselectedPageNumber(
                            upcomingPreselectedPageNumber
                        )
                    );
                    dispatch(
                        setOnDemandPreselectedPageNumber(
                            onDemandPreselectedPageNumber
                        )
                    );
                    return;
                },
                loadPurchaseModal(
                    type: Modals,
                    catalogId: string,
                    playerId: string
                ) {
                    setCatalogId(catalogId);
                    setPlayerId(playerId);
                    dispatch(setIsIframeVisible(true));
                    dispatch(setIframeType(IFrameType.AuxModal));
                    dispatch(setActiveModal(type));
                    return;
                },
                setAuthorization(ticket: ICreatorCustomerTicket) {
                    dispatch(setCreatorCustomerTicket(ticket));
                    setAppKey(uuidv4());
                },
                setPasswordClaims(
                    projectId: string,
                    passwordClaims: PasswordClaim[]
                ) {
                    dispatch(
                        setPasswordClaims({
                            projectId,
                            passwordClaims
                        })
                    );
                    setAppKey(uuidv4());
                }
            }
        });

        hasConnection.promise
            .then((data) => {
                dispatch(setIsGatedContentDisabled(false));
                dispatch(setParentFrame(data));
            })
            .catch((error) => {
                rollbar.error("Error connecting to parent frame", error);
            });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        // returns the oldest player's playerId
        const handleLegacyProjectId = async () => {
            let videoPlayerId: string = Guid.empty;
            if (playerId) {
                videoPlayerId = playerId;
            } else if (projectId !== Guid.empty) {
                const response = await client.oldest(projectId as string);
                videoPlayerId = response?.details?.id ?? Guid.empty;
                setPlayerId(videoPlayerId);
            }

            // only begin creator and token initialization if we have a catalogId, videoPlayerId, or broadcastId
            if (
                (!!catalogId && catalogId != Guid.empty) ||
                (!!videoPlayerId && videoPlayerId != Guid.empty) ||
                (!!broadcastId && broadcastId != Guid.empty)
            ) {
                await dispatch(
                    getCreator({ catalogId, videoPlayerId, broadcastId })
                );
                await getNewRefreshToken();
                setHasFetchedBearerToken(true);
            }
        };
        handleLegacyProjectId();
    }, [
        projectId,
        dispatch,
        getNewRefreshToken,
        catalogId,
        playerId,
        broadcastId
    ]);

    // fetches the catalog data only after the bearer token is set
    useEffect(() => {
        if (hasFetchedBearerToken && catalogId && catalogId !== Guid.empty) {
            dispatch(setConfiguredCatalogId(catalogId));
            dispatch(
                getCatalog({
                    catalogId: catalogId,
                    loadCollections: iframeType === IFrameType.Main
                })
            );
        }
    }, [hasFetchedBearerToken, catalogId, dispatch, iframeType]);

    // fetches the player data only after the bearer token is set
    useEffect(() => {
        if (hasFetchedBearerToken && playerId && playerId !== Guid.empty) {
            dispatch(setConfiguredCollectionId(playerId));
            dispatch(
                getCollection({
                    collectionId: playerId
                })
            );
            // dispatch(getBrandProfile({ playerId: playerId }));
        }
    }, [hasFetchedBearerToken, playerId, dispatch, iframeType]);

    // fetches the broadcast data only after the bearer token is set
    useEffect(() => {
        if (
            hasFetchedBearerToken &&
            broadcastId &&
            broadcastId !== Guid.empty &&
            isUuid(broadcastId)
        ) {
            dispatch(setConfiguredBroadcastId(broadcastId));
            dispatch(
                getSingleBroadcastResponse({
                    broadcastId: broadcastId
                })
            );
        }
    }, [hasFetchedBearerToken, broadcastId, dispatch]);

    // sets (or clears) the aspect ratio on the parent frame once video player profile is loaded
    useEffect(() => {
        if (
            !!parentFrame &&
            !!collection?.details?.aspectRatio &&
            iframeType === IFrameType.Main &&
            isEmbed
        ) {
            if (collection?.details?.embeddedDisplay === "DefaultThumbnail") {
                parentFrame.setAspectRatio(collection?.details?.aspectRatio);
            } else {
                // clear the aspect ratio if the player is not using the default thumbnail presentation
                parentFrame.setAspectRatio();
            }
        }
    }, [
        parentFrame,
        collection?.details?.aspectRatio,
        iframeType,
        isEmbed,
        collection?.details?.embeddedDisplay
    ]);

    // sets the background color on the main dialog since to account for an iframe that is smaller than the dialog
    useEffect(() => {
        if (!!parentFrame && iframeType === IFrameType.Main && isMicrosite) {
            parentFrame.setDialogBackgroundColor(
                (!!catalogId
                    ? catalog?.details?.embedBackgroundColor
                    : collection?.details?.embedBackgroundColor) ?? "#FFFFFF"
            );
        }
    }, [
        parentFrame,
        iframeType,
        isMicrosite,
        catalogId,
        catalog?.details?.embedBackgroundColor,
        collection?.details?.embedBackgroundColor
    ]);

    // sets the max width on the main dialog when it is configured
    useEffect(() => {
        if (!!parentFrame && iframeType === IFrameType.Main && !isMicrosite) {
            let maxWidth;
            if (!!catalogId && catalog?.details?.enableMaxEmbedWidth) {
                // if the catalog max width is enabled, use it as the value
                maxWidth = catalog?.details?.maxEmbedWidth;
            } else if (collection?.details?.enableMaxEmbedWidth) {
                // otherwise if the collection max width is enabled, use it as the value
                maxWidth = collection?.details?.maxEmbedWidth;
            }

            // set the max width on the parent frame
            parentFrame.setMaxWidth(maxWidth);
        }
    }, [
        parentFrame,
        iframeType,
        isMicrosite,
        catalogId,
        catalog?.details?.enableMaxEmbedWidth,
        catalog?.details?.maxEmbedWidth,
        collection?.details?.enableMaxEmbedWidth,
        collection?.details?.maxEmbedWidth
    ]);

    // set default expanded state for a player embed (only applies to the player view)
    useEffect(() => {
        if (iframeType === IFrameType.Main && !!playerId && isEmbed) {
            dispatch(setExpandedState(false));
        }
    }, [iframeType, playerId, isEmbed, dispatch]);

    useEffect(() => {
        // The tool drawer is closed by default when the player is:
        const toolDrawerClosed =
            isPortraitMobile && // in portrait orientation on mobile
            catalogId && // being launched from the catalog
            catalogId !== Guid.empty && // (has valid catalogId)
            iframeType === IFrameType.AuxPlayer && // in the aux iframe
            (!catalog?.isGated || !!catalog?.isEntitled); // not restricted by catalog entitlement/access

        dispatch(setToolState(!toolDrawerClosed));
    }, [isPortraitMobile, dispatch, catalogId, iframeType, catalog]);

    const embedComponent = useMemo<JSX.Element>(() => {
        if (iframeType === IFrameType.Main) {
            if (broadcastId) {
                return <EmbeddedVideoPlayerLite />;
            }
            return (
                <>
                    {(!!catalogId ||
                        (!!playerId &&
                            !!collection?.details?.embeddedDisplay &&
                            collection?.details?.embeddedDisplay !==
                                "DefaultThumbnail")) && <Catalog />}
                    {!!playerId &&
                        collection?.details?.embeddedDisplay ===
                            "DefaultThumbnail" && (
                            <EmbeddedVideoPlayer key={appKey} />
                        )}
                </>
            );
        }
        if (iframeType === IFrameType.AuxPlayer && !!playerId) {
            return <EmbeddedVideoPlayer key={appKey} />;
        }
        return <></>;
    }, [
        iframeType,
        playerId,
        broadcastId,
        catalogId,
        collection?.details?.embeddedDisplay,
        appKey
    ]);

    useEffect(() => {
        if (iframeType === IFrameType.Main) trackPage();
    }, [trackPage, iframeType]);

    // Fire the embed loaded event
    useEffect(() => {
        if (!parentFrame?.emitEvent) return;
        parentFrame.emitEvent("switcherEmbedLoaded", { isLoaded: true });
    }, [parentFrame]);

    const playerStateGlobalClasses = usePlayerStateClasses();

    const embedContainerClasses = cx("embedded-page-container", {
        "aux-or-default-on-main":
            iframeType === IFrameType.AuxPlayer ||
            (collection?.details?.embeddedDisplay === "DefaultThumbnail" &&
                iframeType === IFrameType.Main),
        "is-catalog": iframeType === IFrameType.Main && !!catalogId
    });

    useOpenPreselectedPlayer();

    return (
        <div className={`${embedContainerClasses} ${playerStateGlobalClasses}`}>
            {embedComponent}
        </div>
    );
};

export default EmbedPage;
