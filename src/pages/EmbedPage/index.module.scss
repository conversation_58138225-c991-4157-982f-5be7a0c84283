.embedded-page-container {
    width: 100%;
    height: 100%;
    overflow: hidden;

    // background-color and backdrop-filter only on EXPANDED embed
    &:global(:not(.is-embed):not(.is-expanded)),
    &.is-catalog {
        background-color: rgba(var(--embed-background-color-rgb));
    }

    @include mobile-landscape {
        &:global(.tool-active) {
            min-width: 600px;
            overflow: auto;
        }
    }
}

:global(.is-expanded).aux-or-default-on-main {
    backdrop-filter: blur(rem(16));
    transform: translateZ(0);
    background-color: rgba(0, 0, 0, 0.5);
}
