import { useInterval } from "hooks/useInterval";
import React, { useMemo, useState } from "react";
import { useSearchParams } from "react-router-dom";
import styles from "./index.module.scss";
import { TransitionPageContent } from "components/TransitionPageContent";
import classNames from "classnames/bind";
import { payments } from "api/payment/client";
const cx = classNames.bind(styles);

const STRIPE_CHECKOUT_POLLING_INTERVAL =
    parseInt(import.meta.env.VITE_STRIPE_CHECKOUT_POLLING_INTERVAL || "") ||
    2000;

const GatedContentProcessingPage = () => {
    const [searchParams] = useSearchParams();
    const finalUrl = useMemo(
        () => searchParams.get("final_url") ?? "",
        [searchParams]
    );
    const checkoutSessionId = useMemo(
        () => searchParams.get("session_id"),
        [searchParams]
    );
    const creatorStripeAccountId = useMemo(
        () => searchParams.get("account_id"),
        [searchParams]
    );
    const [checkoutSessionStatus, setCheckoutSessionStatus] =
        useState<string>("waiting");

    // poll for the updated checkout session status
    useInterval(async () => {
        if (
            checkoutSessionStatus === "waiting" &&
            checkoutSessionId &&
            creatorStripeAccountId
        ) {
            const checkoutStatusResponse =
                await payments.GetStripeCheckoutStatus({
                    sessionId: checkoutSessionId,
                    creatorStripeAccountId
                });

            setCheckoutSessionStatus(checkoutStatusResponse.Status ?? "");
            if (checkoutStatusResponse?.Status === "success") {
                setTimeout(() => {
                    window.location.href = `${window.location.protocol}//${
                        window.location.host
                    }/authorize?token=${
                        checkoutStatusResponse?.AuthToken
                    }&final_url=${encodeURIComponent(finalUrl)}`;
                }, 2000);
            }
        }
    }, STRIPE_CHECKOUT_POLLING_INTERVAL);

    return (
        <div className={cx("processing-page")}>
            {checkoutSessionStatus === "failed" && (
                <TransitionPageContent
                    type="error"
                    header="Payment Failed"
                    descriptionLines={[
                        "We couldn't process your payment. Try another payment method, or contact your card issuer for more information."
                    ]}
                    extraContent={
                        <button
                            className={cx(
                                "btn",
                                "btn-black",
                                "back-to-video-button"
                            )}
                            type="button"
                            onClick={() => {
                                window.location.href = finalUrl;
                            }}
                        >
                            Cancel
                        </button>
                    }
                />
            )}

            {checkoutSessionStatus === "waiting" && (
                <TransitionPageContent
                    type="loading"
                    header="Payment Processing"
                    descriptionLines={[
                        "This could take a moment. Please don't leave this page."
                    ]}
                />
            )}

            {checkoutSessionStatus === "success" && (
                <TransitionPageContent
                    type="success"
                    header="Payment Successful"
                    descriptionLines={[
                        "Hang tight, we're taking you back to your purchased video now. Please don't leave this page."
                    ]}
                />
            )}
        </div>
    );
};

export default GatedContentProcessingPage;
