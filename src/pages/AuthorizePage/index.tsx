import { TransitionPageContent } from "components/TransitionPageContent";
import { trackSegmentEvent } from "helpers/analyticsHelpers";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import { useSearchParams } from "react-router-dom";
import { exchangeCode } from "store/CreatorCustomers/thunks";
import { RootState } from "store/reducers";
import { AppDispatch } from "store/store";
import styles from "./index.module.scss";
import classNames from "classnames/bind";
import { connectToParent } from "penpal";
import { ParentFrame } from "store/VideoSession/types";
import { setParentFrame } from "store/VideoSession/slice";
import { useRollbar } from "@rollbar/react";
import { ICreatorCustomerTicket } from "store/CreatorCustomers/types";
import { setCreatorCustomerTicket } from "store/CreatorCustomers/slice";
const cx = classNames.bind(styles);

const AuthorizePage = () => {
    const [status, setStatus] = useState<"loading" | "error">("loading");
    const dispatch = useDispatch<AppDispatch>();
    const [searchParams] = useSearchParams();
    const authToken = searchParams.get("token");
    const finalUrl = searchParams.get("final_url");
    const successUrl = searchParams.get("success_url");
    const creatorProjectId = useSelector(
        (s: RootState) => s.playerCreator?.details?.projectId
    );
    const creatorCustomers = useSelector((s: RootState) => s.creatorCustomers);
    const creatorCustomer = useMemo(
        () => creatorCustomers[creatorProjectId ?? ""],
        [creatorCustomers, creatorProjectId]
    );

    const redirectUrl = useMemo(
        () => finalUrl ?? successUrl,
        [finalUrl, successUrl]
    );

    const rollbar = useRollbar();
    useEffect(() => {
        // Connect to parent frame in order to distribute auth when it is available
        connectToParent<ParentFrame>({
            methods: {
                setAuthorization(ticket: ICreatorCustomerTicket) {
                    dispatch(setCreatorCustomerTicket(ticket));
                }
            }
        })
            .promise.then((data) => {
                dispatch(setParentFrame(data));
            })
            .catch((error) => {
                rollbar.error("Error connecting to parent frame", error);
            });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const trackAndRedirect = useCallback(() => {
        trackSegmentEvent("Creator Customer Authenticated", {
            creatorCustomerId: creatorCustomer?.ticket?.customerId,
            creatorCustomerEmail: creatorCustomer?.ticket?.email,
            projectId: creatorCustomer?.ticket?.projectId
        });

        if (redirectUrl) {
            window.location.href = redirectUrl;
        }
    }, [creatorCustomer, redirectUrl]);

    useEffect(() => {
        const run = async () => {
            try {
                await dispatch(exchangeCode(authToken!));
                trackAndRedirect();
            } catch (e) {
                setStatus("error");
            }
        };

        run();
    }, [authToken, trackAndRedirect, dispatch]);

    const header = useMemo(() => {
        switch (status) {
            case "loading":
                return "Authorizing";
            case "error":
                return "Something Went Wrong";
            default:
                return "";
        }
    }, [status]);
    const descriptionLines = useMemo(() => {
        switch (status) {
            case "loading":
                return ["Do not refresh or navigate away from this page."];
            case "error":
                return ["Please return to the video and try again."];
            default:
                return [];
        }
    }, [status]);

    return (
        <div className={cx("authorization-page")}>
            <TransitionPageContent
                type={status}
                header={header}
                descriptionLines={descriptionLines}
                extraContent={
                    status === "error" ? (
                        <button
                            className={cx(
                                "btn",
                                "btn-black",
                                "back-to-video-button"
                            )}
                            type="button"
                            onClick={() => {
                                if (redirectUrl) {
                                    window.location.href = redirectUrl;
                                }
                            }}
                        >
                            Go Back
                        </button>
                    ) : undefined
                }
            />
        </div>
    );
};

export default AuthorizePage;
