{"name": "embedded-video-player", "version": "2.20.8", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/material": "^5.15.0", "@reduxjs/toolkit": "^1.8.3", "@rollbar/react": "^0.11.1", "@stripe/react-stripe-js": "^2.7.0", "@stripe/stripe-js": "^3.3.0", "@switcherstudio/player-api-client": "^1.120.0", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "bootstrap": "^5.1.3", "classnames": "^2.3.2", "dayjs": "^1.11.10", "error-stack-parser": "^2.1.4", "i18next": "^21.8.14", "i18next-browser-languagedetector": "^6.1.4", "lit": "^2.3.1", "penpal": "^6.2.2", "player.js": "^0.1.0", "react": "^18.3.1", "react-device-detect": "^2.2.2", "react-dom": "^18.3.1", "react-i18next": "^11.18.1", "react-lazy-load-image-component": "^1.6.0", "react-qr-code": "^2.0.8", "react-redux": "^8.0.2", "react-router-dom": "6", "redux-persist": "^6.0.0", "resize-observer-polyfill": "^1.5.1", "rollbar": "^2.25.1", "shaka-player": "^4.4.2", "swiper": "^10.0.4", "twilio-sync": "^3.1.0", "uuid": "^9.0.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "yarn build-ts-dev && vite", "build": "vite build && yarn build-ts", "serve": "vite preview", "lint": "eslint src/**/*.{js,ts,tsx}", "embed": "npx http-server embed", "test": "npx playwright test", "cf": "vite build && npx wrangler pages dev build --port 8788", "build-ts": "parcel build ./embed/embed.ts --dist-dir build", "build-ts-dev": "parcel build ./embed/embed.ts --dist-dir public", "link-local-client": "cd ../switcher-player-api/client && yarn link && cd ../../embedded-video-player && yarn link @switcherstudio/player-api-client && cd ../switcher-player-api/client && npm run build-local", "unlink-local-client": "yarn unlink @switcherstudio/player-api-client && yarn install --force", "upgrade-player-client": "yarn upgrade @switcherstudio/player-api-client", "listen-to-stripe": "stripe listen --forward-to localhost:1338/api/Creator/Webhook"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@cloudflare/workers-types": "^4.20240117.0", "@playwright/test": "^1.38.1", "@types/google.analytics": "^0.0.45", "@types/jest": "^27.5.2", "@types/node": "^20.16.5", "@types/react": "^18.3.8", "@types/react-dom": "^18.3.0", "@types/react-lazy-load-image-component": "^1.6.2", "@types/react-redux": "^7.1.24", "@types/redux-persist": "^4.3.1", "@types/segment-analytics": "^0.0.37", "@types/uuid": "^8.3.4", "@types/webpack-env": "^1.17.0", "@typescript-eslint/eslint-plugin": "^6.20.0", "@typescript-eslint/parser": "^6.20.0", "@vitejs/plugin-react-swc": "^3.4.0", "dotenv": "^16.3.1", "eslint": "^8.50.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "parcel": "^2.8.0", "sass": "^1.54.0", "typescript": "^5.1.6", "vite": "^4.4.9", "vite-plugin-eslint": "^1.8.1", "vite-plugin-svgr": "^4.0.0", "vite-tsconfig-paths": "^4.2.1"}}