export const onRequestGet: PagesFunction = async (context) => {
  // Contents of context object
  const {
    request, // same as existing Worker API
    // env, // same as existing Worker API
    // params, // if filename includes [id] or [[path]]
    // waitUntil, // same as ctx.waitUntil in existing Worker API
    // next, // used for middleware or to fetch assets
    // data, // arbitrary space for passing data between middlewares
  } = context;

  const requestUrl = new URL(request.url);
  const videoPlayerId = requestUrl.searchParams.get("videoPlayerId");
  const host = requestUrl.host;

  const wixJavascript = `
        const createPlayerDiv = (playerId) => {
            const player = document.createElement('div');
            player.classList.add('dff402f7-5be0-4890-b831-95c5b63ddb42');
            player.dataset.hostname = 'https://${host}';
            player.dataset.path = '/embed';
            player.dataset.videoplayerid = playerId;
            player.dataset.location = 'iframe';

            return player;
        }

        class switcherPlayer extends HTMLElement {
            constructor() {
                super();
            }

            connectedCallback() {
                this.appendChild(createPlayerDiv('${videoPlayerId}'));
                window.switcherPlayerApp.init()
            }
        }

        customElements.define('switcher-player', switcherPlayer);
    `;

  return !videoPlayerId
    ? new Response(null, { status: 404 })
    : new Response(wixJavascript, {
        headers: { "Content-Type": "text/javascript" },
      });
};
