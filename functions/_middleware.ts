import { onRequest as apiRequest } from "./api-proxy/[[route]]";
import { Env } from "./internal/types/env";
import type {
    BroadcastResponse,
    CatalogResponse,
    CollectionResponse,
    CollectionVideoResponse,
    SlugLookupResponse
} from "@switcherstudio/player-api-client";

// Skip processing on anything that isn't an html request
const isHtml = (result: Response) => {
    const contentTypeHeader = result.headers.get("Content-Type");
    const mimeType = contentTypeHeader ? contentTypeHeader.split(";")[0] : null;
    return mimeType && mimeType.toLowerCase() === "text/html";
};

/**
 * Middleware to inject Open Graph and oEmbed tags into the head of /watch pages.
 */
class headInjectWatchElementHandler {
    baseUrl: string;
    url: string;
    ogTitle: string;
    ogDescription: string;
    ogImage: string;

    constructor(baseUrl, url, ogTitle, ogDescription, ogImage) {
        this.baseUrl = baseUrl;
        this.url = encodeURIComponent(url);
        this.ogTitle = ogTitle;
        this.ogDescription = ogDescription;
        this.ogImage = ogImage;
    }

    element(element: Element) {
        // Append oEmbed Link Tags
        element.append(
            `<link rel="alternate" type="application/json+oembed" title="Switcher Player" href="${this.baseUrl}/oembed?format=json&url=${this.url}">`,
            { html: true }
        );
        element.append(
            `<link rel="alternate" type="text/xml+oembed" title="Switcher Player" href="${this.baseUrl}/oembed?format=xml&url=${this.url}">`,
            { html: true }
        );

        // Append OG Meta Tags
        if (!!this.ogTitle) {
            element.append(
                `<meta property="og:title" content="${this.ogTitle}" />`,
                {
                    html: true
                }
            );
        } else {
            element.append(`<meta property="og:title" content="Join Us." />`, {
                html: true
            });
        }
        if (!!this.ogDescription) {
            element.append(
                `<meta property="og:description" content="${this.ogDescription}" />`,
                {
                    html: true
                }
            );
            element.append(
                `<meta name="description" content="${this.ogDescription}" />`,
                {
                    html: true
                }
            );
        } else {
            element.append(
                `<meta property="og:description" content="Watch live video content and videos on-demand." />`,
                { html: true }
            );
            element.append(
                `<meta name="description" content="Watch live video content and videos on-demand" />`,
                { html: true }
            );
        }
        if (!!this.ogImage) {
            element.append(
                `<meta property="og:image" content="${this.ogImage}" />`,
                {
                    html: true
                }
            );
        } else {
            element.append(
                `<meta property="og:image" content="/switcher-player-preview-generic.png" />`,
                { html: true }
            );
        }
    }
}

/**
 * Middleware to inject Open Graph and oEmbed tags into the head of /watch pages.
 */
const headInjectWatch: PagesFunction<Env> = async (
    context
): Promise<Response> => {
    let result = await context.next();

    if (!isHtml(result)) {
        return result;
    }

    const regex =
        /\/watch\?([cbp])=([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})/g;

    const requestUrl = context.request.url;
    const { host, pathname, search } = new URL(requestUrl);
    const match = regex.exec(pathname + search);

    const params = new URLSearchParams(search);

    if (!!match) {
        const baseUrl =
            context.env.REACT_APP_PLAYER_API_URL ??
            "https://vapi-develop.switcherstudio.com";

        const playerType = match[1];
        const entityId = match[2];
        let ogTitle = null,
            ogDescription = null,
            ogImage = null;

        if (params.has("switcher-pcid") && params.has("switcher-pbid")) {
            const collectionId = params.get("switcher-pcid");
            const broadcastId = params.get("switcher-pbid");

            const collectionVideoResponse = await apiRequest({
                ...context,
                request: new Request(
                    `${baseUrl}/api/v1/CollectionVideos/ByBroadcast?CollectionId=${collectionId}&BroadcastId=${broadcastId}`
                )
            });

            if (
                collectionVideoResponse.ok &&
                collectionVideoResponse.status !== 204
            ) {
                const collectionVideo =
                    (await collectionVideoResponse.json()) as CollectionVideoResponse;

                ogTitle = collectionVideo?.broadcast?.details?.title;
                ogDescription =
                    collectionVideo?.broadcast?.details?.description;
                ogImage =
                    collectionVideo?.broadcast?.thumbnail?.details?.url ??
                    collectionVideo?.broadcast?.videos?.[0]?.details?.thumbnail;
            }
        } else if (playerType === "c") {
            const catalogResponse = await apiRequest({
                ...context,
                request: new Request(`${baseUrl}/api/v1/Catalog?Id=${entityId}`)
            });

            if (catalogResponse.ok) {
                const catalog = await catalogResponse.json<CatalogResponse>();

                ogTitle = !catalog?.details?.title
                    ? "Video Catalog"
                    : catalog?.details?.title;
                ogDescription =
                    catalog?.details?.description ??
                    "Check out the entire catalog of video content!";
                ogImage = catalog?.thumbnail?.details?.url;
            }
        } else if (playerType === "p") {
            const videosPlayerResponse = await apiRequest({
                ...context,
                request: new Request(
                    `${baseUrl}/api/v1/Collections/${entityId}`
                )
            });

            if (videosPlayerResponse.ok) {
                const collection =
                    (await videosPlayerResponse.json()) as CollectionResponse;

                if (collection?.details?.isLibraryPlayer) {
                    ogTitle = "Video Library";
                    ogDescription =
                        "Check out the entire library of video content!";
                } else {
                    ogTitle = collection?.details?.name;
                    ogDescription = collection?.details?.description;
                }

                ogImage = collection?.thumbnail?.details?.url;
            }
        }
        // if this is a lite player, get the broadcast information
        else if (playerType === "b") {
            const broadcastResponse = await apiRequest({
                ...context,
                request: new Request(`${baseUrl}/api/v1/Videos/${entityId}`)
            });

            if (broadcastResponse.ok) {
                const broadcast =
                    (await broadcastResponse.json()) as BroadcastResponse;

                ogTitle = broadcast?.details?.title;
                ogDescription = broadcast?.details?.description;
                ogImage =
                    broadcast?.thumbnail?.details?.url ??
                    broadcast?.videos?.[0]?.details?.thumbnail;
            }
        }

        return new HTMLRewriter()
            .on(
                "head",
                new headInjectWatchElementHandler(
                    `https://${host}`,
                    requestUrl,
                    ogTitle,
                    ogDescription,
                    ogImage
                )
            )
            .transform(result);
    }

    return result;
};

/**
 * Middleware to inject Google Fonts link into the head of /embed pages.
 */
class headInjectEmbedElementHandler {
    font: string;
    constructor(font: string) {
        this.font = font;
    }

    element(element: Element) {
        element.append(
            `<link href="https://fonts.googleapis.com/css2?family=${this.font.replace(
                " ",
                "+"
            )}" rel="stylesheet">`,
            { html: true }
        );
        element.append(
            `<style>:root { --embed-font-family: '${this.font}', 'Noto Sans', sans-serif; }</style>`,
            { html: true }
        );
    }
}

/**
 * Middleware to inject Google Fonts link into the head of /embed pages.
 */
const headInjectEmbed: PagesFunction<Env> = async (
    context
): Promise<Response> => {
    let result = await context.next();

    if (!isHtml(result)) {
        return result;
    }

    const regex =
        /^\/embed\?[^#]*([cbp])=([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})/;

    const requestUrl = context.request.url;

    const { pathname, search } = new URL(requestUrl);
    const match = regex.exec(pathname + search);

    let font = "Noto Sans";

    if (!!match) {
        const baseUrl =
            context.env.REACT_APP_PLAYER_API_URL ??
            "https://vapi-develop.switcherstudio.com";

        const playerType = match[1];

        const entityId = match[2];

        if (playerType === "c") {
            const catalogResponse = await apiRequest({
                ...context,
                request: new Request(`${baseUrl}/api/v1/Catalog?Id=${entityId}`)
            });

            if (catalogResponse.ok) {
                const catalog = await catalogResponse.json<CatalogResponse>();

                font = catalog?.details?.googleFontFamily ?? "Noto Sans";
            }
        } else if (playerType === "p") {
            const videosPlayerResponse = await apiRequest({
                ...context,
                request: new Request(
                    `${baseUrl}/api/v1/Collections/${entityId}`
                )
            });

            if (videosPlayerResponse.ok) {
                const collection =
                    (await videosPlayerResponse.json()) as CollectionResponse;

                font = collection?.details?.googleFontFamily ?? "Noto Sans";
            }
        }


        return new HTMLRewriter()
            .on("head", new headInjectEmbedElementHandler(font))
            .transform(result);
    }

    return result;
};

const protectedPaths = [
    "/",
    "/watch",
    "/embed",
    "/order-processing",
    "/authorize",
    "/assets"
];

const redirectFromSlug = async (context) => {
    let result = await context.next();

    if (!isHtml(result)) {
        return result;
    }

    const { host, pathname } = new URL(context.request.url);

    if (!protectedPaths.includes(pathname)) {
        const baseUrl =
            context.env.REACT_APP_PLAYER_API_URL ??
            "https://vapi-develop.switcherstudio.com";

        const slug = pathname.split("/")[1];

        // Bypass for legacy project ID urls
        if (
            /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(
                slug
            )
        ) {
            return result;
        }

        const request = new Request(
            `${baseUrl}/api/v1/Slug/Lookup?slug=${slug}`
        );

        const response = await apiRequest({
            ...context,
            request
        });

        if (response.ok) {
            try {
                const slug = await response.json<SlugLookupResponse>();
                let url: URL;

                switch (slug.type) {
                    case "Catalog":
                        url = new URL(`https://${host}/watch?c=${slug.id}`);
                        break;
                    case "Collection":
                        url = new URL(`https://${host}/watch?p=${slug.id}`);
                        break;
                    default:
                        throw new Error();
                }

                return Response.redirect(url.href, 302);
            } catch (e) {
                return new Response("Player not found", { status: 404 });
            }
        } else {
            return new Response("Player not found", { status: 404 });
        }
    } else {
        return result;
    }
};

export const onRequest = [redirectFromSlug, headInjectWatch, headInjectEmbed];
