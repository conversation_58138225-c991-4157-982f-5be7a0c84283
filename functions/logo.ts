interface PotentialLogo {
    rel?: string;
    sizes?: string;
    href: string;
}

class LogoHandler {
    logoOptions: PotentialLogo[];

    constructor() {
        this.logoOptions = [];
    }
    element(el) {
        const rel = el.getAttribute('rel');
        const sizes = el.getAttribute('sizes');
        const href = el.getAttribute('href');
        this.logoOptions.push({ rel: rel, sizes: sizes, href: href });
    }
}

export const onRequestGet: PagesFunction = async (context) => {
    // Contents of context object
    const {
        request, // same as existing Worker API
        env, // same as existing Worker API
        params, // if filename includes [id] or [[path]]
        waitUntil, // same as ctx.waitUntil in existing Worker API
        next, // used for middleware or to fetch assets
        data, // arbitrary space for passing data between middlewares
    } = context;
    
    const requestUrl = new URL(request.url);
    const targetQuery = requestUrl.searchParams.get('target');
    const targetUrl = new URL(targetQuery);
    const target = `${targetUrl.protocol}//${targetUrl.host}`;

    const cache = caches.default;
    const cacheKey = `${target}/logo`;
    let response = await cache.match(cacheKey);
    console.log(!(!response));

    if (!response) {
        const handler = new LogoHandler();
        const rewriter = new HTMLRewriter()
            .on('link[rel="apple-touch-icon"]', handler)
            .on('link[rel="apple-touch-icon-precomposed"]', handler)
            .on('link[rel="icon"]', handler)
            .on('link[rel="shortcut icon"]', handler);
        const res = await fetch(target.toString());
        if (!res.ok) return new Response(null, { status: res.status });

        try {
            const transformed = rewriter.transform(res);
            await transformed.arrayBuffer();
        } catch (e) { }

        // sort by order of preference (higher sizes better if defined)
        handler.logoOptions.sort((a, b) => {
            const aSize = a.sizes?.split("x");
            const aSizeComp = aSize?.length > 0 ? parseInt(aSize[0]) : 0;
            const bSize = b.sizes?.split("x");
            const bSizeComp = bSize?.length > 0 ? parseInt(bSize[0]) : 0;
            if (aSizeComp < bSizeComp) return 1;
            if (aSizeComp > bSizeComp) return -1;
            return 0;
        });

        // add fallbacks as last resort 
        handler.logoOptions.push({ rel: 'manual-apple', href: `${target}/apple-touch-icon.png` });
        handler.logoOptions.push({ rel: 'manual-favicon', href: `${target}/favicon.ico` });

        // loop through options and return first found
        //console.log("OPTIONS", handler.logoOptions);
        for (const option of handler.logoOptions) {
            //console.log("CHECKING OPTION", option);
            const res = await fetch(new URL(option.href, target).toString());
            if (res.ok) {
                //console.log("FOUND OPTION", option);                
                let newResponse = new Response(res.clone().body, res);
                newResponse.headers.set('Cache-Control', `max-age=86400,public`)

                waitUntil(cache.put(cacheKey, newResponse.clone()));
                response = res;
                break;
            }
        }
    }

    return !response ? new Response(null, { status: 404 }) : response;
}
