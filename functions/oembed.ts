const jsonTempalte = `{
	"version": "1.0",
	"type": "video",
	"provider_name": "Switcher Inc.",
	"provider_url": "https://www.switcherstudio.com/",
	"width": 560,
	"height": 315,
    "html": "{html_string}"
}`

const xmlTemplate = `<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<oembed>
	<version>1.0</version>
	<type>video</type>
	<width>560</width>
	<height>315</height>
	<provider_name>Switcher Inc.</provider_name>
	<provider_url>https://www.switcherstudio.com/</provider_url>
	<html>{html_string}</html>
</oembed>`;

const encodeHtml = (markup) => {
    return markup.replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&apos;');
}
const unicodeHtml = (markup) => {
    return markup.replace(/&/g, '\\u0026')
        .replace(/</g, '\\u003c')
        .replace(/>/g, '\\u003e')
        .replace(/"/g, '\\u0022')
        .replace(/'/g, '\\u2019');
}

export const onRequestGet: PagesFunction = async(context) => {
    // Contents of context object
    const {
        request, // same as existing Worker API
        env, // same as existing Worker API
        params, // if filename includes [id] or [[path]]
        waitUntil, // same as ctx.waitUntil in existing Worker API
        next, // used for middleware or to fetch assets
        data, // arbitrary space for passing data between middlewares
    } = context;

    const regex = /\/watch\?([bp])=([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89ab][0-9a-fA-F]{3}-[0-9a-fA-F]{12})/g;
    const { host, searchParams } = new URL(request.url);
    const formatQuery = searchParams.get('format');
    const urlQuery = searchParams.get('url');
    const { pathname, search } = new URL(urlQuery);
    const match = regex.exec(pathname + search);   
    let response = null;
    if(!!match) {
        const playerType = match[1];
        const entityId = match[2];
    
        const htmlMarkup = `<iframe width="560" height="315" allowfullscreen="" frameborder="0" scrolling="auto" src="https://${host}/embed?${playerType}=${entityId}"></iframe>`;
    
        if (formatQuery === 'json') {
            response = new Response(jsonTempalte.replace('{html_string}', unicodeHtml(htmlMarkup)));
            response.headers.set('Content-Type', 'application/json');
        } else {
            response = new Response(xmlTemplate.replace('{html_string}', encodeHtml(htmlMarkup)));
            response.headers.set('Content-Type', 'application/xml');
        }
    } else {
        response = new Response(null, { status: 404 });
    }
    return response;
}
