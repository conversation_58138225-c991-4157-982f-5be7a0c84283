import { RollbarSettings, rollbarError } from "../internal/helpers/rollbar";
import { Env } from "../internal/types/env";

export const onRequest: PagesFunction<Env> = async(context) => {
    // Contents of context object
    const {
        request, // same as existing Worker API
        env, // same as existing Worker API
        params, // if filename includes [id] or [[path]]
        waitUntil, // same as ctx.waitUntil in existing Worker API
        next, // used for middleware or to fetch assets
        data, // arbitrary space for passing data between middlewares
    } = context;
    let cacheDuration = 10;

    const { host, pathname, search } = new URL(request.url);
    const rollbarSettings = {
        environment: host === "player-stage.switcherstudio.com" ? "stage" : (env.REACT_APP_ENV || "local"),
        version: env.REACT_APP_VERSION || "1.0",
        clientKey: env.REACT_APP_ROLLBAR_SERVERKEY
    } as RollbarSettings

    try {
        // Determine API url based on original host...
        // We have to do this as Cloudflare only has 2 environments (production and preview)
        let apiURL = context.env.REACT_APP_PLAYER_API_URL ??  "https://vapi-develop.switcherstudio.com";
        switch (host) {
            case "player.switcherstudio.com":
                apiURL = "https://vapi.switcherstudio.com";
                break;
            case "player-stage.switcherstudio.com":
                apiURL = "https://vapi-stage.switcherstudio.com";
                break;         
        }
        const apiRequestUrl = `${apiURL}${pathname.replace('/api-proxy', '')}${search}`;
        let newRequest = new Request(apiRequestUrl, request);

        // disable cache if authorization is set
        if(Boolean(request.headers.get("Authorization"))) cacheDuration = 0;

        let response;
        await fetch(newRequest, {
            cf: {
                // Always cache this fetch regardless of content type
                // for a max of 10 seconds before revalidating the resource
                cacheTtl: cacheDuration,
                cacheEverything: true,
                //Enterprise only feature, see Cache API for other plans
                cacheKey: apiRequestUrl,
            },
        }).then((serverResponse) => {
            response = new Response(serverResponse.clone().body, serverResponse);
            if (!serverResponse.ok && serverResponse.status !== 304) {
                return Promise.reject(serverResponse);
            }
        }).catch((response) => {
            response.text().then((text: any) => {
                console.log("Failed API Proxy Request", apiRequestUrl, response.status, response.statusText, text);
                rollbarError(rollbarSettings, {
                    error: new Error("Failed API Proxy Request"),
                    description: `${response.statusText} - ${text}`,
                    telemetryType: "network",
                    method: "GET",
                    url: apiRequestUrl,
                    statusCode: response.statusCode
                });
            });
        });

        return response;
    } catch (e) {
        console.log("General Error", e.message);
        rollbarError(rollbarSettings, {
            error: e
        });
        return new Response(e.message, { status: 500 });
    }
}
