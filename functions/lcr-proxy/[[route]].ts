import handlePostRequest from "../internal/helpers/handlePostRequest";
import { RollbarSettings, rollbarError } from "../internal/helpers/rollbar";
import { Env } from "../internal/types/env";

const doNotCache = [
    'LiveCommentsFn_HttpGetCheckout'
]

export const onRequest: PagesFunction<Env> = async(context) => {
    // Contents of context object
    const {
        request, // same as existing Worker API
        env, // same as existing Worker API
        params, // if filename includes [id] or [[path]]
        waitUntil, // same as ctx.waitUntil in existing Worker API
        next, // used for middleware or to fetch assets
        data, // arbitrary space for passing data between middlewares
    } = context;
    const cacheDuration = 10;

    const rollbarSettings = {
        environment: env.REACT_APP_ENV|| "local",
        version: env.REACT_APP_VERSION|| "1.0",
        clientKey: env.REACT_APP_ROLLBAR_SERVERKEY
    } as RollbarSettings

    try {
        const { pathname, search } = new URL(request.url);
        const apiRequestUrl = `${env.REACT_APP_LCR_API_URL}${pathname.replace('/lcr-proxy', '')}${search}`;
        let newRequest = new Request(apiRequestUrl, request);

        if (doNotCache.includes(params.route.at(-1))) return await fetch(newRequest);

        let response;
        if (newRequest.method.toUpperCase() === 'POST') {
            response = handlePostRequest(context, newRequest, cacheDuration);
        } else {
            response = fetch(newRequest, {
                cf: {
                    // Always cache this fetch regardless of content type
                    // for a max of 10 seconds before revalidating the resource
                    cacheTtl: cacheDuration,
                    cacheEverything: true,
                    //Enterprise only feature, see Cache API for other plans
                    cacheKey: apiRequestUrl,
                },
            });
        }

        response.then((serverResponse) => {
            response = new Response(serverResponse.clone().body, serverResponse);
            if (!serverResponse.ok && serverResponse.status != 304) {
                return Promise.reject(serverResponse);
            }
        }).catch((response) => {
            response.text().then((text: any) => {
                console.log("Failed LCR Proxy Request", apiRequestUrl, response.status, response.statusText, text);
                rollbarError(rollbarSettings, {
                    error: new Error("Failed LCR Proxy Request"),
                    description: `${response.statusText} - ${text}`,
                    telemetryType: "network",
                    method: newRequest.method.toUpperCase(),
                    url: apiRequestUrl,
                    statusCode: response.statusCode
                });
            });
        });

        return response;
    } catch (e) {
        console.log("General Error", e.message);
        rollbarError(rollbarSettings, {
            error: e
        });
        return new Response(e.message, { status: 500 });
    }
}
