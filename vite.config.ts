import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import viteTsconfigPaths from "vite-tsconfig-paths";
import svgr from "vite-plugin-svgr";
import eslint from "vite-plugin-eslint";
import { splitVendorChunkPlugin } from "vite";

function manualChunks(id: string, { getModuleInfo }) {
  if (id.includes(".svg")) {
    return "icons";
  }

  if (
    id.includes("react") ||
    id.includes("react-dom") ||
    id.includes("use-sync-external-store")
  ) {
    return "@react";
  }

  if (id.includes("react-router") || id.includes("react-router-dom") || id.includes("remix-run")) {
    return "@react-router";
  }

  if (id.includes("@switcherstudio")) {
    return "@switcherstudio-client";
  }

  if (id.includes("shaka-player")) {
    return "@shaka-player";
  }

  if (id.includes("hls")) {
    return "@hls";
  }

  const match = /.*\.strings\.(\w+)\.js/.exec(id);
  if (match) {
    const language = match[1]; // e.g. "en"
    const dependentEntryPoints: any[] = [];

    // we use a Set here so we handle each module at most once. This
    // prevents infinite loops in case of circular dependencies
    const idsToHandle = new Set(getModuleInfo(id).dynamicImporters);

    for (const moduleId of idsToHandle) {
      const { isEntry, dynamicImporters, importers } = getModuleInfo(moduleId);
      if (isEntry || dynamicImporters.length > 0)
        dependentEntryPoints.push(moduleId);

      // The Set iterator is intelligent enough to iterate over
      // elements that are added during iteration
      for (const importerId of importers) idsToHandle.add(importerId);
    }

    // If there is a unique entry, we put it into a chunk based on the
    // entry name
    if (dependentEntryPoints.length === 1) {
      return `${
        dependentEntryPoints[0].split("/").slice(-1)[0].split(".")[0]
      }.strings.${language}`;
    }
    // For multiple entries, we put it into a "shared" chunk
    if (dependentEntryPoints.length > 1) {
      return `shared.strings.${language}`;
    }
  }
}

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    viteTsconfigPaths(),
    svgr(),
    eslint({
      cache: false,
      include: ['./src/**/*.js', './src/**/*.jsx', './src/**/*.ts', './src/**/*.tsx'],
      exclude: ['/virtual:/**', 'node_modules/**']
    }),
    splitVendorChunkPlugin(),
  ],
  build: {
    outDir: "build",
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks,
      },
    },
  },
  resolve: {
    alias: {
      // fixes vite hls.js issue when running dev server --> https://github.com/video-dev/hls.js/issues/5146#issuecomment-1445155717
      "hls.js": "hls.js/dist/hls.min.js",
    },
  },
  server: {
    open: true,
    port: 3004,
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: '@import "./src/_utils.scss";',
      },
    },
  },
});
