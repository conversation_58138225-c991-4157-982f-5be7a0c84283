{"parser": "@typescript-eslint/parser", "extends": ["plugin:react/recommended", "plugin:react-hooks/recommended", "plugin:@typescript-eslint/recommended"], "plugins": ["react", "react-hooks", "@typescript-eslint", "import"], "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "rules": {"react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn", "@typescript-eslint/no-explicit-any": "off", "react/react-in-jsx-scope": "off", "@typescript-eslint/no-unused-vars": "warn", "@typescript-eslint/no-var-requires": "warn", "@typescript-eslint/ban-types": "warn", "@typescript-eslint/ban-ts-comment": "warn", "@typescript-eslint/no-non-null-assertion": "off", "prefer-const": "off", "no-var": "warn", "prefer-rest-params": "off", "react/prop-types": "off", "react/no-children-prop": "warn", "react/jsx-key": "warn", "react/no-unescaped-entities": "warn", "react/no-unknown-property": "warn", "react/display-name": "warn", "import/no-cycle": [2, {"maxDepth": "∞"}]}, "settings": {"react": {"version": "detect"}}}