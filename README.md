# Embedded Switcher Player

## About

This project contains the code for the embeddable Switcher Player and Switcher Player microsite.

## System Dependencies

To run this project, you should have the [latest LTS version of `node` installed](https://nodejs.org/en/download).

`yarn` is used as the package manager for this project3, so if it is not installed on your machine, install it [here](https://classic.yarnpkg.com/lang/en/docs/install).

Although not a requirement, [VS Code](https://code.visualstudio.com/download) is our preferred text editor. If you are using VS Code, install recommended packages for the project.

---

**Otherwise there are a couple more steps to setup the project:**

### Package Authentication for Local Development

There are packages referenced that are hosted on the GitHub Packages registry. If you have setup authentication for the Dashboard, you do not need to do it again for this project. To authorize to the registry follow these steps:

1. Generate a Personal Access Token in GitHub: <https://github.com/settings/tokens>. The token needs to at least include the read:packages permission.
2. Update or add a new .npmrc file with the following contents. The following snippet is two separate lines:

```sh
  @switcherstudio:registry=https://npm.pkg.github.com
  //npm.pkg.github.com/:_authToken={{YOUR_TOKEN}}
```

---

### Environment Variables for Local Development

Make a copy of the`.env.local.template` file and rename the copy to `.env.local`. Replace `<first-name>` in the URLs variable with your first name (ex. `https://dashboard-project-isaac.switcherstudio.com`) to use the tunnel urls. Note that cloudflare tunnels needs to be set up on your machine to use these tunnels.

---

To install dependencies, run `yarn` and get some coffee/tea/water.

---

## How To Debug

To run the dashboard locally, run `yarn start` - this will start a development server on `localhost:3004`

Then, to open a debugger, go to the `Run and Debug` tab of VS Code and press the launch button or open you command pallette (shift+ctrl+P) and type `Debug: Start Debugging. You can select either to launch against Chrome or Firefox.

## Available Scripts

In the project directory, you can run:

### `npm start`

Runs the app in the development mode.\
Open [http://localhost:3004](http://localhost:3004) to view it in the browser.

The page will reload if you make edits.\
You will also see any lint errors in the console.

### `npm embed`

Creates a server that hosts a web page that contains the player embedded in an iframe.

### `npm run cf`

Requires the [Wrangler CLI](https://developers.cloudflare.com/workers/wrangler/install-and-update/) to be installed globally. Runs wrangler for local cloudflare functions development. Note that while this will allow for live updates to the functions, any changes in the react project itself this script needs to be run again for those changes to be applied.

### `npm run build`

Builds the app for production to the `build` folder.\
It correctly bundles React in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.\
Your app is ready to be deployed!

See the section about [deployment](https://facebook.github.io/create-react-app/docs/deployment) for more information.

### `npm test`

Launches the test runner in the interactive watch mode.\
See the section about [running tests](https://facebook.github.io/create-react-app/docs/running-tests) for more information.

### `npm build-ts`

Compiles and builds for production build.

### `npm build-ts-dev`

Compiles and builds for develop build. Contains source maps.

### `npm run listen-to-stripe`

Listens to Stripe events and forwards them to the local Stripe webhook endpoint. To use this you will need the Stripe CLI installed and configured. This is needed to test purchasing locally.

### Running Embedded Player Locally

In `embed/index.html`, change `data-videoplayerid` to equal the video player id you want to target.

Run `npm start` then `npm embed`

While this is an option, most team members use [Glitch](https://glitch.com/) to build a simple website that embeds the player.

## Testing

Automated testing is not currently implemented.

### Deployment To Dev

Deployment to the development environment is triggered two ways.

**Method #1**: The [Push to Develop](https://github.com/switcherstudio/embedded-video-player/actions/workflows/push-to-develop.yml) workflow. This workflow is triggered anytime a feature branch is merged into develop, or when develop is pushed to directly. **Avoid pushing directly to develop**. This workflow does two things:

-   Increments the build number in develop.
-   Builds and deploys the application to the development environment.

**Method #2**: The [Manual Deploy (dev)](https://github.com/switcherstudio/embedded-video-player/actions/workflows/manual-deploy-dev.yml) workflow. The default inputs should almost always be used. This workflow should only be triggered if "Push to Develop" fails for some reason.

### Deployment To Production

Deployment to production is also triggered two ways.

**Method #1**: Creating a release candidate, and merging to master. This involves running two workflows:

1. First, run the [Create a Release Candidate](https://github.com/switcherstudio/embedded-video-player/actions/workflows/create-rc.yml) workflow manually, supplying the desired version number in the `version` input. Running this workflow will:
    - Bump the version in develop and create a tag using the provided `version`.
    - Create a release candidate branch (`rc-x.x.x`) off of develop, and open a PR to master from the new branch.
2. Second, when ready, merge the release candidate PR into master. This will trigger the [Push to Master](https://github.com/switcherstudio/embedded-video-player/actions/workflows/push-to-master.yml) workflow, which will:
    - Build and deploy the app to the production environment.
    - Draft and create a release from the most recent tag in master.

**Method #2**: The [Manual Deploy by Tag (prod)](https://github.com/switcherstudio/embedded-video-player/actions/workflows/manual-deploy-prod.yml) workflow. Supply the desired tag number (`x.x.x`) in the `tag` input. The workflow will checkout the repository with the given tag, and build and deploy the app to production. This method is useful for deploying previous versions of the application to production.

## Events Dispatched in embed.ts

This section describes the custom events dispatched in the `embed.ts` file to facilitate communication and interaction within the application.

### List of Events

1. **switcherPlayerAppLoaded**

    - **Description**: Dispatched when the player is loaded.

2. **switcherPlayerDialogOpened**

    - **Description**: Dispatched when the main dialog is opened.

3. **switcherPlayerDialogClosed**

    - **Description**: Dispatched when the main or auxiliary dialog is closed.

### Listening to Events

You can listen to these events using standard event listeners in JavaScript:

```javascript
document.addEventListener("switcherPlayerDialogOpened", () => {
    console.log("Main dialog opened");
});

document.addEventListener("switcherPlayerDialogClosed", () => {
    console.log("Dialog closed");
});
```
