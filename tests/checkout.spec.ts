import { test, expect, Page, FrameLocator } from "@playwright/test";

const CATALOG = "97377210-b55f-43a2-aab8-5215270c3c3b";
const PLAYER_WITH_PASS = "76f3a982-c1c5-4d1a-8bac-05d783e85a24";
const PLAYER_WITHOUT_PASS_WITH_GATED = "afc90439-1208-4599-b85c-2de3bb960b25";

test.beforeEach(async ({ page }) => {});

test.describe("Checkout", () => {
    test("Can checkout catalog", async ({ page }) => {
        await page.goto(`/watch?c=${CATALOG}`);

        const frame = page.frameLocator("iframe").first();
        const dialogFrame = page.frameLocator("iframe").nth(1);

        await frame.getByRole("button", { name: "SUBSCRIBE" }).click();

        await dialogFrame
            .getByLabel("Your Email")
            .fill(`engagementtesting+${Date.now()}@switcherstudio.com`);

        await dialogFrame.getByRole("button", { name: "$10/MONTH", exact: true }).click();

        await stripeCheckout(dialogFrame);

        expect(!frame.getByRole("button", { name: "SUBSCRIBE" }));
    });

    test("Can can checkout subscription", async ({ page }) => {
        await page.goto(`/watch?p=${PLAYER_WITH_PASS}`);

        const frame = page.frameLocator("iframe").first();

        expectGated(page);

        await frame.getByRole("button", { name: /SUBSCRIBE FOR/ }).click();

        await frame
            .getByLabel("Your Email")
            .fill(`engagementtesting+${Date.now()}@switcherstudio.com`);

        await frame.getByRole("button", { name: "$700/YEAR", exact: true }).click();

        await stripeCheckout(frame);

        expectUngated(page);
    });

    test("Can checkout one-time pass", async ({ page }) => {
        await page.goto(`/watch?p=${PLAYER_WITHOUT_PASS_WITH_GATED}`);
        const frame = page.frameLocator("iframe").first();

        expectGated(page);

        await frame.getByRole("button", { name: /^PURCHASE OPTIONS/ }).click();

        await frame
            .getByLabel("Your Email")
            .fill(`engagementtesting+${Date.now()}@switcherstudio.com`);

        await frame.getByRole("button", { name: "BUY FOR $7", exact: true }).click();

        await stripeCheckout(frame);

        expectUngated(page);
    });
});

const stripeCheckout = async (frame: FrameLocator) => {
    const checkoutFrame = frame.frameLocator('iframe[name="embedded-checkout"]').first();

    await checkoutFrame.getByPlaceholder("1234 1234 1234 1234").fill("4242 4242 4242 4242");
    await checkoutFrame.getByPlaceholder("MM / YY").fill("02/28");
    await checkoutFrame.getByPlaceholder("CVC").fill("424");
    await checkoutFrame.getByPlaceholder("Full name on card").fill("Devvy McDev");
    await checkoutFrame.getByLabel("Country or region").selectOption("United States");
    await checkoutFrame.getByPlaceholder("ZIP").fill("42424");
    await checkoutFrame.getByLabel(/Save my info/).uncheck();

    await checkoutFrame.getByTestId("hosted-payment-submit-button").click();

    await frame.getByRole("button", { name: "Continue" }).click();
};

const expectGated = (page: Page) => {
    expect(page.getByText("Buy to watchThis content requires a purchase to view."));
};

const expectUngated = (page: Page) => {
    expect(!page.getByText("Buy to watchThis content requires a purchase to view."));
};
